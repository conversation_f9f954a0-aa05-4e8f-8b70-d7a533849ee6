module "rds_reserva_emissao" {
  source                = "../../../blueprints/rds-postgres"
  profile               = "hml" 
  region                = "us-east-1"
  application_name      = "reserva-emissao"
  environment_name      = ["developer", "staging"]
  allowed_cidr_blocks   = [ "172.16.0.0/16", "172.18.0.0/16", "172.29.0.0/16", "192.168.224.0/24", "10.20.0.0/16" ]
  vpc_id                = "vpc-097093d2681a97b1d"
  subnet_group          = "default-vpc-097093d2681a97b1d"
  security_group_name   = "app-rds-security-group-stg-vpc" 
  instance_size         = "db.t4g.micro"
  pg_version            = "17.5"
  dns_developer         = true
}


# Necessário criar um secret a parte do blueprint porque este rds é compartilhado por dev e staging, então apenas um role é 
# criado com acesso aos dois db
resource "aws_secretsmanager_secret" "rds_reserva_emissao_secret" {
  name        = "developer-reserva-emissao-app"
  description = "password for app usage"
}
