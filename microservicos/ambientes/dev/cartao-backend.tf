module "cartao_backend" {
  source           = "../../modules/service"
  environment_name = local.environment_name
  app_docker_image = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/cartao-backend:${local.environment_name}"
  environment_variables = concat(local.quarkus_default_env, [
    {
      name  = "QUARKUS_DATASOURCE_USERNAME",
      value = "postgres"
    },
    {
      name  = "QUARKUS_DATASOURCE_PASSWORD",
      value = "HuCrlR0apVYDWPGaefbq"
    },
    {
      name  = "QUARKUS_DATASOURCE_JDBC_URL",
      value = "*************************************************************************************************"
    },
    {
      name  = "QUARKUS_DATASOURCE_REACTIVE_URL",
      value = "postgresql://cartao-backend-dev.cs94xnd3ogad.us-east-1.rds.amazonaws.com:5432/cartao_backend"
    },
    {
      name  = "QUARKUS_REDIS_HOSTS",
      value = "redis://developer-cache-redis-001.5ps2ds.0001.use1.cache.amazonaws.com:6379"
    },
    {
      name  = "INTEGRADOR_SWAP_URL",
      value = "http://gateway.developer.paytrack.local/integrador-swap/"
    },
    {
      name  = "KAFKA_GROUP_ID",
      value = "developer-cartao"
    },
    {
      name  = "TRADUTOR_URL",
      value = "https://gama.paytrack.com.br/tradutor"
    },
    {
      name  = "PAYTRACK_ENV_URL",
      value = "https://gama.developer.paytrack.com.br"
    },
    {
     name  = "AUTENTICADOR_ENV_URL",
     value = "http://auth-internal.developer.paytrack.com.br/autenticador/api"
    },
    {
      name  = "PAYTRACK_KAFKA_CONSUMER_DISPOSITIVO-CONFIAVEL_APROVACAO_TOPIC",
      value = "developer-configuracoes.dispositivo-confiavel.aprovacao"
    }
  ])
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/developer/kafka/paytrack-kafka/broker/url/plaintext"
    },
    {
      name      = "TRADUTOR_USER",
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/developer/paytrack-tradutor/backdoor/user"
    },
    {
      name      = "TRADUTOR_PASSWORD",
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/developer/paytrack-tradutor/backdoor/password"
    },
    {
      name      = "PAYTRACK_ENV_BACKDOOR_USER",
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/developer/paytrack/backdoor/user"
    },
    {
      name      = "PAYTRACK_ENV_BACKDOOR_PASSWORD",
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/developer/paytrack/backdoor/password"
    }
  ]
  service_name = "cartao-backend"

  public       = true
  target_arn   = module.api_gateway.target_group_arn
  listener_arn = module.api_gateway.listener_arn

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  gateway = {
    route              = "/cartao-backend"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night     = false
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  docker_labels         = local.docker_labels
  specifications        = local.medium
}