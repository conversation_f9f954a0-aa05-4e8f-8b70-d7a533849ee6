resource "aws_sqs_queue" "sending_email_requested" {
  name                        = "sending-email-requested-developer"
  delay_seconds               = 0
  max_message_size            = 262144
  message_retention_seconds   = 1209600  # 14 days
  receive_wait_time_seconds   = 0
  visibility_timeout_seconds  = 30
  
  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.sending_email_requested_dlq.arn
    maxReceiveCount     = 3
  })

  tags = {
    Environment = "developer"
    Service     = "notify"
    Type        = "main-queue"
  }
}

resource "aws_sqs_queue" "sending_email_requested_dlq" {
  name                        = "sending-email-requested-dlq-developer"
  delay_seconds               = 0
  max_message_size            = 262144
  message_retention_seconds   = 1209600  # 14 days
  receive_wait_time_seconds   = 0
  visibility_timeout_seconds  = 30

  tags = {
    Environment = "developer"
    Service     = "notify"
    Type        = "dlq"
  }
}

resource "aws_sqs_queue" "sending_push_requested" {
  name                        = "sending-push-requested-developer"
  delay_seconds               = 0
  max_message_size            = 262144
  message_retention_seconds   = 1209600  # 14 days
  receive_wait_time_seconds   = 0
  visibility_timeout_seconds  = 30
  
  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.sending_push_requested_dlq.arn
    maxReceiveCount     = 3
  })

  tags = {
    Environment = "developer"
    Service     = "notify"
    Type        = "main-queue"
  }
}

resource "aws_sqs_queue" "sending_push_requested_dlq" {
  name                        = "sending-push-requested-dlq-developer"
  delay_seconds               = 0
  max_message_size            = 262144
  message_retention_seconds   = 1209600  # 14 days
  receive_wait_time_seconds   = 0
  visibility_timeout_seconds  = 30

  tags = {
    Environment = "developer"
    Service     = "notify"
    Type        = "dlq"
  }
}

resource "aws_secretsmanager_secret" "firebase_credentials" {
  name        = "developer-firebase-notify-credentials"
  description = "Firebase service account credentials for notify service in developer environment"
  
  tags = {
    Environment = "developer"
    Service     = "notify"
    Type        = "firebase-credentials"
  }
}

module "rds_notify" { #por enquanto, será usado para dev e staging por economia
  create_rds_instance   = true
  source                = "../../../blueprints/rds-postgres"
  profile               = "hml" #profile do aws cli
  region                = "us-east-1"
  application_name      = "notify"
  environment_name      = "developer"
  allowed_cidr_blocks   = [ "**********/16", "**********/16", "*************/24", "*********/16" ]
  vpc_id                = "vpc-00648d5a38c32ba60"
  subnet_group          = "default-vpc-00648d5a38c32ba60"
  instance_size         = "db.t4g.micro"
  pg_version            = "17.4"
  dns_developer         = true
}

#developer
#vpc_id              = " vpc-00648d5a38c32ba60"
#subnet_group        = "default-vpc-00648d5a38c32ba60"
#cidr **********/16
#staging
#subnet_group           = "default-vpc-097093d2681a97b1d"
#vpc_id                 = "vpc-097093d2681a97b1d"
#cidr 	172.16.0.0/16
