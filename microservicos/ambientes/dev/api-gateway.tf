module "api_gateway" {
  source            = "../../modules/api_gateway"
  environment_name  = local.environment_name
  certificate_arn   = module.route53.certificate_arn
  route53_zone_id   = module.route53.public_hosted_zone_id
  subnets           = module.infra.public_subnets
  security_groups   = [module.infra.security_group_id]
  vpc_id            = module.infra.vpc_id
  private_subnets   = module.infra.private_subnets
  ecs_cluster       = module.infra.ecs_cluster
  private_dns_id    = module.infra.private_dns_id
  shutdown_at_night = false
  paytrack_urls = [
    "https://alpha.paytrack.com.br",
    "https://charlie.paytrack.com.br",
    "https://delta.paytrack.com.br",
    "https://gama.paytrack.com.br",
    "https://juliet.paytrack.com.br",
    "https://romeo.paytrack.com.br",
    "https://tango.paytrack.com.br",
    "https://zulu.paytrack.com.br",
    "https://supplier-alpha.paytrack.com.br",
    "https://supplier-charlie.paytrack.com.br",
    "https://supplier-delta.paytrack.com.br",
    "https://supplier-gama.paytrack.com.br",
    "https://supplier-juliet.paytrack.com.br",
    "https://supplier-papa.paytrack.com.br",
    "https://supplier-romeo.paytrack.com.br",
    "https://supplier-tango.paytrack.com.br",
    "https://supplier-xray.paytrack.com.br",
    "https://supplier-zulu.paytrack.com.br",
    "https://clientes.developer.paytrack.com.br",
    "https://alpha.developer.paytrack.com.br",
    "https://charlie.developer.paytrack.com.br",
    "https://delta.developer.paytrack.com.br",
    "https://gama.developer.paytrack.com.br",
    "https://juliet.developer.paytrack.com.br",
    "https://papa.developer.paytrack.com.br",
    "https://romeo.developer.paytrack.com.br",
    "https://tango.developer.paytrack.com.br",
    "https://xray.developer.paytrack.com.br",
    "https://zulu.developer.paytrack.com.br",
    "https://echo.developer.paytrack.com.br",
    "https://alpha-web.developer.paytrack.com.br",
    "https://charlie-web.developer.paytrack.com.br",
    "https://delta-web.developer.paytrack.com.br",
    "https://gama-web.developer.paytrack.com.br",
    "https://juliet-web.developer.paytrack.com.br",
    "https://papa-web.developer.paytrack.com.br",
    "https://romeo-web.developer.paytrack.com.br",
    "https://tango-web.developer.paytrack.com.br",
    "https://xray-web.developer.paytrack.com.br",
    "https://zulu-web.developer.paytrack.com.br",
    "https://echo-web.developer.paytrack.com.br",
  ]
  iam_roles = {
    execution_arn = module.infra.task_execution_iam_role
    task_arn      = module.infra.task_iam_role
  }
  mesh_name = module.infra.mesh_id
}
