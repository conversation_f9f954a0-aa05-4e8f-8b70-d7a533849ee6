terraform {
  backend "s3" {
    bucket  = "terraform-iac-v2"
    key     = "microservice/environments/ecr-dev/applications.tfstate"
    region  = "us-east-1"
    profile = "main_account"
  }

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 4.0"
    }
  }

  required_version = "~> 1.1"
}

module "ecr_collector_proxy" {
  source       = "./module"
  service_name = "collector-proxy"
}

module "ecr_integrador_reservafacil" {
  source       = "./module"
  service_name = "integrador-reservafacil"
}

module "ecr_integrador_sabre" {
  source       = "./module"
  service_name = "integrador-sabre"
}

module "ecr_monitor_backend" {
  source       = "./module"
  service_name = "monitor-backend"
}

module "ecr_monitor_frontend" {
  source       = "./module"
  service_name = "monitor-frontend"
}

module "ecr_mysql_testes" {
  source       = "./module"
  service_name = "mysql-testes"
}

module "ecr_paytrack_autenticador" {
  source       = "./module"
  service_name = "paytrack-autenticador"
}

module "ecr_paytrack_tradutor" {
  source       = "./module"
  service_name = "paytrack-tradutor"
}

module "ecr_paytrack_web" {
  source       = "./module"
  service_name = "paytrack-web"
}

module "ecr_pipeline_aws_jdk_17" {
  source       = "./module"
  service_name = "pipeline-aws-jdk-17"
}

module "ecr_pipeline_docker_aws" {
  source       = "./module"
  service_name = "pipeline-docker-aws"
}
