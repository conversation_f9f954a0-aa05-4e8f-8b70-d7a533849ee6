#!/usr/bin/env bash

export AWS_PROFILE="dev"  # ← ✅ Set the AWS profile for Terraform to use

TF_RESOURCE_NAME="ecr_repo"

REPOS=(
  "paytrack/collector-proxy"
  "paytrack/integrador-reservafacil"
  "paytrack/integrador-sabre"
  "paytrack/monitor-backend"
  "paytrack/monitor-frontend"
  "paytrack/mysql-testes"
  "paytrack/paytrack-autenticador"
  "paytrack/paytrack-tradutor"
  "paytrack/paytrack-web"
  "paytrack/pipeline-aws-jdk-17"
  "paytrack/pipeline-docker-aws"
)

for repo in "${REPOS[@]}"; do
  # Turn repo name into Terraform-safe module name
  module_suffix=$(echo "$repo" | sed -e 's|^paytrack/||' -e 's|[^a-zA-Z0-9]|_|g')

  # Address in Terraform state
  tf_address="module.ecr_${module_suffix}.aws_ecr_repository.${TF_RESOURCE_NAME}"

  # Check if already imported
  if terraform state list 2>/dev/null | grep -q "^${tf_address}$"; then
    echo "✅ Already in state: $tf_address"
    continue
  fi

  echo "📦 Importing $repo → $tf_address"
  terraform import "$tf_address" "$repo"
done

echo "✅ All ECR repos imported into Terraform state."
