variable "profile" {
  type = string
  default = "dev"
  description = "Nome do profile onde os recursos serão criados"
}

provider "aws" {
  profile = var.profile
}

variable "service_name" {
  type = string
}

resource "aws_ecr_repository" "ecr_repo" {
  name                 = "paytrack/${var.service_name}"
  image_tag_mutability = "MUTABLE"
}

# resource "aws_ecr_repository_policy" "ecr_repo_policy" {
#   depends_on = [
#     aws_ecr_repository.ecr_repo
#   ]
#   repository = aws_ecr_repository.ecr_repo.name

#   policy = <<EOF
# {
#   "Version": "2012-10-17",
#   "Statement": [
#     {
#       "Sid": "DevAccount",
#       "Effect": "Allow",
#       "Principal": {
#         "AWS": [
#           "arn:aws:iam::************:root",
#           "arn:aws:iam::************:root"
#         ]
#       },
#       "Action": [
#         "ecr:BatchCheckLayerAvailability",
#         "ecr:BatchGetImage",
#         "ecr:GetDownloadUrlForLayer"
#       ]
#     }
#   ]
# }
# EOF
# }

resource "aws_ecr_lifecycle_policy" "ecr_lifecycle_policy" {
  depends_on = [
    aws_ecr_repository.ecr_repo
  ]
  repository = aws_ecr_repository.ecr_repo.name

  policy = <<EOF
{
  "rules": [
    {
      "rulePriority": 1,
      "description": "Keep only the last 10 images",
      "selection": {
        "tagStatus": "any",
        "countType": "imageCountMoreThan",
        "countNumber": 10
      },
      "action": {
        "type": "expire"
      }
    }
  ]
}
EOF
}