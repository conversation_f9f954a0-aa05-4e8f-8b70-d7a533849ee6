module "rds_reserva_emissao" {
  source              = "../../../blueprints/rds-postgres"
  profile             = "prod" #profile do aws cli
  region              = "us-east-1"
  application_name    = "reserva-emissao"
  environment_name    = "prod"
  allowed_cidr_blocks = ["172.20.0.0/16", "172.17.0.0/16", "172.29.0.0/16", "192.168.224.0/24", "10.0.0.0/8"]
  vpc_id              = "vpc-06d3dac02535f5752" #vpc prod (ecs)
  subnet_group        = "default-vpc-06d3dac02535f5752"
  security_group_name = "app-rds-security-group-prod-vpc"
  instance_size       = "db.t4g.micro"
  pg_version          = "17.5"
  dns_prod            = true
}

#developer
#vpc_id              = " vpc-00648d5a38c32ba60"
#subnet_group        = "default-vpc-00648d5a38c32ba60"
#cidr 172.18.0.0/16
#staging
#subnet_group           = "default-vpc-097093d2681a97b1d"
#vpc_id                 = "vpc-097093d2681a97b1d"
#cidr 	172.16.0.0/16
