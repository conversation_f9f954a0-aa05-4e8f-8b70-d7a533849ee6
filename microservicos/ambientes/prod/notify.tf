resource "aws_secretsmanager_secret" "firebase_credentials" {
  name        = "prod-firebase-notify-credentials"
  description = "Firebase service account credentials for notify service in prod environment"

  tags = {
    Environment = "prod"
    Service     = "notify"
    Type        = "firebase-credentials"
  }
}

resource "aws_sqs_queue" "sending_email_requested" {
  name                       = "sending-email-requested-prod"
  delay_seconds              = 0
  max_message_size           = 262144
  message_retention_seconds  = 1209600 # 14 days
  receive_wait_time_seconds  = 0
  visibility_timeout_seconds = 30

  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.sending_email_requested_dlq.arn
    maxReceiveCount     = 3
  })

  tags = {
    Environment = "prod"
    Service     = "notify"
    Type        = "main-queue"
  }
}

resource "aws_sqs_queue" "sending_email_requested_dlq" {
  name                       = "sending-email-requested-dlq-prod"
  delay_seconds              = 0
  max_message_size           = 262144
  message_retention_seconds  = 1209600 # 14 days
  receive_wait_time_seconds  = 0
  visibility_timeout_seconds = 30

  tags = {
    Environment = "prod"
    Service     = "notify"
    Type        = "dlq"
  }
}

resource "aws_sqs_queue" "sending_push_requested" {
  name                       = "sending-push-requested-prod"
  delay_seconds              = 0
  max_message_size           = 262144
  message_retention_seconds  = 1209600 # 14 days
  receive_wait_time_seconds  = 0
  visibility_timeout_seconds = 30

  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.sending_push_requested_dlq.arn
    maxReceiveCount     = 3
  })

  tags = {
    Environment = "prod"
    Service     = "notify"
    Type        = "main-queue"
  }
}

resource "aws_sqs_queue" "sending_push_requested_dlq" {
  name                       = "sending-push-requested-dlq-prod"
  delay_seconds              = 0
  max_message_size           = 262144
  message_retention_seconds  = 1209600 # 14 days
  receive_wait_time_seconds  = 0
  visibility_timeout_seconds = 30

  tags = {
    Environment = "prod"
    Service     = "notify"
    Type        = "dlq"
  }
}

module "rds_notify" {
  source              = "../../../blueprints/rds-postgres"
  profile             = "prod" #profile do aws cli
  region              = "us-east-1"
  application_name    = "notify"
  environment_name    = "prod"
  allowed_cidr_blocks = ["172.20.0.0/16", "172.29.0.0/16", "192.168.224.0/24"]
  vpc_id              = "vpc-06c7423cc4349d603" #vpc do EKS
  subnet_group        = "rds-prod-eks-vpc"      #subnets dedicadas, isoladas, rtb própria, 3 AZs, para produção
  instance_size       = "db.t4g.micro"
  pg_version          = "17.4"
  dns_prod            = true
}

#developer
#vpc_id              = " vpc-00648d5a38c32ba60"
#subnet_group        = "default-vpc-00648d5a38c32ba60"
#cidr 172.18.0.0/16
#staging
#subnet_group           = "default-vpc-097093d2681a97b1d"
#vpc_id                 = "vpc-097093d2681a97b1d"
#cidr 	172.16.0.0/16