locals {
  environment_name = "prod"
  default_env = [
    {
      name  = "PAYTRACK_AMBIENTE",
      value = local.environment_name
    },
    {
      name  = "AWS_REGION",
      value = "us-east-1"
    }
  ]
  quarkus_default_env = concat(local.default_env, [
    {
      name  = "QUARKUS_PROFILE",
      value = local.environment_name
    },
    {
      name  = "QUARKUS_LOG_HANDLER_GELF_ENABLED"
      value = "true"
    },
    {
      name  = "QUARKUS_LOG_HANDLER_GELF_HOST"
      value = "tcp:internal-loadbalancer.paytrack.com.br"
    },
    {
      name  = "QUARKUS_LOG_HANDLER_GELF_PORT"
      value = "12201"
    },
    {
      name  = "QUARKUS_LOG_HANDLER_GELF_INCLUDE_FULL_MDC"
      value = "true"
    },
    {
      name  = "QUARKUS_LOG_HANDLER_GELF_TIMESTAMP_PATTERN"
      value = "dd MMM yyyy HH:mm:ss,SSS"
    },
    {
      name  = "QUARKUS_OPENTELEMETRY_ENABLED",
      value = "true"
    },
    {
      name  = "QUARKUS_OPENTELEMETRY_TRACER_EXPORTER_OTLP_ENDPOINT",
      value = "http://data-prepper.paytrack.com.br:80"
    },
    {
      name  = "QUARKUS_OTEL_EXPORTER_OTLP_TRACES_ENDPOINT",
      value = "http://data-prepper.paytrack.com.br:80"
    }
  ])
  spring_default_env = concat(local.default_env, [
    {
      name  = "SPRING_PROFILES_ACTIVE",
      value = "${local.environment_name},logstash"
    }
  ])
  small = {
    cpu    = 512
    memory = 1024
  }
  medium = {
    cpu    = 1024
    memory = 2048
  }
  large = {
    cpu    = 2048
    memory = 4096
  }
  auto_scaling_default_capacity = {
    min = 2,
    max = 4
  }
  auto_scaling_min_capacity = {
    min = 1,
    max = 4
  }

  health_check_props_default = {
    interval    = 10,
    timeout     = 10,
    retries     = 6,
    startPeriod = 60
  }

  docker_labels = {
    "PROMETHEUS_EXPORTER_PATH" : "/q/metrics",
    "PROMETHEUS_EXPORTER_PORT" : "8080"
  }

}

terraform {
  backend "s3" {
    bucket  = "terraform-iac-v2"
    key     = "microservice/environments/prod/applications.tfstate"
    region  = "us-east-1"
    profile = "main_account"
  }

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }

  required_version = "~> 1.1"
}

provider "aws" {
  default_tags {
    tags = {
      Name     = local.environment_name
      Ambiente = local.environment_name
    }
  }
}

provider "aws" {
  alias   = "main_account"
  profile = "main_account"
  default_tags {
    tags = {
      Name     = local.environment_name
      Ambiente = local.environment_name
    }
  }
}

module "infra" {
  source               = "../../modules/infra"
  environment_name     = local.environment_name
  vpc_cidr             = "**********/16"
  private_subnet1_cidr = "***********/19"
  private_subnet2_cidr = "***********/19"
  public_subnet1_cidr  = "**********/19"
  public_subnet2_cidr  = "***********/19"
}

module "vpc_peering" {
  source           = "../../modules/vpc_peering"
  environment_name = local.environment_name
  origin_data = {
    hosted_zone_id = module.infra.hosted_zone_id
    vpc_id         = module.infra.vpc_id
  }
  target_data = {
    vpc_id = "vpc-289f564e"
  }
}

data "aws_route53_zone" "public_hosted_zone" {
  provider = aws.main_account
  name     = "paytrack.com.br"
}

data "aws_acm_certificate" "certificate" {
  domain = "*.paytrack.com.br"
}

module "collector-proxy" {
  source           = "../../modules/collector-proxy"
  environment_name = local.environment_name
}

module "api_gateway" {
  source           = "../../modules/api_gateway"
  environment_name = local.environment_name
  certificate_arn  = data.aws_acm_certificate.certificate.arn
  route53_zone_id  = data.aws_route53_zone.public_hosted_zone.zone_id
  subnets          = module.infra.public_subnets
  security_groups  = [module.infra.security_group_id]
  vpc_id           = module.infra.vpc_id
  private_subnets  = module.infra.private_subnets
  ecs_cluster      = module.infra.ecs_cluster
  private_dns_id   = module.infra.private_dns_id
  paytrack_urls = [
    "https://app.paytrack.com.br",
    "https://financeflow.paytrack.com.br",
    "https://supplier.paytrack.com.br",
  ]
  iam_roles = {
    execution_arn = module.infra.task_execution_iam_role
    task_arn      = module.infra.task_iam_role
  }
  mesh_name         = module.infra.mesh_id
  shutdown_at_night = false
}

data "aws_region" "current" {}

module "integrador_bpp" {
  source           = "../../modules/service"
  environment_name = local.environment_name
  app_docker_image = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/integrador-bpp:${local.environment_name}"
  environment_variables = concat(local.quarkus_default_env, [
    {
      name  = "BPP_PASSWORD_HOM"
      value = "C01isPjc1P5IainS"
    },
    {
      name  = "BPP_SECRET_HOM"
      value = "77c2a94d-6578-45af-8104-7d8a3d956c6b"
    },
    {
      name  = "BPP_PASSWORD_PROD"
      value = "jlDPMwpM5kCNSIbD"
    },
    {
      name  = "BPP_SECRET_PROD"
      value = "57f9fb5b-e59e-4874-a4df-a4db9b938519"
    }
  ])
  service_name = "integrador-bpp"

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  gateway = {
    route              = "/integrador-bpp"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night     = false
  auto_scaling          = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
}

module "integrador_sabre" {
  source                = "../../modules/service"
  environment_name      = local.environment_name
  app_docker_image      = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/integrador-sabre:${local.environment_name}"
  environment_variables = local.quarkus_default_env
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name = "integrador-sabre"

  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props   = local.health_check_props_default

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  specifications           = local.medium
  gateway = {
    route              = "/integrador-sabre"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night     = false
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  public                = false
  docker_labels         = local.docker_labels
}

module "integrador_arbi" {
  source                = "../../modules/service"
  environment_name      = local.environment_name
  app_docker_image      = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/integrador-arbi:${local.environment_name}"
  environment_variables = local.quarkus_default_env
  secrets = [
    {
      name      = "ARBI_USER"
      valueFrom = "arn:aws:secretsmanager:us-east-1:393346304284:secret:prod/arbi-Amgk2V:USER::"
    },
    {
      name      = "ARBI_PASSWORD"
      valueFrom = "arn:aws:secretsmanager:us-east-1:393346304284:secret:prod/arbi-Amgk2V:PASSWORD::"
    },
    {
      name      = "ARBI_CLIENT_ID",
      valueFrom = "arn:aws:secretsmanager:us-east-1:393346304284:secret:prod/arbi-Amgk2V:CLIENT-ID::"
    },
    {
      name      = "ARBI_INSCRICAO_PARCEIRO"
      valueFrom = "arn:aws:secretsmanager:us-east-1:393346304284:secret:prod/arbi-Amgk2V:INSCRICAO-PARCEIRO::"
    },
    {
      name      = "ARBI_TOKEN"
      valueFrom = "arn:aws:secretsmanager:us-east-1:393346304284:secret:prod/arbi-Amgk2V:TOKEN::"
    },
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name             = "integrador-arbi"
  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id

  specifications = {
    cpu    = 256
    memory = 1024
  }

  gateway = {
    route              = "/integrador-arbi"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night     = false
  public                = false
  auto_scaling          = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
}

module "clientes-frontend" {
  source                = "../../modules/frontend"
  environment_name      = local.environment_name
  project_name          = "clientes"
  others_aliases        = ["financeflow.paytrack.com.br"]
  certificate_arn       = data.aws_acm_certificate.certificate.arn
  public_hosted_zone_id = data.aws_route53_zone.public_hosted_zone.zone_id
}

module "clientes-backend" {
  source           = "../../modules/service"
  environment_name = local.environment_name
  app_docker_image = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/clientes-backend:${local.environment_name}"
  environment_variables = concat(local.quarkus_default_env, [
    {
      name  = "FRONTEND_URL",
      value = "https://financeflow.paytrack.com.br"
    },
    {
      name  = "QUARKUS_DATASOURCE_USERNAME",
      value = "postgres"
    },
    {
      name  = "QUARKUS_DATASOURCE_PASSWORD",
      value = "FMhjPKeZkRe1R7BEt1q9"
    },
    {
      name  = "QUARKUS_DATASOURCE_JDBC_URL",
      value = "********************************************************************************************"
    },
    {
      name  = "QUARKUS_DATASOURCE_LEITURA_USERNAME",
      value = "postgres"
    },
    {
      name  = "QUARKUS_DATASOURCE_LEITURA_PASSWORD",
      value = "FMhjPKeZkRe1R7BEt1q9"
    },
    {
      name  = "QUARKUS_DATASOURCE_LEITURA_JDBC_URL",
      value = "********************************************************************************************"
    }
  ])
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/kafka/paytrack-kafka/broker/url/tls"
    },
    {
      name      = "COM_PAYTRACK_USUARIO_AUTENTICACAO",
      valueFrom = "arn:aws:secretsmanager:us-east-1:393346304284:secret:paytrack/backdoor-J7XmFP:usuario::"
    },
    {
      name      = "COM_PAYTRACK_SENHA_AUTENTICACAO",
      valueFrom = "arn:aws:secretsmanager:us-east-1:393346304284:secret:paytrack/backdoor-J7XmFP:senha::"
    },
    {
      name      = "COM_PAYTRACK_TOKEN"
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/com/paytrack/token"
    },
    {
      name      = "COM_PAYTRACK_TOKEN_INTEGRACOES_BUSCAR_FEE_SERVICOS_OFF"
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/com/paytrack/token/integracoes-buscar-fee-servicos-off"
    },
    {
      name      = "COM_PAYTRACK_TOKEN_INTEGRACOES_SALVAR_FEE_SERVICOS_OFF"
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/com/paytrack/token/integracoes-salvar-fee-servicos-off"
    },
    {
      name      = "COM_PAYTRACK_TOKEN_INTEGRACOES_ATUALIZAR_FEE_SERVICOS_OFF"
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/com/paytrack/token/integracoes-atualizar-fee-servicos-off"
    },
    {
      name      = "COM_PAYTRACK_TOKEN_INTEGRACOES_REMOVER_FEE_SERVICOS_OFF"
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/com/paytrack/token/integracoes-remover-fee-servicos-off"
    },
    {
      name      = "COM_PAYTRACK_TOKEN_INTEGRACOES_SALVAR_FEE_SERVICOS_OFF_ETAPAS"
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/com/paytrack/token/integracoes-salvar-fee-servicos-off-etapas"
    },
    {
      name      = "COM_PAYTRACK_TOKEN_INTEGRACOES_ATUALIZAR_FEE_SERVICOS_OFF_ETAPAS"
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/com/paytrack/token/integracoes-atualizar-fee-servicos-off-etapas"
    },
    {
      name      = "COM_PAYTRACK_TOKEN_INTEGRACOES_REMOVER_FEE_SERVICOS_OFF_ETAPAS"
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/com/paytrack/token/integracoes-remover-fee-servicos-off-etapas"
    },
    {
      name      = "COM_PAYTRACK_TOKEN_EXEDENTE_MRR_ANUAL"
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/com/paytrack/token/exedente-mrr-anual"
    },
    {
      name      = "COM_PAYTRACK_TOKEN_GERAR_EVENTO_ATUALIZAR_FORNECEDORES"
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/com/paytrack/token/gerar-evento-atualizar-fornecedores"
    },
    {
      name      = "COM_PAYTRACK_TOKEN_GERAR_EVENTO_ATUALIZAR_CLIENTES"
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/com/paytrack/token/gerar-evento-atualizar-clientes"
    },
    {
      name      = "COM_PAYTRACK_TOKEN_GERAR_EVENTO_ATUALIZAR_COBRANCAS"
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/com/paytrack/token/gerar-evento-atualizar-cobrancas"
    },
    {
      name      = "COM_PAYTRACK_TOKEN_INSERIR_NOVO_FORNECEDOR"
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/com/paytrack/token/inserir-novo-fornecedor"
    },
    {
      name      = "COM_PAYTRACK_TOKEN_COBRANCAS_FINALIZAR"
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/com/paytrack/token/cobrancas-finalizar"
    },
    {
      name      = "COM_PAYTRACK_TOKEN_RECALCULAR_COMISSIONAMENTO_INCENTIVO"
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/com/paytrack/token/recalcular-comissionamento-incentivo"
    },
    {
      name      = "COM_PAYTRACK_TOKEN_FECHAR_PREVISAO_FORNECEDOR_CP"
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/com/paytrack/token/fechar-previsao-fornecedor-cp"
    },
    {
      name      = "COM_PAYTRACK_TOKEN_FECHAR_PREVISAO_FORNECEDOR_CR"
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/com/paytrack/token/fechar-previsao-fornecedor-cr"
    },
    {
      name      = "COM_PAYTRACK_TOKEN_ALTERAR_CLIENTE_INTEGRACAO"
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/com/paytrack/token/alterar-cliente-integracao"
    },
    {
      name      = "COM_PAYTRACK_TOKEN_MRR"
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/com/paytrack/token/mrr"
    },
    {
      name      = "COM_PAYTRACK_TOKEN_MRR_V1"
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/com/paytrack/token/mrr-v1"
    },
    {
      name      = "COM_PAYTRACK_TOKEN_MRR_V2"
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/com/paytrack/token/mrr-v2"
    }
  ]

  service_name = "clientes-backend"
  target_arn   = module.api_gateway.target_group_arn
  listener_arn = module.api_gateway.listener_arn

  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props   = local.health_check_props_default

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  specifications           = local.medium
  docker_labels            = local.docker_labels
  gateway = {
    route              = "/clientes"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night     = false
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_min_capacity
  public                = true
}

module "integrador_localiza" {
  source                = "../../modules/service"
  environment_name      = local.environment_name
  app_docker_image      = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/integrador-localiza:${local.environment_name}"
  environment_variables = local.quarkus_default_env
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name = "integrador-localiza"

  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props   = local.health_check_props_default

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  specifications           = local.small
  gateway = {
    route              = "/integrador-localiza"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night     = false
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  public                = false
  docker_labels         = local.docker_labels
}

module "diretorio-hoteis" {
  source           = "../../modules/service"
  environment_name = local.environment_name
  app_docker_image = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/diretorio-hoteis:${local.environment_name}"
  environment_variables = concat(local.spring_default_env, [
    {
      name  = "SPRING_DATASOURCE_URL"
      value = "************************************************"
    },
    {
      name  = "SPRING_DATASOURCE_USERNAME"
      value = "diretorio_hoteis"
    },
    {
      name  = "SPRING_DATASOURCE_PASSWORD"
      value = "o9Ts#E*51vT7"
    },
    {
      name  = "KAFKA_ENABLED"
      value = "true"
    },
    {
      name  = "OTEL_EXPORTER_OTLP_ENDPOINT"
      value = "http://data-prepper.paytrack.com.br:80"
    },
    {
      name  = "OTEL_EXPORTER_OTLP_PROTOCOL"
      value = "grpc"
    },
    {
      name  = "OTEL_SERVICE_NAME"
      value = "diretorio-hoteis"
    }
  ])
  secrets = [
    {
      name      = "BR_COM_PAYTRACK_KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name  = "diretorio-hoteis"
  public        = true
  target_arn    = module.api_gateway.target_group_arn
  listener_arn  = module.api_gateway.listener_arn
  docker_labels = local.docker_labels

  health_check_command = "curl -s -f http://localhost:8080/actuator/health"
  health_check_props   = local.health_check_props_default

  specifications = {
    cpu    = 256
    memory = 1024
  }

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  gateway = {
    route              = "/hoteis"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night = false
}

module "integrador_hrs" {
  source                = "../../modules/service"
  environment_name      = local.environment_name
  app_docker_image      = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/integrador-hrs:${local.environment_name}"
  environment_variables = local.quarkus_default_env
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name = "integrador-hrs"

  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props   = local.health_check_props_default

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  specifications           = local.small
  gateway = {
    route              = "/integrador-hrs"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night     = false
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  public                = false
  docker_labels         = local.docker_labels
}

module "integrador_movida" {
  source                = "../../modules/service"
  environment_name      = local.environment_name
  app_docker_image      = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/integrador-movida:${local.environment_name}"
  environment_variables = local.quarkus_default_env
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name = "integrador-movida"

  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props   = local.health_check_props_default

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  gateway = {
    route              = "/integrador-movida"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night     = false
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  public                = false
  docker_labels         = local.docker_labels
  specifications        = local.small
}

module "integrador_cangooroo" {
  source                = "../../modules/service"
  environment_name      = local.environment_name
  app_docker_image      = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/integrador-cangooroo:${local.environment_name}"
  environment_variables = local.quarkus_default_env
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name = "integrador-cangooroo"

  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props   = local.health_check_props_default

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  specifications           = local.medium
  gateway = {
    route              = "/integrador-cangooroo"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night     = false
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  public                = false
  docker_labels         = local.docker_labels
}

module "integrador_reservafacil" {
  source                = "../../modules/service"
  environment_name      = local.environment_name
  app_docker_image      = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/integrador-reservafacil:${local.environment_name}"
  environment_variables = local.quarkus_default_env
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name = "integrador-reservafacil"

  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props   = local.health_check_props_default

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  specifications = {
    cpu    = 512
    memory = 2048
  }
  gateway = {
    route              = "/integrador-reservafacil"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night     = false
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  public                = false
  docker_labels         = local.docker_labels
}

module "pesquisa_servico" {
  source           = "../../modules/service"
  environment_name = local.environment_name
  app_docker_image = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/pesquisa-servico:${local.environment_name}"
  environment_variables = concat(local.quarkus_default_env, [
    {
      name  = "QUARKUS_DATASOURCE_USERNAME",
      value = "pesquisa_servico"
    },
    {
      name  = "QUARKUS_DATASOURCE_PASSWORD",
      value = "466XlFO9@07w"
    },
    {
      name  = "QUARKUS_DATASOURCE_JDBC_URL",
      value = "**********************************"
    },
    {
      name  = "QUARKUS_MONGODB_CONNECTION_STRING",
      value = "mongodb://root:<EMAIL>:27017/pesquisa-servico?replicaSet=rs0&readPreference=secondaryPreferred&retryWrites=false&serverSelectionTimeoutMS=5000"
    },
    {
      name  = "BR_COM_PAYTRACK_URL_TRADUTOR",
      value = "https://tradutor.paytrack.com.br/tradutor/"
    },
    {
      name  = "BR_COM_PAYTRACK_URL_DIRETORIO_HOTEIS",
      value = "http://gateway.prod.paytrack.local/hoteis/"
    },
    {
      name  = "COM_PAYTRACK_S3_BUCKET",
      value = "viajor-app-vault"
    },
    {
      name  = "awsAccessKey",
      value = var.pesquisa_servico_key
    },
    {
      name  = "awsSecretKey",
      value = var.pesquisa_servico_secret
    },
    {
      name  = "QUARKUS_DATASOURCE__MATCH_HOTEIS__USERNAME",
      value = "postgres"
    },
    {
      name  = "QUARKUS_DATASOURCE__MATCH_HOTEIS__PASSWORD",
      value = "64A2zpA5JrilPK3Aejdg"
    },
    {
      name  = "QUARKUS_DATASOURCE__MATCH_HOTEIS__JDBC_URL",
      value = "****************************************************************************************"
    }
  ])
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name = "pesquisa-servico"

  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props   = local.health_check_props_default
  docker_labels        = local.docker_labels

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  specifications           = local.large
  gateway = {
    route              = "/pesquisa-servico"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night = false

  public                = true
  auto_scaling          = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  target_arn            = module.api_gateway.target_group_arn
  listener_arn          = module.api_gateway.listener_arn
}

module "integrador_swap" {
  source               = "../../modules/service"
  environment_name     = local.environment_name
  app_docker_image     = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/integrador-swap:${local.environment_name}"
  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props = {
    interval    = 10,
    timeout     = 10,
    retries     = 6,
    startPeriod = 60
  }
  environment_variables = concat(local.quarkus_default_env, [
    {
      name  = "KAFKA_GROUP_ID",
      value = "prod-integrador-swap"
    },
  ])
  service_name = "integrador-swap"
  specifications = {
    cpu    = 256
    memory = 1024
  }

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  gateway = {
    route              = "/integrador-swap"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night     = false
  auto_scaling          = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  public                = false

  secrets = [
    {
      name      = "SWAP_APIKEY"
      valueFrom = "arn:aws:secretsmanager:us-east-1:393346304284:secret:prod/integrador-swap-nnGEja:x-api-key::"
    },
    {
      name      = "SWAP_SECRET"
      valueFrom = "arn:aws:secretsmanager:us-east-1:393346304284:secret:prod/integrador-swap-nnGEja:client-secret::"
    },
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/kafka/paytrack-kafka/broker/url/plaintext"
    },
    {
      name      = "MTLS_CERT"
      valueFrom = "arn:aws:secretsmanager:us-east-1:393346304284:secret:prod/integrador-swap-nnGEja:mtls-cert::"
    },
    {
      name      = "MTLS_KEY"
      valueFrom = "arn:aws:secretsmanager:us-east-1:393346304284:secret:prod/integrador-swap-nnGEja:mtls-key::"
    }
  ]
  docker_labels = local.docker_labels
}

module "cartao_backend" {
  source               = "../../modules/service"
  environment_name     = local.environment_name
  app_docker_image     = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/cartao-backend:${local.environment_name}"
  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props = {
    interval    = 10,
    timeout     = 10,
    retries     = 6,
    startPeriod = 240
  }

  environment_variables = concat(local.quarkus_default_env, [
    {
      name  = "QUARKUS_DATASOURCE_USERNAME",
      value = "postgres"
    },
    {
      name  = "QUARKUS_DATASOURCE_PASSWORD",
      value = "aOLjwjSEjJHjo7U8Ybg7"
    },
    {
      name  = "QUARKUS_DATASOURCE_JDBC_URL",
      value = "**********************************************************************************************"
    },
    {
      name  = "QUARKUS_DATASOURCE_REACTIVE_URL",
      value = "postgresql://paytrack-cartao.c4tposufhwhv.us-east-1.rds.amazonaws.com:5432/cartao_backend"
    },
    {
      name  = "QUARKUS_REDIS_HOSTS",
      value = "redis://prod-cache-redis.5ps2ds.ng.0001.use1.cache.amazonaws.com:6379"
    },
    {
      name  = "INTEGRADOR_SWAP_URL",
      value = "http://gateway.prod.paytrack.local/integrador-swap/"
    },
    {
      name  = "KAFKA_GROUP_ID",
      value = "prod-cartao"
    },
    {
      name  = "TRADUTOR_URL",
      value = "https://internal-app.paytrack.com.br/tradutor"
    },
    {
      name  = "PAYTRACK_ENV_URL",
      value = "https://internal-app.paytrack.com.br"
    },
    {
      name  = "AUTENTICADOR_ENV_URL",
      value = "http://auth-internal.paytrack.com.br/autenticador/api"
    }
  ])
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/kafka/paytrack-kafka/broker/url/plaintext"
    },
    {
      name      = "TRADUTOR_USER",
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/prod/paytrack-tradutor/backdoor/user"
    },
    {
      name      = "TRADUTOR_PASSWORD",
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/prod/paytrack-tradutor/backdoor/password"
    },
    {
      name      = "PAYTRACK_ENV_BACKDOOR_USER",
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/prod/paytrack/backdoor/user"
    },
    {
      name      = "PAYTRACK_ENV_BACKDOOR_PASSWORD",
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/prod/paytrack/backdoor/password"
    }
  ]
  service_name   = "cartao-backend"
  specifications = local.large

  target_arn   = module.api_gateway.target_group_arn
  listener_arn = module.api_gateway.listener_arn

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  gateway = {
    route              = "/cartao-backend"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  public                = true
  shutdown_at_night     = false
  auto_scaling          = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  docker_labels         = local.docker_labels
}


module "processador_pagamentos" {
  source               = "../../modules/service"
  environment_name     = local.environment_name
  app_docker_image     = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/processador-pagamentos:${local.environment_name}"
  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props = {
    interval    = 10,
    timeout     = 10,
    retries     = 6,
    startPeriod = 240
  }

  environment_variables = concat(local.quarkus_default_env, [
    {
      name  = "QUARKUS_DATASOURCE_USERNAME",
      value = "postgres"
    },
    {
      name  = "QUARKUS_DATASOURCE_PASSWORD",
      value = "aOLjwjSEjJHjo7U8Ybg7"
    },
    {
      name  = "QUARKUS_DATASOURCE_JDBC_URL",
      value = "**********************************************************************************************"
    },
    {
      name  = "INTEGRADOR_SWAP_URL",
      value = "http://gateway.prod.paytrack.local/integrador-swap/"
    },
    {
      name  = "KAFKA_GROUP_ID",
      value = "prod-processador-pagamentos"
    },
    {
      name  = "AUTENTICADOR_ENV_URL",
      value = "http://auth-internal.paytrack.com.br/autenticador/api"
    }
  ])
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/kafka/paytrack-kafka/broker/url/plaintext"
    },
    {
      name      = "PAYTRACK_ENV_BACKDOOR_USER",
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/prod/paytrack/backdoor/user"
    },
    {
      name      = "PAYTRACK_ENV_BACKDOOR_PASSWORD",
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/prod/paytrack/backdoor/password"
    }
  ]
  service_name   = "processador-pagamentos"
  specifications = local.medium

  target_arn   = module.api_gateway.target_group_arn
  listener_arn = module.api_gateway.listener_arn

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  gateway = {
    route              = "/processador-pagamentos"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  public                = true
  shutdown_at_night     = false
  auto_scaling          = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  docker_labels         = local.docker_labels
}

module "integrador_omnibees" {
  source                = "../../modules/service"
  environment_name      = local.environment_name
  app_docker_image      = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/integrador-omnibees:${local.environment_name}"
  environment_variables = local.quarkus_default_env
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name = "integrador-omnibees"

  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props   = local.health_check_props_default

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  specifications           = local.medium
  auto_scaling_capacity    = local.auto_scaling_default_capacity
  gateway = {
    route              = "/integrador-omnibees"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night   = false
  auto_scaling        = true
  auto_scaling_memory = true
  public              = false
  docker_labels       = local.docker_labels
}

module "integrador_queropassagem" {
  source                = "../../modules/service"
  environment_name      = local.environment_name
  app_docker_image      = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/integrador-queropassagem:${local.environment_name}"
  environment_variables = local.quarkus_default_env
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name = "integrador-queropassagem"


  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props   = local.health_check_props_default

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  specifications           = local.small
  gateway = {
    route              = "/integrador-queropassagem"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night     = false
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  public                = false
  docker_labels         = local.docker_labels
}


module "etl" {
  source           = "../../modules/service"
  environment_name = local.environment_name
  app_docker_image = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/etl:${local.environment_name}"
  environment_variables = concat(local.quarkus_default_env, [
    {
      name  = "QUARKUS_DATASOURCE_REDSHIFT_JDBC_URL",
      value = "**********************************************************************************************************"
    },
    {
      name  = "QUARKUS_DATASOURCE_REDSHIFT_USERNAME",
      value = "root"
    },
    {
      name  = "QUARKUS_DATASOURCE_REDSHIFT_PASSWORD",
      value = "WNdknE48UzrEsWd"
    },
    {
      name  = "QUARKUS_DATASOURCE_PAYTRACK_USERNAME",
      value = "etl"
    },
    {
      name  = "QUARKUS_DATASOURCE_PAYTRACK_PASSWORD",
      value = "XchKg9J0835X"
    },
    {
      name  = "QUARKUS_DATASOURCE_PAYTRACK_JDBC_URL",
      value = "*******************************" // Precisamos usar a escrita pois no momento da replicação precisamos dos dados bem atualizados.
    },
    {
      name  = "QUARKUS_DATASOURCE_PAYTRACK_REPLICA_USERNAME",
      value = "etl"
    },
    {
      name  = "QUARKUS_DATASOURCE_PAYTRACK_REPLICA_PASSWORD",
      value = "XchKg9J0835X"
    },
    {
      name  = "QUARKUS_DATASOURCE_PAYTRACK_REPLICA_JDBC_URL",
      value = "**********************************?useCursorFetch=true"
    },
    {
      name  = "BR_COM_PAYTRACK_REDSHIFT_EMISSOES_SCHEMA",
      value = "emissoes"
    },
    {
      name  = "MP_MESSAGING_INCOMING_EMISSOES_MAX_POLL_RECORDS",
      value = "25"
    },
    {
      name  = "KAFKA_FETCH_MAX_WAIT_MS",
      value = "60000"
    },
    {
      name  = "MP_MESSAGING_INCOMING_DESPESAS_FETCH_MIN_BYTES",
      value = "62000"
    },
    {
      name  = "MP_MESSAGING_INCOMING_EMPRESAS_FETCH_MIN_BYTES",
      value = "62000"
    },
    {
      name  = "MP_MESSAGING_INCOMING_VIAJANTES_FETCH_MIN_BYTES",
      value = "62000"
    },
    {
      name  = "MP_MESSAGING_INCOMING_CENTROS-CUSTO_FETCH_MIN_BYTES",
      value = "62000"
    },
    {
      name  = "QUARKUS_MONGODB_CONNECTION_STRING",
      value = "mongodb://root:<EMAIL>:27017/?replicaSet=rs0&readPreference=secondaryPreferred&retryWrites=false"
    }
  ])
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name         = "etl"
  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props = {
    interval    = 30,
    timeout     = 20,
    retries     = 10,
    startPeriod = 60
  }
  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  specifications           = local.large
  gateway = {
    route              = "/etl"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  docker_labels     = local.docker_labels
  shutdown_at_night = false

}

module "integrador_aereo_precificador" {
  source                = "../../modules/service"
  environment_name      = local.environment_name
  app_docker_image      = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/integrador-aereo-precificador:${local.environment_name}"
  environment_variables = local.quarkus_default_env
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name = "integrador-aereo-precificador"

  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props   = local.health_check_props_default

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  specifications           = local.large
  gateway = {
    route              = "/integrador-aereo-precificador"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night     = false
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  public                = false
  docker_labels         = local.docker_labels
}

module "integrador_clickbus" {
  source                = "../../modules/service"
  environment_name      = local.environment_name
  app_docker_image      = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/integrador-clickbus:${local.environment_name}"
  environment_variables = local.quarkus_default_env
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name = "integrador-clickbus"

  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props   = local.health_check_props_default

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  specifications           = local.small
  gateway = {
    route              = "/integrador-clickbus"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night     = false
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  public                = false
  docker_labels         = local.docker_labels
}

module "fluxo_financeiro" {
  source           = "../../modules/service"
  environment_name = local.environment_name
  app_docker_image = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/fluxo-financeiro:${local.environment_name}"
  environment_variables = concat(local.quarkus_default_env, [
    {
      name  = "QUARKUS_DATASOURCE_USERNAME",
      value = "postgres"
    },
    {
      name  = "QUARKUS_DATASOURCE_PASSWORD",
      value = "FMhjPKeZkRe1R7BEt1q9"
    },
    {
      name  = "QUARKUS_DATASOURCE_JDBC_URL",
      value = "***********************************************************************************************"
    },
    {
      name  = "TZ",
      value = "America/Sao_Paulo"
    },
    {
      name  = "QUARKUS_DATASOURCE_CLIENTES_USERNAME",
      value = "postgres"
    },
    {
      name  = "QUARKUS_DATASOURCE_CLIENTES_PASSWORD",
      value = "FMhjPKeZkRe1R7BEt1q9"
    },
    {
      name  = "QUARKUS_DATASOURCE_CLIENTES_JDBC_URL",
      value = "********************************************************************************************"
    },
    {
      name  = "OTEL_EXPORTER_OTLP_COMPRESSION",
      value = "gzip"
    }

  ])
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/kafka/paytrack-kafka/broker/url/plaintext"
    },
    {
      name      = "COM_PAYTRACK_CLIENTES_TOKEN"
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/com/paytrack/clientes/token"
    },
    {
      name      = "COM_PAYTRACK_CLIENTES_TOKEN_CR_INTEGRAR_FATURADO_PAYTRACK"
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/com/paytrack/clientes/token/cr-integrar-faturado-paytrack"
    },
    {
      name      = "COM_PAYTRACK_CLIENTES_TOKEN_RECEITA_FECHAR_FATURAS"
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/com/paytrack/clientes/token/receita-fechar-faturas"
    },
    {
      name      = "COM_PAYTRACK_CLIENTES_TOKEN_PROCESSAR_REEMBOLSO_AUTOMATICO"
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/com/paytrack/clientes/token/processar-reembolso-automatico"
    },
    {
      name      = "COM_PAYTRACK_CLIENTES_TOKEN_CP_FECHAR_FATURA"
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/com/paytrack/clientes/token/cp-fechar-fatura"
    },
    {
      name      = "COM_PAYTRACK_CLIENTES_TOKEN_FATURAMENTO_AGENCIA_BUSCAR_FATURA_CR"
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/com/paytrack/clientes/token/faturamento-agencia-buscar-fatura-cr"
    },
    {
      name      = "COM_PAYTRACK_CLIENTES_TOKEN_FATURAMENTO_AGENCIA_BUSCAR_ITENS_REEMBOLSO"
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/com/paytrack/clientes/token/faturamento-agencia-buscar-itens-reembolso"
    },
    {
      name      = "COM_PAYTRACK_CLIENTES_TOKEN_FATURAMENTO_AGENCIA_BUSCAR_ITENS_CR"
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/com/paytrack/clientes/token/faturamento-agencia-buscar-itens-cr"
    },
    {
      name      = "COM_PAYTRACK_CLIENTES_TOKEN_FATURAMENTO_AGENCIA_BUSCAR_DADOS_COBRANCA"
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/com/paytrack/clientes/token/faturamento-agencia-buscar-dados-cobranca"
    }
  ]
  service_name = "fluxo-financeiro"
  target_arn   = module.api_gateway.target_group_arn
  listener_arn = module.api_gateway.listener_arn

  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props   = local.health_check_props_default

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  specifications           = local.large
  docker_labels            = local.docker_labels

  gateway = {
    route              = "/fluxo-financeiro"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night = false
  public            = true
}

module "integrador_expedia" {
  source                = "../../modules/service"
  environment_name      = local.environment_name
  app_docker_image      = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/integrador-expedia:${local.environment_name}"
  environment_variables = local.quarkus_default_env
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name = "integrador-expedia"

  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props   = local.health_check_props_default

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  specifications           = local.large
  gateway = {
    route              = "/integrador-expedia"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night     = false
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  public                = false
  docker_labels         = local.docker_labels
}

module "integrador_omie" {
  source                = "../../modules/service"
  environment_name      = local.environment_name
  app_docker_image      = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/integrador-omie:${local.environment_name}"
  environment_variables = local.quarkus_default_env
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name         = "integrador-omie"
  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props   = local.health_check_props_default

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  specifications           = local.small
  gateway = {
    route              = "/integrador-omie"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night     = false
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  public                = false
  docker_labels         = local.docker_labels
}

module "integrador_azul" {
  source                = "../../modules/service"
  environment_name      = local.environment_name
  app_docker_image      = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/integrador-azul:${local.environment_name}"
  environment_variables = local.quarkus_default_env
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name = "integrador-azul"

  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props   = local.health_check_props_default

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  specifications           = local.small
  gateway = {
    route              = "/integrador-azul"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night     = false
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  public                = false
  docker_labels         = local.docker_labels
}

module "integrador_b2b" {
  source                = "../../modules/service"
  environment_name      = local.environment_name
  app_docker_image      = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/integrador-b2b:${local.environment_name}"
  environment_variables = local.quarkus_default_env
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name = "integrador-b2b"

  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props   = local.health_check_props_default

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  specifications           = local.small
  gateway = {
    route              = "/integrador-b2b"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night     = false
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_min_capacity
  public                = false
  docker_labels         = local.docker_labels
}

module "integrador_ehtl" {
  source                = "../../modules/service"
  environment_name      = local.environment_name
  app_docker_image      = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/integrador-ehtl:${local.environment_name}"
  environment_variables = local.quarkus_default_env
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name = "integrador-ehtl"

  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props   = local.health_check_props_default

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  specifications           = local.small
  gateway = {
    route              = "/integrador-ehtl"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night     = false
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  public                = false
  docker_labels         = local.docker_labels
}

module "integrador_bee2pay" {
  source                = "../../modules/service"
  environment_name      = local.environment_name
  app_docker_image      = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/integrador-bee2pay:${local.environment_name}"
  environment_variables = local.quarkus_default_env
  service_name          = "integrador-bee2pay"

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  specifications           = local.small
  gateway = {
    route              = "/integrador-bee2pay"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night     = false
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  public                = false
}

module "debezium-connect" {
  source           = "../../modules/service"
  environment_name = local.environment_name
  app_docker_image = "debezium/connect:2.5"
  app_port         = 8083
  environment_variables = [
    {
      name  = "OFFSET_STORAGE_TOPIC"
      value = "prod-connect-offset"
    },
    {
      name  = "CONFIG_STORAGE_TOPIC"
      value = "prod-connect-config"
    },
    {
      name  = "STATUS_STORAGE_TOPIC"
      value = "prod-connect-status"
    },
    {
      name  = "VALUE_CONVERTER"
      value = "org.apache.kafka.connect.json.JsonConverter"
    },
    {
      name  = "KEY_CONVERTER"
      value = "org.apache.kafka.connect.json.JsonConverter"
    },
    {
      name  = "HEAP_OPTS"
      value = "-Xms512M -Xmx4G"
    }
  ]
  secrets = [
    {
      name      = "BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name = "debezium-connect"

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  specifications = {
    cpu    = 1024
    memory = 4096
  }
  gateway = {
    route              = "/debezium-connect"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
}

module "opa-server" {
  source           = "../../modules/service"
  environment_name = local.environment_name
  app_docker_image = "openpolicyagent/opa:0.58.0"
  app_port         = 8181
  command = [
    "run", "--server",
    "--set", "services.menu.url=https://paytrack-menu.s3.amazonaws.com",
    "--set", "services.menu.credentials.s3_signing.metadata_credentials.aws_region=us-east-1",
    "--set", "bundles.menu.service=menu",
    "--set", "bundles.menu.resource=${local.environment_name}/bundle.tar.gz",
    "--set", "bundles.menu.polling.min_delay_seconds=60",
    "--set", "bundles.menu.polling.max_delay_seconds=120"
  ]
  environment_variables = [
  ]
  service_name = "opa-server"

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  gateway = {
    route              = "/opa-server"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }

  auto_scaling_capacity = local.auto_scaling_default_capacity
  shutdown_at_night     = false
}

module "acesso" {
  source           = "../../modules/service"
  environment_name = local.environment_name
  app_docker_image = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/acesso:${local.environment_name}"
  environment_variables = concat(local.quarkus_default_env, [
    {
      name  = "QUARKUS_DATASOURCE__PAYTRACK_RO__USERNAME",
      value = "acesso"
    },
    {
      name  = "QUARKUS_DATASOURCE__PAYTRACK_RO__PASSWORD",
      value = "%I>qU0S56tEB"
    },
    {
      name  = "QUARKUS_DATASOURCE__PAYTRACK_RO__REACTIVE_URL",
      value = "mysql://db.paytrack.com.br/autenticador"
    },
    {
      name  = "QUARKUS_REST_CLIENT_OPA_CLIENT_URL",
      value = "http://gateway.prod.paytrack.local/opa-server/"
    },
    {
      name  = "QUARKUS_REDIS_HOSTS",
      value = "redis://prod-cache-redis.5ps2ds.ng.0001.use1.cache.amazonaws.com:6379"
    },
  ])
  service_name  = "acesso"
  docker_labels = local.docker_labels

  public       = true
  target_arn   = module.api_gateway.target_group_arn
  listener_arn = module.api_gateway.listener_arn

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  gateway = {
    route              = "/acesso"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }

  auto_scaling_capacity = local.auto_scaling_default_capacity
  shutdown_at_night     = false
}

module "configuracoes" {
  source           = "../../modules/service"
  environment_name = local.environment_name
  app_docker_image = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/configuracoes:${local.environment_name}"
  environment_variables = concat(local.quarkus_default_env, [
    {
      name  = "QUARKUS_DATASOURCE__PAYTRACK__USERNAME",
      value = "configuracoes"
    },
    {
      name  = "QUARKUS_DATASOURCE__PAYTRACK__PASSWORD",
      value = "Y5z1}+3\"h-4p"
    },
    {
      name  = "QUARKUS_DATASOURCE__PAYTRACK__JDBC_URL",
      value = "*******************************"
    } #,
    #{
    #  name  = "AUTENTICADOR_ENV_URL",
    #  value = "http://auth-internal.paytrack.com.br/autenticador/api"
    #},
  ])
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name         = "configuracoes"
  health_check_command = "curl -s -f http://localhost:8080/q/health"
  public               = true
  target_arn           = module.api_gateway.target_group_arn
  listener_arn         = module.api_gateway.listener_arn
  docker_labels        = local.docker_labels

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  specifications           = local.small
  gateway = {
    route              = "/configuracoes"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  auto_scaling_capacity = local.auto_scaling_default_capacity
  shutdown_at_night     = false
}

module "solicitacoes" {
  source           = "../../modules/service"
  environment_name = local.environment_name
  app_docker_image = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/solicitacoes:${local.environment_name}"
  environment_variables = concat(local.quarkus_default_env, [
    {
      name  = "QUARKUS_DATASOURCE__PAYTRACK__USERNAME",
      value = "solicitacoes"
    },
    {
      name  = "QUARKUS_DATASOURCE__PAYTRACK__PASSWORD",
      value = "LaU8p0Nf5(?U"
    },
    {
      name  = "QUARKUS_DATASOURCE__PAYTRACK__JDBC_URL",
      value = "*******************************"
    },
  ])
  service_name = "solicitacoes"

  public        = true
  target_arn    = module.api_gateway.target_group_arn
  listener_arn  = module.api_gateway.listener_arn
  docker_labels = local.docker_labels

  specifications           = local.medium
  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  gateway = {
    route              = "/solicitacoes"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  auto_scaling_capacity = local.auto_scaling_default_capacity
  shutdown_at_night     = false
}

module "reserva_emissao" {
  source           = "../../modules/service"
  environment_name = local.environment_name
  app_docker_image = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/reserva-emissao:${local.environment_name}"
  environment_variables = concat(local.quarkus_default_env, [
    {
      name  = "QUARKUS_DATASOURCE_USERNAME",
      value = "reserva_emissao"
    },
    {
      name  = "QUARKUS_DATASOURCE_JDBC_URL",
      value = "jdbc:postgresql://${module.rds_reserva_emissao.rds_endpoint}:5432/reserva_emissao"
    }
  ])
  secrets = [
    {
      name      = "QUARKUS_DATASOURCE_PASSWORD",
      valueFrom = module.rds_reserva_emissao.db_password_secret_arn
    },
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name = "reserva-emissao"

  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props   = local.health_check_props_default

  specifications           = local.medium
  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  gateway = {
    route              = "/reserva-emissao"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  auto_scaling_capacity = local.auto_scaling_default_capacity
  shutdown_at_night     = false
}

module "integrador_wooba" {
  source                = "../../modules/service"
  environment_name      = local.environment_name
  app_docker_image      = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/integrador-wooba:${local.environment_name}"
  environment_variables = local.quarkus_default_env
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name         = "integrador-wooba"
  health_check_command = "curl -s -f http://localhost:8080/q/health"

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  specifications = {
    cpu    = 1024
    memory = 4096
  }
  gateway = {
    route              = "/integrador-wooba"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night     = false
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  public                = false
  docker_labels         = local.docker_labels
}

module "integrador_latam" {
  source                = "../../modules/service"
  environment_name      = local.environment_name
  app_docker_image      = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/integrador-latam:${local.environment_name}"
  environment_variables = local.quarkus_default_env
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name = "integrador-latam"

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  specifications           = local.medium
  gateway = {
    route              = "/integrador-latam"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night     = false
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  public                = false
  docker_labels         = local.docker_labels
}

module "conciliacao_backend" {
  source           = "../../modules/service"
  environment_name = local.environment_name
  app_docker_image = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/conciliacao-backend:${local.environment_name}"
  environment_variables = concat(local.quarkus_default_env, [
    {
      name  = "QUARKUS_DATASOURCE_USERNAME",
      value = "conciliacao-backend"
    },
    {
      name  = "QUARKUS_DATASOURCE_PASSWORD",
      value = "I8Dcmd8eqM6ITiWE"
    },
    {
      name  = "QUARKUS_DATASOURCE_JDBC_URL",
      value = "*******************************/"
    },
    {
      name  = "KAFKA_GROUP_ID",
      value = "prod-conciliacao"
    },
    {
      name  = "QUARKUS_DATASOURCE_PAYTRACK_REPLICA_USERNAME",
      value = "conciliacao-backend"
    },
    {
      name  = "QUARKUS_DATASOURCE_PAYTRACK_REPLICA_PASSWORD",
      value = "I8Dcmd8eqM6ITiWE"
    },
    {
      name  = "QUARKUS_DATASOURCE_PAYTRACK_REPLICA_JDBC_URL",
      value = "**********************************/"
    }
  ])
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:393346304284:parameter/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]

  gateway = {
    route              = "/conciliacao-backend"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  health_check_command     = "curl -s -f http://localhost:8080/q/health"
  service_name             = "conciliacao-backend"
  public                   = true
  target_arn               = module.api_gateway.target_group_arn
  listener_arn             = module.api_gateway.listener_arn
  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  specifications           = local.medium
  replicas                 = 2
  auto_scaling             = true
  auto_scaling_memory      = true
  auto_scaling_capacity    = local.auto_scaling_default_capacity
  shutdown_weekend         = false
  shutdown_at_night        = false
}
