module "aereo-ia" {
  source                = "../../modules/service"
  environment_name      = local.environment_name
  app_docker_image      = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/aereo-ia:${local.environment_name}"
  environment_variables = concat(local.quarkus_default_env, [
    {
    name = "QUARKUS_DATASOURCE_DB-KIND"
    value = "postgresql"
    },
    {
    name = "QUARKUS_DATASOURCE_USERNAME"
    value = module.rds_aereo_ia.app_user
    },
    {
    name = "QUARKUS_DATASOURCE_PASSWORD"
    value = module.rds_aereo_ia.app_password
    },
    {
    name = "QUARKUS_DATASOURCE_JDBC_URL"
    value = "**************************************************************************"
    }
  ])
  service_name = "aereo-ia"

  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props   = local.health_check_props_default

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  specifications           = local.small

  gateway = {
    route              = "/aereo-ia"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }

  public       = true
  target_arn   = module.api_gateway.target_group_arn
  listener_arn = module.api_gateway.listener_arn

  shutdown_at_night     = false
  auto_scaling          = false
  auto_scaling_memory   = false
  auto_scaling_capacity = local.auto_scaling_default_capacity
  docker_labels         = local.docker_labels
}

module "rds_aereo_ia" { #por enquanto, será usado para dev e staging por economia
  create_rds_instance   = false
  db_instance_addr      = "db-aereo-ia.developer.paytrack.com.br" 
  source                = "../../../blueprints/rds-postgres"
  profile               = "hml" #profile do aws cli
  region                = "us-east-1"
  application_name      = "aereo-ia"
  environment_name      = "developer"
  allowed_cidr_blocks   = [ "172.18.0.0/16", "172.29.0.0/16", "192.168.224.0/24", "10.20.0.0/16" ]
  vpc_id                = "vpc-00648d5a38c32ba60"
  subnet_group          = "default-vpc-00648d5a38c32ba60"
  instance_size         = "db.t4g.micro"
  pg_version            = "17.5"
  dns_developer         = true
  security_group_name   = "rds-aereo-ia"
}
