locals {
  environment_name = "staging"
  default_env = [
    {
      name  = "PAYTRACK_AMBIENTE",
      value = local.environment_name
    },
    {
      name  = "AWS_REGION",
      value = "us-east-1"
    }
  ]
  quarkus_default_env = concat(local.default_env, [
    {
      name  = "QUARKUS_PROFILE",
      value = local.environment_name
    },
    {
      name  = "QUARKUS_LOG_HANDLER_GELF_ENABLED"
      value = "true"
    },
    {
      name  = "QUARKUS_LOG_HANDLER_GELF_HOST"
      value = "tcp:logs-dev.paytrack.com.br"
    },
    {
      name  = "QUARKUS_LOG_HANDLER_GELF_PORT"
      value = "12201"
    },
    {
      name  = "QUARKUS_LOG_HANDLER_GELF_INCLUDE_FULL_MDC"
      value = "true"
    },
    {
      name  = "QUARKUS_LOG_HANDLER_GELF_TIMESTAMP_PATTERN"
      value = "dd MMM yyyy HH:mm:ss,SSS"
    },
    {
      name  = "QUARKUS_OPENTELEMETRY_ENABLED",
      value = "true"
    },
    {
      name  = "QUARKUS_OPENTELEMETRY_TRACER_EXPORTER_OTLP_ENDPOINT"
      value = "http://collector-staging.paytrack.com.br:80"
    },
    #versão 3.20 do quarkus suporta os 3 tipos de dados pelo opentelemetry
    {
    name  = "QUARKUS_OTEL_LOGS_ENABLED"
    value = "true"
    },
    {
    name  = "QUARKUS_OTEL_METRICS_ENABLED"
    value = "true"
    },
    {
    name  = "QUARKUS_OTEL_LOGS_EXPORTER"
    value = "otlp"
    },
    {
    name  = "QUARKUS_OTEL_METRICS_EXPORTER"
    value = "otlp"
    },
    {
    name  = "QUARKUS_OTEL_TRACES_EXPORTER"
    value = "otlp"
    },
    {
      name  = "QUARKUS_OTEL_EXPORTER_OTLP_ENDPOINT"
      value = "http://collector-staging.paytrack.com.br:80"
    },
    {
      name  = "QUARKUS_OTEL_EXPORTER_OTLP_TRACES_ENDPOINT"
      value = "http://collector-staging.paytrack.com.br:80"
    }
  ])
  spring_default_env = concat(local.default_env, [
    {
      name  = "SPRING_PROFILES_ACTIVE",
      value = "${local.environment_name},logstash"
    }
  ])
  small = {
    cpu    = 512
    memory = 1024
  }
  medium = {
    cpu    = 1024
    memory = 2048
  }
  large = {
    cpu    = 2048
    memory = 4096
  }

  health_check_props_default = {
    interval    = 10,
    timeout     = 10,
    retries     = 6,
    startPeriod = 60
  }

  auto_scaling_default_capacity = {
    min = 1,
    max = 1
  }

  docker_labels = {
    "PROMETHEUS_EXPORTER_PATH" : "/q/metrics",
    "PROMETHEUS_EXPORTER_PORT" : "8080"
  }
}

terraform {
  backend "s3" {
    bucket  = "terraform-iac-v2"
    key     = "microservice/environments/staging/applications.tfstate"
    region  = "us-east-1"
    profile = "main_account"
  }

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }

  required_version = "~> 1.1"
}

module "infra" {
  source           = "../../modules/infra"
  environment_name = local.environment_name

}

module "route53" {
  source           = "../../modules/route53"
  environment_name = local.environment_name
}

module "vpc_peering" {
  source           = "../../modules/vpc_peering"
  environment_name = local.environment_name
  origin_data = {
    hosted_zone_id = module.infra.hosted_zone_id
    vpc_id         = module.infra.vpc_id
  }
  target_data = {
    vpc_id = "vpc-289f564e"
  }
}

module "vpc_peering_prod" {
  source           = "../../modules/vpc_peering"
  environment_name = local.environment_name
  origin_data = {
    hosted_zone_id = module.infra.hosted_zone_id
    vpc_id         = module.infra.vpc_id
  }
  target_data = {
    vpc_id = "vpc-06d3dac02535f5752"
  }
}

module "collector-proxy" {
  source           = "../../modules/collector-proxy"
  environment_name = local.environment_name
}

module "api_gateway" {
  source           = "../../modules/api_gateway"
  environment_name = local.environment_name
  certificate_arn  = module.route53.certificate_arn
  route53_zone_id  = module.route53.public_hosted_zone_id
  subnets          = module.infra.public_subnets
  security_groups  = [module.infra.security_group_id]
  vpc_id           = module.infra.vpc_id
  private_subnets  = module.infra.private_subnets
  ecs_cluster      = module.infra.ecs_cluster
  private_dns_id   = module.infra.private_dns_id
  paytrack_urls = [
    "https://hom.paytrack.com.br",
    "https://clientes.staging.paytrack.com.br",
    "https://supplier-hom.paytrack.com.br",
  ]
  iam_roles = {
    execution_arn = module.infra.task_execution_iam_role
    task_arn      = module.infra.task_iam_role
  }
  mesh_name         = module.infra.mesh_id
  shutdown_at_night = false
  shutdown_weekend  = true
}

data "aws_region" "current" {}

module "integrador_arbi" {
  source                = "../../modules/service"
  environment_name      = local.environment_name
  app_docker_image      = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/integrador-arbi:${local.environment_name}"
  environment_variables = local.quarkus_default_env

  secrets = [
    {
      name      = "ARBI_USER"
      valueFrom = "arn:aws:secretsmanager:us-east-1:560211763190:secret:staging/arbi-nYvyH3:USER::"
    },
    {
      name      = "ARBI_PASSWORD"
      valueFrom = "arn:aws:secretsmanager:us-east-1:560211763190:secret:staging/arbi-nYvyH3:PASSWORD::"
    },
    {
      name      = "ARBI_CLIENT_ID",
      valueFrom = "arn:aws:secretsmanager:us-east-1:560211763190:secret:staging/arbi-nYvyH3:CLIENT-ID::"
    },
    {
      name      = "ARBI_INSCRICAO_PARCEIRO"
      valueFrom = "arn:aws:secretsmanager:us-east-1:560211763190:secret:staging/arbi-nYvyH3:INSCRICAO-PARCEIRO::"
    },
    {
      name      = "ARBI_TOKEN"
      valueFrom = "arn:aws:secretsmanager:us-east-1:560211763190:secret:staging/arbi-nYvyH3:TOKEN::"
    },
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/staging/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name = "integrador-arbi"

  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props   = local.health_check_props_default

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  gateway = {
    route              = "/integrador-arbi"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
}

module "integrador_sabre" {
  source                = "../../modules/service"
  environment_name      = local.environment_name
  app_docker_image      = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/integrador-sabre:${local.environment_name}"
  environment_variables = local.quarkus_default_env
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/staging/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name = "integrador-sabre"
  
  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props   = local.health_check_props_default

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  gateway = {
    route              = "/integrador-sabre"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night = false
  shutdown_weekend  = true
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  docker_labels       = local.docker_labels
}
module "clientes-frontend" {
  source                = "../../modules/frontend"
  environment_name      = local.environment_name
  project_name          = "clientes"
  certificate_arn       = module.route53.certificate_arn
  public_hosted_zone_id = module.route53.public_hosted_zone_id
}

module "clientes-backend" {
  source           = "../../modules/service"
  environment_name = local.environment_name
  app_docker_image = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/clientes-backend:${local.environment_name}"
  environment_variables = concat(local.quarkus_default_env, [
    {
      name  = "FRONTEND_URL",
      value = "https://${module.clientes-frontend.url_frontend}"
    },
    {
      name  = "QUARKUS_DATASOURCE_USERNAME",
      value = "postgres"
    },
    {
      name  = "QUARKUS_DATASOURCE_PASSWORD",
      value = "ETFWCC9C1Y632Ja7mcQO"
    },
    {
      name  = "QUARKUS_DATASOURCE_JDBC_URL",
      value = "***********************************************************************************************"
    },
    {
      name  = "QUARKUS_DATASOURCE_LEITURA_USERNAME",
      value = "postgres"
    },
    {
      name  = "QUARKUS_DATASOURCE_LEITURA_PASSWORD",
      value = "ETFWCC9C1Y632Ja7mcQO"
    },
    {
      name  = "QUARKUS_DATASOURCE_LEITURA_JDBC_URL",
      value = "***********************************************************************************************"
    }
  ])
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/staging/kafka/paytrack-kafka/broker/url/tls"
    },
    {
      name      = "COM_PAYTRACK_USUARIO_AUTENTICACAO",
      valueFrom = "arn:aws:secretsmanager:us-east-1:560211763190:secret:developer/paytrack-backdoor-X80HgV:usuario::"
    },
    {
      name      = "COM_PAYTRACK_SENHA_AUTENTICACAO",
      valueFrom = "arn:aws:secretsmanager:us-east-1:560211763190:secret:developer/paytrack-backdoor-X80HgV:senha::"
    },
    {
      name      = "COM_PAYTRACK_TOKEN"
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/com/paytrack/token"
    },
    {
      name      = "COM_PAYTRACK_TOKEN_INTEGRACOES_BUSCAR_FEE_SERVICOS_OFF"
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/com/paytrack/token/integracoes-buscar-fee-servicos-off"
    },
    {
      name      = "COM_PAYTRACK_TOKEN_INTEGRACOES_SALVAR_FEE_SERVICOS_OFF"
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/com/paytrack/token/integracoes-salvar-fee-servicos-off"
    },
    {
      name      = "COM_PAYTRACK_TOKEN_INTEGRACOES_ATUALIZAR_FEE_SERVICOS_OFF"
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/com/paytrack/token/integracoes-atualizar-fee-servicos-off"
    },
    {
      name      = "COM_PAYTRACK_TOKEN_INTEGRACOES_REMOVER_FEE_SERVICOS_OFF"
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/com/paytrack/token/integracoes-remover-fee-servicos-off"
    },
    {
      name      = "COM_PAYTRACK_TOKEN_INTEGRACOES_SALVAR_FEE_SERVICOS_OFF_ETAPAS"
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/com/paytrack/token/integracoes-salvar-fee-servicos-off-etapas"
    },
    {
      name      = "COM_PAYTRACK_TOKEN_INTEGRACOES_ATUALIZAR_FEE_SERVICOS_OFF_ETAPAS"
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/com/paytrack/token/integracoes-atualizar-fee-servicos-off-etapas"
    },
    {
      name      = "COM_PAYTRACK_TOKEN_INTEGRACOES_REMOVER_FEE_SERVICOS_OFF_ETAPAS"
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/com/paytrack/token/integracoes-remover-fee-servicos-off-etapas"
    },
    {
      name      = "COM_PAYTRACK_TOKEN_EXEDENTE_MRR_ANUAL"
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/com/paytrack/token/exedente-mrr-anual"
    },
    {
      name      = "COM_PAYTRACK_TOKEN_GERAR_EVENTO_ATUALIZAR_FORNECEDORES"
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/com/paytrack/token/gerar-evento-atualizar-fornecedores"
    },
    {
      name      = "COM_PAYTRACK_TOKEN_GERAR_EVENTO_ATUALIZAR_CLIENTES"
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/com/paytrack/token/gerar-evento-atualizar-clientes"
    },
    {
      name      = "COM_PAYTRACK_TOKEN_GERAR_EVENTO_ATUALIZAR_COBRANCAS"
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/com/paytrack/token/gerar-evento-atualizar-cobrancas"
    },
    {
      name      = "COM_PAYTRACK_TOKEN_INSERIR_NOVO_FORNECEDOR"
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/com/paytrack/token/inserir-novo-fornecedor"
    },
    {
      name      = "COM_PAYTRACK_TOKEN_COBRANCAS_FINALIZAR"
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/com/paytrack/token/cobrancas-finalizar"
    },
    {
      name      = "COM_PAYTRACK_TOKEN_RECALCULAR_COMISSIONAMENTO_INCENTIVO"
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/com/paytrack/token/recalcular-comissionamento-incentivo"
    },
    {
      name      = "COM_PAYTRACK_TOKEN_FECHAR_PREVISAO_FORNECEDOR_CP"
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/com/paytrack/token/fechar-previsao-fornecedor-cp"
    },
    {
      name      = "COM_PAYTRACK_TOKEN_FECHAR_PREVISAO_FORNECEDOR_CR"
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/com/paytrack/token/fechar-previsao-fornecedor-cr"
    },
    {
      name      = "COM_PAYTRACK_TOKEN_ALTERAR_CLIENTE_INTEGRACAO"
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/com/paytrack/token/alterar-cliente-integracao"
    },
    {
      name      = "COM_PAYTRACK_TOKEN_MRR"
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/com/paytrack/token/mrr"
    },
    {
      name      = "COM_PAYTRACK_TOKEN_MRR_V2"
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/com/paytrack/token/mrr-v2"
    }
  ]
  service_name = "clientes-backend"
  public       = true
  target_arn   = module.api_gateway.target_group_arn
  listener_arn = module.api_gateway.listener_arn

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  gateway = {
    route              = "/clientes"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night = false
  shutdown_weekend  = true
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  docker_labels         = local.docker_labels
}

module "diretorio-hoteis" {
  source           = "../../modules/service"
  environment_name = local.environment_name
  app_docker_image = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/diretorio-hoteis:${local.environment_name}"
  environment_variables = concat(local.spring_default_env, [
    {
      name  = "SPRING_DATASOURCE_URL"
      value = "***********************************************************************************"
    },
    {
      name  = "SPRING_DATASOURCE_USERNAME"
      value = "diretorio_hoteis"
    },
    {
      name  = "SPRING_DATASOURCE_PASSWORD"
      value = "cONLQ427e6@5"
    },
    {
      name  = "KAFKA_ENABLED"
      value = "true"
    }
  ])
  secrets = [
    {
      name      = "BR_COM_PAYTRACK_KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/staging/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name = "diretorio-hoteis"
  public       = true
  target_arn   = module.api_gateway.target_group_arn
  listener_arn = module.api_gateway.listener_arn

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  gateway = {
    route              = "/hoteis"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night = false
  shutdown_weekend  = true
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  docker_labels         = local.docker_labels
}


module "integrador_localiza" {
  source                = "../../modules/service"
  environment_name      = local.environment_name
  app_docker_image      = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/integrador-localiza:${local.environment_name}"
  environment_variables = local.quarkus_default_env
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/staging/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name = "integrador-localiza"
  
  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props   = local.health_check_props_default

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  gateway = {
    route              = "/integrador-localiza"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night = false
  shutdown_weekend  = true
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  docker_labels         = local.docker_labels
}

# module "cartao_backend" {
#   source           = "../../modules/service"
#   environment_name = local.environment_name
#   app_docker_image = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/cartao-backend:${local.environment_name}"
#   environment_variables = concat(local.quarkus_default_env, [
#     {
#       name  = "QUARKUS_DATASOURCE_USERNAME",
#       value = "postgres"
#     },
#     {
#       name  = "QUARKUS_DATASOURCE_PASSWORD",
#       value = "jretPsvjfApPPKnreTud"
#     },
#     {
#       name  = "QUARKUS_DATASOURCE_JDBC_URL",
#       value = "*************************************************************************************************"
#     }
#   ])
#   service_name = "cartao-backend"

#   public       = true
#   target_arn   = module.api_gateway.target_group_arn
#   listener_arn = module.api_gateway.listener_arn

#   app_mesh_name            = module.infra.mesh_id
#   private_dns_namespace_id = module.infra.private_dns_id
#   task_execution_iam_role  = module.infra.task_execution_iam_role
#   task_iam_role            = module.infra.task_iam_role
#   subnets                  = module.infra.private_subnets
#   ecs_cluster              = module.infra.ecs_cluster
#   security_group           = module.infra.security_group_id
#   gateway = {
#     route              = "/cartao-backend"
#     virtual_gateway_id = module.api_gateway.virtual_gateway_id
#   }
#   shutdown_at_night = false
#    shutdown_weekend = true
# }

module "integrador_hrs" {
  source                = "../../modules/service"
  environment_name      = local.environment_name
  app_docker_image      = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/integrador-hrs:${local.environment_name}"
  environment_variables = local.quarkus_default_env
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/staging/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name = "integrador-hrs"
  
  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props   = local.health_check_props_default

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  gateway = {
    route              = "/integrador-hrs"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night = false
  shutdown_weekend  = true
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  docker_labels         = local.docker_labels
}

# module "integrador_swap" {
#   source                = "../../modules/service"
#   environment_name      = local.environment_name
#   app_docker_image      = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/integrador-swap:${local.environment_name}"
#   environment_variables = local.quarkus_default_env
#   service_name          = "integrador-swap"

#   app_mesh_name            = module.infra.mesh_id
#   private_dns_namespace_id = module.infra.private_dns_id
#   task_execution_iam_role  = module.infra.task_execution_iam_role
#   task_iam_role            = module.infra.task_iam_role
#   subnets                  = module.infra.private_subnets
#   ecs_cluster              = module.infra.ecs_cluster
#   security_group           = module.infra.security_group_id
#   gateway = {
#     route              = "/integrador-swap"
#     virtual_gateway_id = module.api_gateway.virtual_gateway_id
#   }
#   shutdown_at_night = false
#    shutdown_weekend = true
# }

module "integrador_movida" {
  source                = "../../modules/service"
  environment_name      = local.environment_name
  app_docker_image      = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/integrador-movida:${local.environment_name}"
  environment_variables = local.quarkus_default_env
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/staging/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name = "integrador-movida"
  
  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props   = local.health_check_props_default

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  gateway = {
    route              = "/integrador-movida"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night = false
  shutdown_weekend  = true
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  docker_labels         = local.docker_labels
}

module "integrador_cangooroo" {
  source                = "../../modules/service"
  environment_name      = local.environment_name
  app_docker_image      = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/integrador-cangooroo:${local.environment_name}"
  environment_variables = local.quarkus_default_env
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/staging/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name = "integrador-cangooroo"
  
  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props   = local.health_check_props_default

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  gateway = {
    route              = "/integrador-cangooroo"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night = false
  shutdown_weekend  = true
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  docker_labels         = local.docker_labels
}

module "integrador_cangooroo_v2" {
  source                = "../../modules/service"
  environment_name      = local.environment_name
  app_docker_image      = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/integrador-cangooroo-v2:${local.environment_name}"
  environment_variables = local.quarkus_default_env
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/staging/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name = "integrador-cangooroo-v2"
  
  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props   = local.health_check_props_default

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  gateway = {
    route              = "/integrador-cangooroo-v2"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night = false
  shutdown_weekend  = true
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  docker_labels         = local.docker_labels
}

module "integrador_reservafacil" {
  source                = "../../modules/service"
  environment_name      = local.environment_name
  app_docker_image      = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/integrador-reservafacil:${local.environment_name}"
  environment_variables = local.quarkus_default_env
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/staging/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name = "integrador-reservafacil"
  
  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props   = local.health_check_props_default

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  gateway = {
    route              = "/integrador-reservafacil"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night = false
  shutdown_weekend  = true
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  docker_labels         = local.docker_labels
}

module "integrador_queropassagem" {
  source                = "../../modules/service"
  environment_name      = local.environment_name
  app_docker_image      = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/integrador-queropassagem:${local.environment_name}"
  environment_variables = local.quarkus_default_env
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/staging/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name = "integrador-queropassagem"
  
  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props   = local.health_check_props_default

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  gateway = {
    route              = "/integrador-queropassagem"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night = false
  shutdown_weekend  = true
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  docker_labels         = local.docker_labels
}


module "pesquisa_servico" {
  source           = "../../modules/service"
  environment_name = local.environment_name
  app_docker_image = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/pesquisa-servico:${local.environment_name}"
  environment_variables = concat(local.quarkus_default_env, [
    {
      name  = "QUARKUS_DATASOURCE_USERNAME",
      value = "pesquisa_servico"
    },
    {
      name  = "QUARKUS_DATASOURCE_PASSWORD",
      value = "xgE545$4c@BD"
    },
    {
      name  = "QUARKUS_DATASOURCE_JDBC_URL",
      value = "******************************************************************"
    },
    {
      name  = "QUARKUS_MONGODB_CONNECTION_STRING",
      value = "mongodb://root:v7&<EMAIL>:27017/pesquisa-servico-staging?replicaSet=rs0&readPreference=secondaryPreferred&retryWrites=false"
    },
    {
      name  = "BR_COM_PAYTRACK_URL_TRADUTOR",
      value = "https://htradutor.paytrack.com.br/tradutor/"
    },
    {
      name  = "BR_COM_PAYTRACK_URL_DIRETORIO_HOTEIS",
      value = "http://gateway.staging.paytrack.local/hoteis/"
    },
    {
      name  = "COM_PAYTRACK_MONGO_DATABASE",
      value = "pesquisa-servico-staging"
    },
    {
      name  = "QUARKUS_DATASOURCE__MATCH_HOTEIS__JDBC_URL"
      value = "**************************************************************************************************"
    },
    {
      name  = "QUARKUS_DATASOURCE__MATCH_HOTEIS__PASSWORD"
      value = "slRmHglQgx2gTMh0ODUU"
    },
    {
      name  = "QUARKUS_DATASOURCE__MATCH_HOTEIS__USERNAME"
      value = "postgres"
    }
  ])
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/staging/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name = "pesquisa-servico"

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  specifications           = local.small
  health_check_command     = "curl -s -f http://localhost:8080/metrics"
  gateway = {
    route              = "/pesquisa-servico"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night = false
  shutdown_weekend  = true
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  docker_labels         = local.docker_labels

  public       = true
  target_arn   = module.api_gateway.target_group_arn
  listener_arn = module.api_gateway.listener_arn
}

module "integrador_omnibees" {
  source                = "../../modules/service"
  environment_name      = local.environment_name
  app_docker_image      = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/integrador-omnibees:${local.environment_name}"
  environment_variables = local.quarkus_default_env
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/staging/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name = "integrador-omnibees"
  
  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props   = local.health_check_props_default

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  gateway = {
    route              = "/integrador-omnibees"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night = false
  shutdown_weekend  = true
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  docker_labels         = local.docker_labels
}

module "integrador_aereo_precificador" {
  source                = "../../modules/service"
  environment_name      = local.environment_name
  app_docker_image      = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/integrador-aereo-precificador:${local.environment_name}"
  environment_variables = local.quarkus_default_env
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/staging/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name = "integrador-aereo-precificador"
  
  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props   = local.health_check_props_default

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  gateway = {
    route              = "/integrador-aereo-precificador"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night = false
  shutdown_weekend  = true
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  docker_labels         = local.docker_labels
}

module "integrador_clickbus" {
  source                = "../../modules/service"
  environment_name      = local.environment_name
  app_docker_image      = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/integrador-clickbus:${local.environment_name}"
  environment_variables = local.quarkus_default_env
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/staging/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name = "integrador-clickbus"
  
  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props   = local.health_check_props_default

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  gateway = {
    route              = "/integrador-clickbus"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night = false
  shutdown_weekend  = true
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  docker_labels         = local.docker_labels
}

module "integrador_omie" {
  source                = "../../modules/service"
  environment_name      = local.environment_name
  app_docker_image      = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/integrador-omie:${local.environment_name}"
  environment_variables = local.quarkus_default_env
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/staging/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name = "integrador-omie"
  
  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props   = local.health_check_props_default

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  gateway = {
    route              = "/integrador-omie"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night = false
  shutdown_weekend  = true
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  docker_labels         = local.docker_labels
}

module "integrador_azul" {
  source                = "../../modules/service"
  environment_name      = local.environment_name
  app_docker_image      = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/integrador-azul:${local.environment_name}"
  environment_variables = local.quarkus_default_env
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/staging/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name = "integrador-azul"
  
  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props   = local.health_check_props_default

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  gateway = {
    route              = "/integrador-azul"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night = false
  shutdown_weekend  = true
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  docker_labels         = local.docker_labels
}

module "integrador_b2b" {
  source                = "../../modules/service"
  environment_name      = local.environment_name
  app_docker_image      = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/integrador-b2b:${local.environment_name}"
  environment_variables = local.quarkus_default_env
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/staging/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name = "integrador-b2b"
  
  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props   = local.health_check_props_default

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  gateway = {
    route              = "/integrador-b2b"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night = false
  shutdown_weekend  = true
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  docker_labels         = local.docker_labels
}

module "integrador_expedia" {
  source                = "../../modules/service"
  environment_name      = local.environment_name
  app_docker_image      = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/integrador-expedia:${local.environment_name}"
  environment_variables = local.quarkus_default_env
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/staging/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name = "integrador-expedia"
  
  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props   = local.health_check_props_default

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  gateway = {
    route              = "/integrador-expedia"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night = false
  shutdown_weekend  = true
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  docker_labels         = local.docker_labels
}

module "fluxo_financeiro" {
  source           = "../../modules/service"
  environment_name = local.environment_name
  app_docker_image = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/fluxo-financeiro:${local.environment_name}"
  environment_variables = concat(local.quarkus_default_env, [
    {
      name  = "QUARKUS_DATASOURCE_USERNAME",
      value = "postgres"
    },
    {
      name  = "QUARKUS_DATASOURCE_PASSWORD",
      value = "ETFWCC9C1Y632Ja7mcQO"
    },
    {
      name  = "QUARKUS_DATASOURCE_JDBC_URL",
      value = "**************************************************************************************************"
    },
    {
      name  = "QUARKUS_DATASOURCE_CLIENTES_USERNAME",
      value = "postgres"
    },
    {
      name  = "QUARKUS_DATASOURCE_CLIENTES_PASSWORD",
      value = "ETFWCC9C1Y632Ja7mcQO"
    },
    {
      name  = "QUARKUS_DATASOURCE_CLIENTES_JDBC_URL",
      value = "***********************************************************************************************"
    }
  ])
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/staging/kafka/paytrack-kafka/broker/url/plaintext"
    },
    {
      name      = "COM_PAYTRACK_CLIENTES_TOKEN"
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/com/paytrack/clientes/token"
    },
    {
      name      = "COM_PAYTRACK_CLIENTES_TOKEN_CR_INTEGRAR_FATURADO_PAYTRACK"
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/com/paytrack/clientes/token/cr-integrar-faturado-paytrack"
    },
    {
      name      = "COM_PAYTRACK_CLIENTES_TOKEN_RECEITA_FECHAR_FATURAS"
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/com/paytrack/clientes/token/receita-fechar-faturas"
    },
    {
      name      = "COM_PAYTRACK_CLIENTES_TOKEN_PROCESSAR_REEMBOLSO_AUTOMATICO"
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/com/paytrack/clientes/token/processar-reembolso-automatico"
    },
    {
      name      = "COM_PAYTRACK_CLIENTES_TOKEN_CP_FECHAR_FATURA"
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/com/paytrack/clientes/token/cp-fechar-fatura"
    },
    {
      name      = "COM_PAYTRACK_CLIENTES_TOKEN_FATURAMENTO_AGENCIA_BUSCAR_FATURA_CR"
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/com/paytrack/clientes/token/faturamento-agencia-buscar-fatura-cr"
    },
    {
      name      = "COM_PAYTRACK_CLIENTES_TOKEN_FATURAMENTO_AGENCIA_BUSCAR_ITENS_REEMBOLSO"
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/com/paytrack/clientes/token/faturamento-agencia-buscar-itens-reembolso"
    },
    {
      name      = "COM_PAYTRACK_CLIENTES_TOKEN_FATURAMENTO_AGENCIA_BUSCAR_ITENS_CR"
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/com/paytrack/clientes/token/faturamento-agencia-buscar-itens-cr"
    },
    {
      name      = "COM_PAYTRACK_CLIENTES_TOKEN_FATURAMENTO_AGENCIA_BUSCAR_DADOS_COBRANCA"
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/com/paytrack/clientes/token/faturamento-agencia-buscar-dados-cobranca"
    }
  ]
  service_name = "fluxo-financeiro"

  public       = true
  target_arn   = module.api_gateway.target_group_arn
  listener_arn = module.api_gateway.listener_arn

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id

  gateway = {
    route              = "/fluxo-financeiro"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night = false
  shutdown_weekend  = true
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  docker_labels         = local.docker_labels
}

module "integrador_ehtl" {
  source                = "../../modules/service"
  environment_name      = local.environment_name
  app_docker_image      = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/integrador-ehtl:${local.environment_name}"
  environment_variables = local.quarkus_default_env
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/staging/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name = "integrador-ehtl"
  
  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props   = local.health_check_props_default

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  gateway = {
    route              = "/integrador-ehtl"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night = false
  shutdown_weekend  = true
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  docker_labels         = local.docker_labels
}

module "integrador_bee2pay" {
  source                = "../../modules/service"
  environment_name      = local.environment_name
  app_docker_image      = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/integrador-bee2pay:${local.environment_name}"
  environment_variables = local.quarkus_default_env
  service_name          = "integrador-bee2pay"
  
  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props   = local.health_check_props_default

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  gateway = {
    route              = "/integrador-bee2pay"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night = false
  shutdown_weekend  = true
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  docker_labels         = local.docker_labels
}

module "opa-server" {
  source           = "../../modules/service"
  environment_name = local.environment_name
  app_docker_image = "openpolicyagent/opa:0.70.0-static"
  app_port         = 8181
  command = [
    "run", "--server", "--log-level", "debug",
    "--set", "services.menu.url=https://paytrack-menu.s3.amazonaws.com",
    "--set", "services.menu.credentials.s3_signing.metadata_credentials.aws_region=us-east-1",
    "--set", "bundles.menu.service=menu",
    "--set", "bundles.menu.resource=${local.environment_name}/bundle.tar.gz",
    "--set", "bundles.menu.polling.min_delay_seconds=60",
    "--set", "bundles.menu.polling.max_delay_seconds=120"
  ]
  environment_variables = [
  ]
  service_name = "opa-server"

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  gateway = {
    route              = "/opa-server"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night = false
  shutdown_weekend  = true
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  docker_labels         = local.docker_labels
}

module "acesso" {
  source           = "../../modules/service"
  environment_name = local.environment_name
  app_docker_image = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/acesso:${local.environment_name}"
  environment_variables = concat(local.quarkus_default_env, [
    {
      name  = "QUARKUS_DATASOURCE__PAYTRACK_RO__USERNAME",
      value = "acesso"
    },
    {
      name  = "QUARKUS_DATASOURCE__PAYTRACK_RO__PASSWORD",
      value = "4H0o6<IqxQ6Q"
    },
    {
      name  = "QUARKUS_DATASOURCE__PAYTRACK_RO__REACTIVE_URL",
      value = "mysql://paytrack-hml.c4tposufhwhv.us-east-1.rds.amazonaws.com/autenticador"
    },
    {
      name  = "QUARKUS_REST_CLIENT_OPA_CLIENT_URL",
      value = "http://gateway.staging.paytrack.local/opa-server/"
    },
  ])
  service_name = "acesso"

  public       = true
  target_arn   = module.api_gateway.target_group_arn
  listener_arn = module.api_gateway.listener_arn

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  gateway = {
    route              = "/acesso"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night = false
  shutdown_weekend  = true
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  docker_labels         = local.docker_labels
}

module "configuracoes" {
  source           = "../../modules/service"
  environment_name = local.environment_name
  app_docker_image = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/configuracoes:${local.environment_name}"
  environment_variables = concat(local.quarkus_default_env, [
    {
      name  = "QUARKUS_DATASOURCE__PAYTRACK__USERNAME",
      value = "configuracoes"
    },
    {
      name  = "QUARKUS_DATASOURCE__PAYTRACK__PASSWORD",
      value = "E7r4|2SYg~7V"
    },
    {
      name  = "QUARKUS_DATASOURCE__PAYTRACK__JDBC_URL",
      value = "******************************************************************"
    },
    {
      name  = "AUTENTICADOR_ENV_URL",
      value = "http://auth-internal.staging.paytrack.com.br/autenticador/api"
    },
    {
      name  = "PAYTRACK_KAFKA_ENABLED",
      value = "true"
    },
  ])
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/staging/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name = "configuracoes"

  public       = true
  target_arn   = module.api_gateway.target_group_arn
  listener_arn = module.api_gateway.listener_arn

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  gateway = {
    route              = "/configuracoes"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night = false
  shutdown_weekend  = true
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  docker_labels         = local.docker_labels
}

module "solicitacoes" {
  source           = "../../modules/service"
  environment_name = local.environment_name
  app_docker_image = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/solicitacoes:${local.environment_name}"
  environment_variables = concat(local.quarkus_default_env, [
    {
      name  = "QUARKUS_DATASOURCE__PAYTRACK__USERNAME",
      value = "solicitacoes"
    },
    {
      name  = "QUARKUS_DATASOURCE__PAYTRACK__PASSWORD",
      value = "Ym&K846s#[ne"
    },
    {
      name  = "QUARKUS_DATASOURCE__PAYTRACK__JDBC_URL",
      value = "******************************************************************"
    },
  ])
  service_name = "solicitacoes"

  public       = true
  target_arn   = module.api_gateway.target_group_arn
  listener_arn = module.api_gateway.listener_arn

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  gateway = {
    route              = "/solicitacoes"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night = false
  shutdown_weekend  = true
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  docker_labels         = local.docker_labels
}

module "reserva_emissao" {
  source           = "../../modules/service"
  environment_name = local.environment_name
  app_docker_image = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/reserva-emissao:${local.environment_name}"
  environment_variables = concat(local.quarkus_default_env, [
    {
      name  = "QUARKUS_DATASOURCE_USERNAME",
      value = "reserva_emissao"
    },
    {
      name  = "QUARKUS_DATASOURCE_JDBC_URL",
      value = "***************************************************************************************************"
    }
  ])
  secrets = [
    {
      name      = "QUARKUS_DATASOURCE_PASSWORD",
      valueFrom = "arn:aws:secretsmanager:us-east-1:560211763190:secret:developer-reserva-emissao-app-7EJN0o"
    },
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/staging/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name = "reserva-emissao"

  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props   = local.health_check_props_default

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  gateway = {
    route              = "/reserva-emissao"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night = false
  shutdown_weekend  = true  
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  docker_labels         = local.docker_labels
}

module "integrador_wooba" {
  source                = "../../modules/service"
  environment_name      = local.environment_name
  app_docker_image      = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/integrador-wooba:${local.environment_name}"
  environment_variables = local.quarkus_default_env
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/staging/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name = "integrador-wooba"
    
  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props   = local.health_check_props_default

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  gateway = {
    route              = "/integrador-wooba"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night = false
  shutdown_weekend  = true  
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  docker_labels         = local.docker_labels
}

module "integrador_latam" {
  source                = "../../modules/service"
  environment_name      = local.environment_name
  app_docker_image      = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/integrador-latam:${local.environment_name}"
  environment_variables = local.quarkus_default_env
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/staging/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name = "integrador-latam"
    
  health_check_command = "curl -s -f http://localhost:8080/q/health"
  health_check_props   = local.health_check_props_default

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  gateway = {
    route              = "/integrador-latam"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night = false
  shutdown_weekend  = true  
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  docker_labels         = local.docker_labels
}

module "conciliacao_backend" {
  source           = "../../modules/service"
  environment_name = local.environment_name
  app_docker_image = "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/conciliacao-backend:${local.environment_name}"
  environment_variables = concat(local.quarkus_default_env, [
    {
      name  = "QUARKUS_DATASOURCE_USERNAME",
      value = "conciliacao-backend"
    },
    {
      name  = "QUARKUS_DATASOURCE_PASSWORD",
      value = "cJiflsOhrhrZJfzZ"
    },
    {
      name  = "QUARKUS_DATASOURCE_JDBC_URL",
      value = "******************************************************************/"
    },
    {
      name  = "KAFKA_GROUP_ID",
      value = "staging-conciliacao"
    },
    {
      name  = "QUARKUS_DATASOURCE_PAYTRACK_REPLICA_USERNAME",
      value = "conciliacao-backend"
    },
    {
      name  = "QUARKUS_DATASOURCE_PAYTRACK_REPLICA_PASSWORD",
      value = "cJiflsOhrhrZJfzZ"
    },
    {
      name  = "QUARKUS_DATASOURCE_PAYTRACK_REPLICA_JDBC_URL",
      value = "******************************************************************/"
    }
  ])
  secrets = [
    {
      name      = "KAFKA_BOOTSTRAP_SERVERS",
      valueFrom = "arn:aws:ssm:us-east-1:560211763190:parameter/staging/kafka/paytrack-kafka/broker/url/plaintext"
    }
  ]
  service_name = "conciliacao-backend"

  public       = true
  target_arn   = module.api_gateway.target_group_arn
  listener_arn = module.api_gateway.listener_arn

  app_mesh_name            = module.infra.mesh_id
  private_dns_namespace_id = module.infra.private_dns_id
  task_execution_iam_role  = module.infra.task_execution_iam_role
  task_iam_role            = module.infra.task_iam_role
  subnets                  = module.infra.private_subnets
  ecs_cluster              = module.infra.ecs_cluster
  security_group           = module.infra.security_group_id
  specifications           = local.medium
  gateway = {
    route              = "/conciliacao-backend"
    virtual_gateway_id = module.api_gateway.virtual_gateway_id
  }
  shutdown_at_night = false
  shutdown_weekend  = true  
  auto_scaling          = true
  auto_scaling_memory   = true
  auto_scaling_capacity = local.auto_scaling_default_capacity
  docker_labels         = local.docker_labels
}

