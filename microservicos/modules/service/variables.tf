variable "app_mesh_name" {
  type = string
}

variable "service_name" {
  type = string
}

variable "app_port" {
  type    = number
  default = 8080
}

variable "private_dns_namespace_id" {
  type = string
}

variable "task_iam_role" {
  type = string
}

variable "task_execution_iam_role" {
  type = string
}

variable "app_docker_image" {
  type = string
}

variable "envoy_image" {
  type    = string
  default = "840364872350.dkr.ecr.us-east-1.amazonaws.com/aws-appmesh-envoy:v1.22.2.1-prod"
}

variable "ecs_cluster" {
  type = string
}

variable "subnets" {
  type = list(string)
}

variable "environment_name" {
  type = string
}

variable "environment_variables" {
  type = list(object({
    name  = string
    value = string
  }))
  default = []
}

variable "docker_labels" {
  type = map
  default = {}
}

variable "secrets" {
  type = list(object({
    name  = string
    valueFrom = string
  }))
  default = []
}

variable "command" {
  type = list(string)
  default = []
}

variable "security_group" {
  type = string
}

variable "gateway" {
  type = object({
    route              = string
    virtual_gateway_id = string
  })
  default = {
    route              = ""
    virtual_gateway_id = ""
  }
}

variable "specifications" {
  type = object({
    cpu    = number
    memory = number
  })
  default = {
    cpu    = 256
    memory = 512
  }
}

variable "shutdown_at_night" {
  type    = bool
  default = true
}

variable "shutdown_weekend" {
  type    = bool
  default = false
}

variable "public" {
  type    = bool
  default = false
}

variable "listener_arn" {
  type = string
  default = null
}

variable "target_arn" {
  type = string
  default = null
}

variable "auto_scaling" {
  type    = bool
  default = false
}

variable "auto_scaling_memory" {
  type    = bool
  default = false
}

variable "auto_scaling_capacity" {
  type = object({
    min = number
    max  = number
  })
  default = {
    min = 1,
    max = 2
  }
}

variable "health_check_props" {
  type = object({
    interval = number
    timeout  = number
    retries  = number
    startPeriod = number
  })
  default = {
    interval = 5,
    timeout = 10,
    retries = 10,
    startPeriod = null
  }
}
variable "health_check_command" {
  type = string
  default = null
}

variable "replicas" {
  type = number
  default = 1
}

variable "collector_proxy_enabled" {
  type        = bool
  default     = false
  description = "Habilita o sidecar collector-proxy para coleta de requisições HTTP"
}

variable "collector_proxy_s3_bucket_name" {
  type        = string
  default     = null
  description = "Nome do bucket S3 compartilhado para coleta de requisições HTTP. Se null, usa o padrão: paytrack-{environment_name}-http-request-collector"
}

variable "collector_proxy_iam_policy_arn" {
  type        = string
  default     = null
  description = "ARN da política IAM compartilhada para acesso ao S3. Se null, usa o padrão: arn:aws:iam::account:policy/{environment_name}-collector-proxy-s3-policy"
}