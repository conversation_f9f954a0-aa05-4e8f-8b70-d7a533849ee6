provider "aws" {
  default_tags {
    tags = {
      Name     = var.service_name
      Ambiente = var.environment_name
    }
  }
}

provider "aws" {
  alias   = "main_account"
  profile = "main_account"
}

locals {
  serviceDns      = "${var.service_name}.${var.environment_name}.mesh.local"
  nodeDns         = "${var.environment_name}.paytrack.local"
  create_gw_route = var.gateway.route != "" && var.gateway.virtual_gateway_id != ""
  containerHealthCheck = var.health_check_command == null ? null : jsonencode({
    "command" = [
      "CMD-SHELL",
      "${var.health_check_command} || exit 1"
    ],
    "interval" = var.health_check_props.interval
    "timeout"  = var.health_check_props.timeout
    "retries"  = var.health_check_props.retries
    "startPeriod"  = var.health_check_props.startPeriod
  })

  # Collector-proxy defaults
  collector_proxy_s3_bucket_name = var.collector_proxy_s3_bucket_name != null ? var.collector_proxy_s3_bucket_name : "paytrack-${var.environment_name}-http-request-collector"
  collector_proxy_iam_policy_arn = var.collector_proxy_iam_policy_arn != null ? var.collector_proxy_iam_policy_arn : "arn:aws:iam::${data.aws_caller_identity.current.account_id}:policy/${var.environment_name}-collector-proxy-s3-policy"
}

data "aws_region" "current" {}

data "aws_caller_identity" "current" {}

resource "aws_cloudwatch_log_group" "log_group" {
  name              = "${var.service_name}-${var.environment_name}-log-group"
  retention_in_days = 30
  
  tags = {
    Name     = "${var.service_name}-${var.environment_name}-log-group"
    Ambiente = var.environment_name
  }
}

resource "aws_service_discovery_service" "service_registry" {
  name = var.service_name
  dns_config {
    namespace_id = var.private_dns_namespace_id
    dns_records {
      type = "A"
      ttl  = 300
    }
  }
  health_check_custom_config {
    failure_threshold = 1
  }
}

resource "aws_appmesh_virtual_node" "mesh_node" {
  name      = var.service_name
  mesh_name = var.app_mesh_name

  spec {
    listener {
      port_mapping {
        port     = var.app_port
        protocol = "http"
      }

      timeout {
        http {
          per_request {
            unit  = "s"
            value = 120
          }
        }
      }
    }

    service_discovery {
      aws_cloud_map {
        namespace_name = local.nodeDns
        service_name   = aws_service_discovery_service.service_registry.name
        attributes = {
          "ECS_TASK_DEFINITION_FAMILY" = "${var.environment_name}-${var.service_name}"
        }
      }
    }

  }
}

resource "aws_ecs_task_definition" "ecs_service_definition" {
  requires_compatibilities = ["FARGATE"]
  family                   = "${var.environment_name}-${var.service_name}"
  network_mode             = "awsvpc"
  cpu                      = var.specifications.cpu
  memory                   = var.specifications.memory
  task_role_arn            = var.task_iam_role
  execution_role_arn       = var.task_execution_iam_role
  proxy_configuration {
    type           = "APPMESH"
    container_name = "envoy"
    properties = {
      "IgnoredUID"       = "1337"
      "ProxyIngressPort" = "15000"
      "ProxyEgressPort"  = "15001"
      "AppPorts"         = var.app_port
      "EgressIgnoredIPs" = "*************,***************"
    }
  }
  container_definitions = <<TASK_DEFINITION
  [
    {
      "name": "app",
      "image": "${var.app_docker_image}",
      "command": ${jsonencode(var.command)},
      "essential": true,
      "dependsOn": [
        {
          "containerName": "envoy",
          "condition": "HEALTHY"
        }%{ if var.collector_proxy_enabled },
        {
          "containerName": "collector-proxy",
          "condition": "HEALTHY"
        }%{ endif }
      ],
      %{ if local.containerHealthCheck != null }"healthCheck": ${local.containerHealthCheck},%{ endif }
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "${aws_cloudwatch_log_group.log_group.name}",
          "awslogs-region": "${data.aws_region.current.id}",
          "awslogs-stream-prefix": "${var.service_name}"
        }
      },
      "portMappings": [
        {
          "containerPort": ${var.app_port},
          "hostPort": ${var.app_port},
          "protocol": "tcp"
        }
      ],
      "environment": ${jsonencode(
        concat(var.environment_variables, var.collector_proxy_enabled ? [
          {
            "name": "COLLECTOR_PROXY_ENABLED",
            "value": "true"
          }
        ] : [])
      )},
      "secrets": ${jsonencode(var.secrets)},
      "dockerLabels": ${jsonencode(var.docker_labels)}
    },
    {
      "name": "envoy",
      "image": "${var.envoy_image}",
      "essential": true,
      "user": "1337",
      "ulimits": [
        {
          "name": "nofile",
          "hardLimit": 15000,
          "softLimit": 15000
        }
      ],
      "portMappings": [
        {
          "containerPort": 9901,
          "protocol": "tcp"
        },
        {
          "containerPort": 15000,
          "protocol": "tcp"
        },
        {
          "containerPort": 15001,
          "protocol": "tcp"
        }
      ],
      "healthCheck": {
        "command": [
          "CMD-SHELL",
          "curl -s http://localhost:9901/server_info | grep state | grep -q LIVE"
        ],
        "interval": 5,
        "timeout": 10,
        "retries": 10
      },
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "${aws_cloudwatch_log_group.log_group.name}",
          "awslogs-region": "${data.aws_region.current.id}",
          "awslogs-stream-prefix": "${var.service_name}"
        }
      },
      "environment": [
        {
          "name": "APPMESH_RESOURCE_ARN",
          "value": "${aws_appmesh_virtual_node.mesh_node.arn}"
        }
      ]
    }%{ if var.collector_proxy_enabled },
    {
      "name": "collector-proxy",
      "image": "393346304284.dkr.ecr.us-east-1.amazonaws.com/paytrack/collector-proxy:${var.environment_name}",
      "essential": true,
      "portMappings": [
        {
          "containerPort": 11000,
          "hostPort": 11000,
          "protocol": "tcp"
        },
        {
          "containerPort": 11001,
          "hostPort": 11001,
          "protocol": "tcp"
        }
      ],
      "healthCheck": {
        "command": [
          "CMD-SHELL",
          "curl -s -f http://localhost:11001/health || exit 1"
        ],
        "interval": 10,
        "timeout": 5,
        "retries": 3,
        "startPeriod": 30
      },
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "${aws_cloudwatch_log_group.collector_proxy_log_group[0].name}",
          "awslogs-region": "${data.aws_region.current.id}",
          "awslogs-stream-prefix": "collector-proxy"
        }
      },
      "environment": [
        {
          "name": "S3_BUCKET_NAME",
          "value": "${local.collector_proxy_s3_bucket_name}"
        },
        {
          "name": "JSON_LOGGING",
          "value": "true"
        }
      ]
    }%{ endif }
  ]
TASK_DEFINITION
}

resource "aws_ecs_service" "ecs_service" {
  launch_type = null
  capacity_provider_strategy {
    base              = 1
    weight            = 100
    capacity_provider = "FARGATE_SPOT"
  }

  force_new_deployment = true
  name                 = var.service_name
  depends_on = [
    aws_service_discovery_service.service_registry
  ]
  cluster                            = var.ecs_cluster
  deployment_maximum_percent         = 200
  deployment_minimum_healthy_percent = 100
  desired_count                      = var.replicas
  enable_execute_command             = true
  propagate_tags                     = "SERVICE"
  service_registries {
    registry_arn = aws_service_discovery_service.service_registry.arn
  }
  network_configuration {
    assign_public_ip = false
    subnets          = var.subnets
    security_groups  = [var.security_group]
  }
  task_definition = aws_ecs_task_definition.ecs_service_definition.arn

  tags = {
    Name     = var.service_name
    Ambiente = var.environment_name
  }
  
}

resource "aws_appmesh_virtual_router" "mesh_virtual_router" {
  name      = var.service_name
  mesh_name = var.app_mesh_name
  spec {
    listener {
      port_mapping {
        port     = var.app_port
        protocol = "http"
      }
    }
  }
}

resource "aws_appmesh_route" "mesh_default_route" {
  name                = "${var.service_name}-route"
  mesh_name           = var.app_mesh_name
  virtual_router_name = aws_appmesh_virtual_router.mesh_virtual_router.name
  spec {
    http_route {
      match {
        prefix = "/"
      }
      action {
        weighted_target {
          virtual_node = aws_appmesh_virtual_node.mesh_node.name
          weight       = 100
        }
      }
      timeout {
        per_request {
          unit  = "s"
          value = 120
        }
      }
    }
  }
}

resource "aws_appmesh_virtual_service" "mesh_service" {
  name      = local.serviceDns
  mesh_name = var.app_mesh_name

  spec {
    provider {
      virtual_router {
        virtual_router_name = aws_appmesh_virtual_router.mesh_virtual_router.name
      }
    }
  }
}

resource "aws_appmesh_gateway_route" "gateway_route" {
  count                = local.create_gw_route ? 1 : 0
  name                 = "${var.service_name}-route"
  mesh_name            = var.app_mesh_name
  virtual_gateway_name = var.gateway.virtual_gateway_id

  spec {
    http_route {
      match {
        prefix = var.gateway.route
      }
      action {
        target {
          virtual_service {
            virtual_service_name = aws_appmesh_virtual_service.mesh_service.name
          }
        }

        rewrite {
          hostname {
            default_target_hostname = "DISABLED"
          }
        }
      }
    }
  }
}

resource "aws_appautoscaling_target" "ecs_unified" {
  count = (var.auto_scaling || var.shutdown_at_night || var.shutdown_weekend) ? 1 : 0

  max_capacity = var.auto_scaling ? var.auto_scaling_capacity.max : aws_ecs_service.ecs_service.desired_count
  min_capacity = var.auto_scaling ? var.auto_scaling_capacity.min : aws_ecs_service.ecs_service.desired_count

  resource_id        = "service/${var.environment_name}/${aws_ecs_service.ecs_service.name}"
  scalable_dimension = "ecs:service:DesiredCount"
  service_namespace  = "ecs"

  lifecycle {
    ignore_changes = [tags, tags_all]
  }
}

resource "aws_appautoscaling_scheduled_action" "ecs_comercial_off" {
  count              = var.shutdown_at_night ? 1 : 0
  name               = "${var.environment_name}-${var.service_name}-comercial-off"
  service_namespace  = aws_appautoscaling_target.ecs_unified[0].service_namespace
  resource_id        = aws_appautoscaling_target.ecs_unified[0].resource_id
  scalable_dimension = aws_appautoscaling_target.ecs_unified[0].scalable_dimension
  schedule           = "cron(0 22 ? * MON-FRI *)"
  timezone           = "America/Sao_Paulo"

  scalable_target_action {
    min_capacity = 0
    max_capacity = 0
  }
}

resource "aws_appautoscaling_policy" "appautoscaling_policy" {
  count              = var.auto_scaling ? 1 : 0
  policy_type        = "TargetTrackingScaling"
  name               = "cpu-auto-scaling"
  resource_id        = aws_appautoscaling_target.ecs_unified[0].resource_id
  scalable_dimension = aws_appautoscaling_target.ecs_unified[0].scalable_dimension
  service_namespace  = aws_appautoscaling_target.ecs_unified[0].service_namespace

  target_tracking_scaling_policy_configuration {
    predefined_metric_specification {
      predefined_metric_type = "ECSServiceAverageCPUUtilization"
    }
    target_value = 70
  }
}

resource "aws_appautoscaling_policy" "appautoscaling_memory_policy" {
  count              = var.auto_scaling_memory ? 1 : 0
  policy_type        = "TargetTrackingScaling"
  name               = "memory-auto-scaling"
  resource_id        = aws_appautoscaling_target.ecs_unified[0].resource_id
  scalable_dimension = aws_appautoscaling_target.ecs_unified[0].scalable_dimension
  service_namespace  = aws_appautoscaling_target.ecs_unified[0].service_namespace

  target_tracking_scaling_policy_configuration {
    predefined_metric_specification {
      predefined_metric_type = "ECSServiceAverageMemoryUtilization"
    }
    target_value = 80
  }
}

resource "aws_appautoscaling_scheduled_action" "ecs_comercial_on" {
  depends_on = [
    aws_appautoscaling_scheduled_action.ecs_comercial_off
  ]
  count              = var.shutdown_at_night ? 1 : 0
  name               = "${var.environment_name}-${var.service_name}-comercial-on"
  service_namespace  = aws_appautoscaling_target.ecs_unified[0].service_namespace
  resource_id        = aws_appautoscaling_target.ecs_unified[0].resource_id
  scalable_dimension = aws_appautoscaling_target.ecs_unified[0].scalable_dimension
  schedule           = "cron(0 6 ? * MON-FRI *)"
  timezone           = "America/Sao_Paulo"

  scalable_target_action {
    min_capacity = aws_ecs_service.ecs_service.desired_count
    max_capacity = aws_ecs_service.ecs_service.desired_count
  }
}

resource "aws_lb_listener_rule" "static" {

  count        = var.public ? 1 : 0
  listener_arn = var.listener_arn

  action {
    type             = "forward"
    target_group_arn = var.target_arn
  }

  condition {
    path_pattern {
      values = ["${var.gateway.route}*"]
    }
  }
}

resource "aws_appautoscaling_scheduled_action" "week_on" {
  depends_on = [
    aws_appautoscaling_scheduled_action.weekend_off
  ]
  count              = var.shutdown_weekend ? 1 : 0
  name               = "${var.environment_name}-${var.service_name}-week-on"
  service_namespace  = aws_appautoscaling_target.ecs_unified[0].service_namespace
  resource_id        = aws_appautoscaling_target.ecs_unified[0].resource_id
  scalable_dimension = aws_appautoscaling_target.ecs_unified[0].scalable_dimension
  schedule           = "cron(30 6 ? * MON *)"
  timezone           = "America/Sao_Paulo"

  scalable_target_action {
    min_capacity = aws_ecs_service.ecs_service.desired_count
    max_capacity = aws_ecs_service.ecs_service.desired_count
  }
}

resource "aws_appautoscaling_scheduled_action" "weekend_off" {
  count              = var.shutdown_weekend ? 1 : 0
  name               = "${var.environment_name}-${var.service_name}-weekend-off"
  service_namespace  = aws_appautoscaling_target.ecs_unified[0].service_namespace
  resource_id        = aws_appautoscaling_target.ecs_unified[0].resource_id
  scalable_dimension = aws_appautoscaling_target.ecs_unified[0].scalable_dimension
  schedule           = "cron(30 20 ? * FRI *)"
  timezone           = "America/Sao_Paulo"

  scalable_target_action {
    min_capacity = 0
    max_capacity = 0
  }
}

resource "aws_appautoscaling_target" "ecs2" {
  count              = var.shutdown_weekend ? 1 : 0
  max_capacity       = aws_ecs_service.ecs_service.desired_count
  min_capacity       = aws_ecs_service.ecs_service.desired_count
  resource_id        = "service/${var.environment_name}/${aws_ecs_service.ecs_service.name}"
  scalable_dimension = "ecs:service:DesiredCount"
  service_namespace  = "ecs"
}
