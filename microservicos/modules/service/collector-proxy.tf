# ==============================================================================
# COLLECTOR PROXY SIDECAR - RECURSOS ESPECÍFICOS POR SERVIÇO
# ==============================================================================
#
# Este arquivo contém apenas os recursos específicos por serviço do collector-proxy.
# Os recursos compartilhados (S3 buckets, IAM policies) são criados pelo módulo
# collector-proxy dedicado e referenciados via variáveis.
#
# RECURSOS ESPECÍFICOS POR SERVIÇO:
# - CloudWatch Log Groups (isolados por serviço)
# - IAM Policy Attachment (anexa política compartilhada ao role do serviço)

# ------------------------------------------------------------------------------
# Grupo de Log do CloudWatch para Collector Proxy
# ------------------------------------------------------------------------------
# Cria um CloudWatch Log Group específico para cada serviço que utiliza o 
# collector-proxy sidecar. Este log group é isolado por serviço para facilitar
# debugging e monitoramento independente.
# Retenção: 30 dias (configurável)
resource "aws_cloudwatch_log_group" "collector_proxy_log_group" {
  count             = var.collector_proxy_enabled ? 1 : 0
  name              = "${var.service_name}-${var.environment_name}-collector-proxy-log-group"

  lifecycle {
    prevent_destroy = true
  }

  retention_in_days = 30
  
  tags = {
    Name        = "${var.service_name}-${var.environment_name}-collector-proxy-log-group"
    Description = "CloudWatch Log Group para sidecar collector-proxy do serviço ${var.service_name} no ambiente ${var.environment_name}"
    Ambiente    = var.environment_name
    Service     = var.service_name
    Component   = "collector-proxy"
    Purpose     = "HTTP Request Collection Logging"
  }
}

# ------------------------------------------------------------------------------
# IAM Policy Attachment para Task ECS
# ------------------------------------------------------------------------------
# Anexa a política IAM compartilhada (criada pelo módulo collector-proxy) ao 
# IAM Role da ECS Task, permitindo que o container collector-proxy tenha 
# permissões para gravar no S3 compartilhado.
resource "aws_iam_role_policy_attachment" "collector_proxy_s3_policy_attachment" {
  count      = var.collector_proxy_enabled ? 1 : 0
  role       = basename(var.task_iam_role)
  policy_arn = local.collector_proxy_iam_policy_arn
} 