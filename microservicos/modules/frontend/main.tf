provider "aws" {
  default_tags {
    tags = {
      Name     = local.api
      Ambiente = var.environment_name
    }
  }
}

locals {
  api     = var.environment_name == "prod" ? "${var.project_name}.paytrack.com.br" : "${var.project_name}.${var.environment_name}.paytrack.com.br"
  aliases = concat(var.others_aliases, [local.api])
}

resource "aws_s3_bucket" "s3_bucket" {
  bucket = "${var.environment_name}-${var.project_name}"

  tags = {
    Name     = "${var.environment_name}-${var.project_name}"
    Ambiente = var.environment_name
  }  
}

resource "aws_cloudfront_origin_access_identity" "origin_access_identity" {
  comment = "${var.environment_name}-${var.project_name}.s3.amazonaws.com"
}

resource "aws_cloudfront_distribution" "cloudfront_distribution" {
  aliases             = local.aliases
  default_root_object = "index.html"
  enabled             = true
  http_version        = "http2"
  is_ipv6_enabled     = true
  price_class         = "PriceClass_All"
  retain_on_delete    = false

  wait_for_deployment = true

  custom_error_response {
    error_caching_min_ttl = 10
    error_code            = 403
    response_code         = 200
    response_page_path    = "/index.html"
  }
  custom_error_response {
    error_caching_min_ttl = 10
    error_code            = 404
    response_code         = 200
    response_page_path    = "/index.html"
  }

  default_cache_behavior {
    allowed_methods = [
      "DELETE",
      "GET",
      "HEAD",
      "OPTIONS",
      "PATCH",
      "POST",
      "PUT",
    ]
    cached_methods = [
      "GET",
      "HEAD",
    ]
    compress               = true
    default_ttl            = 86400
    max_ttl                = 31536000
    smooth_streaming       = false
    target_origin_id       = "S3-${aws_s3_bucket.s3_bucket.bucket}.paytrack.com.br"
    viewer_protocol_policy = "redirect-to-https"

    forwarded_values {
      query_string = false

      cookies {
        forward = "none"
      }
    }
  }

  origin {
    connection_attempts = 3
    connection_timeout  = 10
    domain_name         = aws_s3_bucket.s3_bucket.bucket_domain_name
    origin_id           = "S3-${aws_s3_bucket.s3_bucket.bucket}.paytrack.com.br"
    origin_path         = ""
    s3_origin_config {
      origin_access_identity = aws_cloudfront_origin_access_identity.origin_access_identity.cloudfront_access_identity_path
    }
  }

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  viewer_certificate {
    acm_certificate_arn            = var.certificate_arn
    cloudfront_default_certificate = false
    minimum_protocol_version       = "TLSv1.2_2019"
    ssl_support_method             = "sni-only"
  }
}

resource "aws_route53_record" "route53_record" {
  name    = local.api
  type    = "A"
  zone_id = var.public_hosted_zone_id

  alias {
    name                   = aws_cloudfront_distribution.cloudfront_distribution.domain_name
    zone_id                = aws_cloudfront_distribution.cloudfront_distribution.hosted_zone_id
    evaluate_target_health = false
  }
}

data "aws_iam_policy_document" "s3_policy" {
  statement {
    actions   = ["s3:GetObject"]
    resources = ["${aws_s3_bucket.s3_bucket.arn}/*"]

    principals {
      type        = "AWS"
      identifiers = [aws_cloudfront_origin_access_identity.origin_access_identity.iam_arn]
    }
  }
}

resource "aws_s3_bucket_policy" "s3_bucket_policy" {
  bucket = aws_s3_bucket.s3_bucket.id
  policy = data.aws_iam_policy_document.s3_policy.json
}


output "url_frontend" {
  value = aws_route53_record.route53_record.name
}
