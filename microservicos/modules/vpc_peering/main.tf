
// Quem for usar deve ter cuidado com conflito de ips entre as vpc
provider "aws" {
  profile = "default"
  default_tags {
    tags = {
      Name     = var.environment_name
      Ambiente = var.environment_name
    }
  }
}

provider "aws" {
  alias = "main_account"
  profile = "main_account"
  region  = "us-east-1"
  default_tags {
    tags = {
      Name     = var.environment_name
      Ambiente = var.environment_name
    }
  }
}

resource "aws_route53_vpc_association_authorization" "hosted_zone_association" {
  vpc_id  = var.target_data.vpc_id
  zone_id = var.origin_data.hosted_zone_id
}

resource "aws_route53_zone_association" "external_zone_association" {
  provider = aws.main_account

  vpc_id  = aws_route53_vpc_association_authorization.hosted_zone_association.vpc_id
  zone_id = aws_route53_vpc_association_authorization.hosted_zone_association.zone_id
}

data "aws_caller_identity" "external" {
  provider = aws.main_account
}

resource "aws_vpc_peering_connection" "peer_creation" {
  peer_owner_id = data.aws_caller_identity.external.account_id
  peer_vpc_id   = var.target_data.vpc_id
  vpc_id        = var.origin_data.vpc_id

}

resource "aws_vpc_peering_connection_accepter" "peer_accept" {
  provider                  = aws.main_account
  vpc_peering_connection_id = aws_vpc_peering_connection.peer_creation.id
  auto_accept               = true
}

// Ajustando route tables da conta destino
data "aws_route_tables" "external_route_tables" {
  provider = aws.main_account
  vpc_id = var.target_data.vpc_id
}

data "aws_vpc" "vpc" {
  id = var.origin_data.vpc_id
}

resource "aws_route" "peer_routes" {
  provider = aws.main_account
  count                     = length(data.aws_route_tables.external_route_tables.ids)
  route_table_id            = tolist(data.aws_route_tables.external_route_tables.ids)[count.index]
  destination_cidr_block    = data.aws_vpc.vpc.cidr_block
  vpc_peering_connection_id = aws_vpc_peering_connection.peer_creation.id

  lifecycle {
    ignore_changes = [
      route_table_id
    ]
  }
}

// Ajustando route table da conta origem
data "aws_route_tables" "route_tables" {
  vpc_id = var.origin_data.vpc_id
}

data "aws_vpc" "peer_vpc" {
  provider = aws.main_account
  id = var.target_data.vpc_id
}

resource "aws_route" "routes" {
  count                     = length(data.aws_route_tables.route_tables.ids)
  route_table_id            = tolist(data.aws_route_tables.route_tables.ids)[count.index]
  destination_cidr_block    = data.aws_vpc.peer_vpc.cidr_block
  vpc_peering_connection_id = aws_vpc_peering_connection.peer_creation.id

  lifecycle {
    ignore_changes = [
      route_table_id
    ]
  }
}