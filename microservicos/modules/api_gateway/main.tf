provider "aws" {
  default_tags {
    tags = {
      Name     = var.environment_name
      Ambiente = var.environment_name
    }
  }
}

data "aws_region" "current" {}

resource "aws_appmesh_virtual_gateway" "virtual_gateway" {
  name      = "${var.environment_name}-gw"
  mesh_name = var.mesh_name
  spec {
    listener {
      port_mapping {
        port     = 80
        protocol = "http"
      }
    }
  }
}

resource "aws_cloudwatch_log_group" "log_group_gateway" {
  name              = "${var.environment_name}-gateway"
  retention_in_days = 30

  tags = {
    Name     = "${var.environment_name}-gateway"
    Ambiente = var.environment_name
  }
}

resource "aws_ecs_task_definition" "ecs_service_definition" {
  requires_compatibilities = ["FARGATE"]
  family                   = "${var.environment_name}-gateway"
  network_mode             = "awsvpc"
  cpu                      = 256
  memory                   = 512
  task_role_arn            = var.iam_roles.task_arn
  execution_role_arn       = var.iam_roles.execution_arn
  container_definitions    = <<TASK_DEFINITION
  [
    {
      "name": "app",
      "image": "${var.envoy_image}",
      "essential": true,
      "ulimits": [
        {
          "name": "nofile",
          "hardLimit": 15000,
          "softLimit": 15000
        }
      ],
      "portMappings": [
        {
          "containerPort": 9901,
          "protocol": "tcp"
        },
        {
          "containerPort": ${var.port},
          "protocol": "tcp"
        }
      ],
      "healthCheck": {
        "command": [
          "CMD-SHELL",
          "curl -s http://localhost:9901/server_info | grep state | grep -q LIVE"
        ],
        "interval": 5,
        "timeout": 2,
        "retries": 3
      },
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "${aws_cloudwatch_log_group.log_group_gateway.name}",
          "awslogs-region": "${data.aws_region.current.id}",
          "awslogs-stream-prefix": "envoy"
        }
      },
      "environment": [
        {
          "name": "ENVOY_LOG_LEVEL",
          "value": "info"
        },
        {
          "name": "APPMESH_RESOURCE_ARN",
          "value": "${aws_appmesh_virtual_gateway.virtual_gateway.arn}"
        }
      ]
    }
  ]
TASK_DEFINITION
}

resource "aws_service_discovery_service" "gateway_registry" {
  name = "gateway"
  dns_config {
    namespace_id = var.private_dns_id
    dns_records {
      type = "A"
      ttl  = 300
    }
  }
  health_check_custom_config {
    failure_threshold = 1
  }
}
resource "aws_lb_target_group" "target_group" {
  name        = "${var.environment_name}-gateway"
  port        = var.port
  protocol    = "HTTP"
  target_type = "ip"
  vpc_id      = var.vpc_id
  health_check {
    matcher           = "404"
    interval          = 15
    healthy_threshold = 2
  }
}
resource "aws_ecs_service" "gateway_service" {
  name = "gateway"
  depends_on = [
    aws_service_discovery_service.gateway_registry
  ]
  cluster                            = var.ecs_cluster
  deployment_maximum_percent         = 200
  deployment_minimum_healthy_percent = 100
  desired_count                      = 1
  launch_type                        = "FARGATE"
  service_registries {
    registry_arn = aws_service_discovery_service.gateway_registry.arn
  }

  load_balancer {
    target_group_arn = aws_lb_target_group.target_group.arn
    container_name   = "app"
    container_port   = var.port
  }

  network_configuration {
    assign_public_ip = false
    subnets          = var.private_subnets
    security_groups  = var.security_groups
  }
  task_definition = aws_ecs_task_definition.ecs_service_definition.arn
}

// loadbalancer
resource "aws_security_group" "private_lb_sg" {
  description = "Entrada load balancer privado"
  name_prefix = "lb_sg"
  vpc_id      = var.vpc_id
}

resource "aws_security_group_rule" "private_lb_role" {
  type              = "ingress"
  security_group_id = aws_security_group.private_lb_sg.id
  cidr_blocks       = ["0.0.0.0/0"]
  protocol          = "tcp"
  from_port         = 80
  to_port           = 80
}

resource "aws_security_group_rule" "public_lb_role2" {
  type              = "egress"
  security_group_id = aws_security_group.private_lb_sg.id
  cidr_blocks       = ["0.0.0.0/0"]
  protocol          = -1
  from_port         = 0
  to_port           = 0
}

resource "aws_lb" "load_balancer" {
  name               = "private-${var.environment_name}"
  internal           = true
  load_balancer_type = "application"
  security_groups    = [aws_security_group.private_lb_sg.id]
  subnets            = var.private_subnets
  idle_timeout       = 120
}


resource "aws_lb_listener" "lb_listener" {
  depends_on = [
    aws_lb.load_balancer
  ]
  port              = 80
  protocol          = "HTTP"
  load_balancer_arn = aws_lb.load_balancer.arn

  default_action {
    type = "fixed-response"
    fixed_response {
      status_code  = 404
      content_type = "application/json"
    }
  }
}

resource "aws_apigatewayv2_vpc_link" "vpc_link" {
  name               = "api-gateway-${var.environment_name}-link"
  subnet_ids         = var.subnets
  security_group_ids = var.security_groups
}

resource "aws_apigatewayv2_api" "api" {
  name          = "${var.environment_name}-api"
  protocol_type = "HTTP"

  cors_configuration {
          allow_credentials = true
          allow_headers     = [
              "content-type",
              "x-dispositivo",
              "x-requested-with",
              "authorization"
            ]
          allow_methods     = [
              "DELETE",
              "GET",
              "HEAD",
              "OPTIONS",
              "PATCH",
              "POST",
              "PUT",
            ]
          allow_origins     = var.paytrack_urls
          expose_headers    = []
          max_age           = 0 
        }

}

resource "aws_apigatewayv2_stage" "stage" {
  api_id      = aws_apigatewayv2_api.api.id
  name        = "$default"
  auto_deploy = true

  default_route_settings {
    throttling_burst_limit = 5000
    throttling_rate_limit = 10000
  }

  access_log_settings {
    destination_arn = aws_cloudwatch_log_group.log_group_gateway.arn
    format          = jsonencode(
                {
                  httpMethod     = "$context.httpMethod"
                  ip             = "$context.identity.sourceIp"
                  protocol       = "$context.protocol"
                  requestId      = "$context.requestId"
                  requestTime    = "$context.requestTime"
                  responseLength = "$context.responseLength"
                  routeKey       = "$context.routeKey"
                  status         = "$context.status"
                }
            )
        }
}

resource "aws_apigatewayv2_integration" "loadbalancer_integration" {
  api_id             = aws_apigatewayv2_api.api.id
  integration_type   = "HTTP_PROXY"
  integration_uri    = aws_lb_listener.lb_listener.arn
  integration_method = "ANY"

  connection_type = "VPC_LINK"
  connection_id   = aws_apigatewayv2_vpc_link.vpc_link.id

  response_parameters {
    mappings = {
      "overwrite:header.Strict-Transport-Security" = "max-age=63072000",
      "overwrite:header.X-Content-Type-Options"    = "nosniff"
    }
    status_code = 200
  }
}

data "aws_iam_policy_document" "assume_role" {
  statement {
    effect = "Allow"

    principals {
      type        = "Service"
      identifiers = ["lambda.amazonaws.com"]
    }

    actions = ["sts:AssumeRole"]
  }
}

resource "aws_iam_role" "iam_for_lambda" {
  name               = "${var.environment_name}-iam_for_lambda"
  assume_role_policy = data.aws_iam_policy_document.assume_role.json
}

resource "aws_lambda_function" "lambda_authorizer" {
  function_name = "${var.environment_name}-lambda-authorizer"
  handler       = "lambda-authorizer"
  runtime       = "go1.x"
  filename      = "../../modules/api_gateway/lambda-authorizer.zip"
  role          = aws_iam_role.iam_for_lambda.arn
  memory_size   = 128
  timeout       = 10
  depends_on = [
    aws_iam_role_policy_attachment.lambda_logs
  ]
  lifecycle {
    ignore_changes = [
      filename, vpc_config, environment
    ]
  }
}

data "aws_iam_policy_document" "lambda_logging" {
  statement {
    effect = "Allow"

    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents",
    ]

    resources = ["arn:aws:logs:*:*:*"]
  }
}

resource "aws_iam_policy" "lambda_logging" {
  name        = "${var.environment_name}-lambda_logging"
  path        = "/"
  description = "IAM policy for logging from a lambda"
  policy      = data.aws_iam_policy_document.lambda_logging.json
}

resource "aws_iam_role_policy_attachment" "lambda_logs" {
  role       = aws_iam_role.iam_for_lambda.name
  policy_arn = aws_iam_policy.lambda_logging.arn
}

resource "aws_lambda_permission" "lambda_permission" {
  statement_id  = "AllowAPIGatewayExecution"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.lambda_authorizer.function_name
  principal     = "apigateway.amazonaws.com"

  # The /* part allows invocation from any stage, method and resource path
  # within API Gateway.
  source_arn = "${aws_apigatewayv2_api.api.execution_arn}/*"
}

resource "aws_apigatewayv2_authorizer" "authorizer" {
  api_id                            = aws_apigatewayv2_api.api.id
  authorizer_type                   = "REQUEST"
  name                              = "${var.environment_name}-lambda-authorizer"
  authorizer_payload_format_version = "2.0"
  enable_simple_responses           = false
  authorizer_uri                    = aws_lambda_function.lambda_authorizer.invoke_arn
  authorizer_result_ttl_in_seconds  = 0
}

resource "aws_apigatewayv2_route" "name" {
  api_id    = aws_apigatewayv2_api.api.id
  route_key = "ANY /{proxy+}"

  target             = "integrations/${aws_apigatewayv2_integration.loadbalancer_integration.id}"
  authorizer_id      = aws_apigatewayv2_authorizer.authorizer.id
  authorization_type = "CUSTOM"
}

resource "aws_apigatewayv2_route" "cors" {
  api_id    = aws_apigatewayv2_api.api.id
  route_key = "OPTIONS /{proxy+}"

  target = "integrations/${aws_apigatewayv2_integration.loadbalancer_integration.id}"
}

resource "aws_apigatewayv2_route" "clientes" {
  api_id    = aws_apigatewayv2_api.api.id
  route_key = "ANY /clientes/{proxy+}"

  target = "integrations/${aws_apigatewayv2_integration.loadbalancer_integration.id}"
}

resource "aws_apigatewayv2_route" "fluxo_financeiro" {
  api_id    = aws_apigatewayv2_api.api.id
  route_key = "ANY /fluxo-financeiro/{proxy+}"

  target = "integrations/${aws_apigatewayv2_integration.loadbalancer_integration.id}"
}

resource "aws_apigatewayv2_route" "convite" {
  api_id    = aws_apigatewayv2_api.api.id
  route_key = "ANY /convite/{proxy+}"

  target = "integrations/${aws_apigatewayv2_integration.loadbalancer_integration.id}"
}

resource "aws_apigatewayv2_domain_name" "api_custom_domain" {
  domain_name = var.environment_name == "prod" ? "api.paytrack.com.br" : "api.${var.environment_name}.paytrack.com.br"

  domain_name_configuration {
    certificate_arn = var.certificate_arn
    endpoint_type   = "REGIONAL"
    security_policy = "TLS_1_2"
  }
}

resource "aws_apigatewayv2_api_mapping" "api_mapping" {
  api_id      = aws_apigatewayv2_api.api.id
  domain_name = aws_apigatewayv2_domain_name.api_custom_domain.id
  stage       = aws_apigatewayv2_stage.stage.id
}

resource "aws_route53_record" "example" {
  name    = aws_apigatewayv2_domain_name.api_custom_domain.domain_name
  type    = "A"
  zone_id = var.route53_zone_id

  alias {
    name                   = aws_apigatewayv2_domain_name.api_custom_domain.domain_name_configuration[0].target_domain_name
    zone_id                = aws_apigatewayv2_domain_name.api_custom_domain.domain_name_configuration[0].hosted_zone_id
    evaluate_target_health = false
  }
}


resource "aws_appautoscaling_target" "ecs" {
  count              = var.shutdown_at_night ? 1 : 0
  max_capacity       = aws_ecs_service.gateway_service.desired_count
  min_capacity       = aws_ecs_service.gateway_service.desired_count
  resource_id        = "service/${var.environment_name}/${aws_ecs_service.gateway_service.name}"
  scalable_dimension = "ecs:service:DesiredCount"
  service_namespace  = "ecs"
}

resource "aws_appautoscaling_scheduled_action" "ecs_comercial_off" {
  count              = var.shutdown_at_night ? 1 : 0
  name               = "${var.environment_name}-gateway-comercial-off"
  service_namespace  = aws_appautoscaling_target.ecs[count.index].service_namespace
  resource_id        = aws_appautoscaling_target.ecs[count.index].resource_id
  scalable_dimension = aws_appautoscaling_target.ecs[count.index].scalable_dimension
  schedule           = "cron(0 22 ? * MON-FRI *)"
  timezone           = "America/Sao_Paulo"

  scalable_target_action {
    min_capacity = 0
    max_capacity = 0
  }
}

resource "aws_appautoscaling_scheduled_action" "ecs_comercial_on" {
  depends_on = [
    aws_appautoscaling_scheduled_action.ecs_comercial_off
  ]
  count              = var.shutdown_at_night ? 1 : 0
  name               = "${var.environment_name}-gateway-comercial-on"
  service_namespace  = aws_appautoscaling_target.ecs[count.index].service_namespace
  resource_id        = aws_appautoscaling_target.ecs[count.index].resource_id
  scalable_dimension = aws_appautoscaling_target.ecs[count.index].scalable_dimension
  schedule           = "cron(0 6 ? * MON-FRI *)"
  timezone           = "America/Sao_Paulo"

  scalable_target_action {
    min_capacity = aws_ecs_service.gateway_service.desired_count
    max_capacity = aws_ecs_service.gateway_service.desired_count
  }
}

output "api_gateway_info" {
  value = {
    id = aws_apigatewayv2_api.api.id
    route_target = "integrations/${aws_apigatewayv2_integration.loadbalancer_integration.id}"
    authorizer_id = aws_apigatewayv2_authorizer.authorizer.id
  }
}

output "virtual_gateway_id" {
  value = aws_appmesh_virtual_gateway.virtual_gateway.name
}

output "target_group_arn" {
  value = aws_lb_target_group.target_group.arn
}


output "listener_arn" {
  value = aws_lb_listener.lb_listener.arn
}


resource "aws_appautoscaling_scheduled_action" "weekend_off" {
  count              = var.shutdown_weekend ? 1 : 0
  name               = "${var.environment_name}-gateway-comercial-off"
  service_namespace  = aws_appautoscaling_target.ecs2[count.index].service_namespace
  resource_id        = aws_appautoscaling_target.ecs2[count.index].resource_id
  scalable_dimension = aws_appautoscaling_target.ecs2[count.index].scalable_dimension
  schedule           = "cron(30 20 ? * FRI *)"
  timezone           = "America/Sao_Paulo"

  scalable_target_action {
    min_capacity = 0
    max_capacity = 0
  }
}

resource "aws_appautoscaling_scheduled_action" "week_on" {
  depends_on = [
    aws_appautoscaling_scheduled_action.weekend_off
  ]
  count              = var.shutdown_weekend ? 1 : 0
  name               = "${var.environment_name}-gateway-comercial-on"
  service_namespace  = aws_appautoscaling_target.ecs2[count.index].service_namespace
  resource_id        = aws_appautoscaling_target.ecs2[count.index].resource_id
  scalable_dimension = aws_appautoscaling_target.ecs2[count.index].scalable_dimension
  schedule           = "cron(30 6 ? * MON *)"
  timezone           = "America/Sao_Paulo"

  scalable_target_action {
    min_capacity = aws_ecs_service.gateway_service.desired_count
    max_capacity = aws_ecs_service.gateway_service.desired_count
  }
}

resource "aws_appautoscaling_target" "ecs2" {
  count              = var.shutdown_weekend ? 1 : 0
  max_capacity       = aws_ecs_service.gateway_service.desired_count
  min_capacity       = aws_ecs_service.gateway_service.desired_count
  resource_id        = "service/${var.environment_name}/${aws_ecs_service.gateway_service.name}"
  scalable_dimension = "ecs:service:DesiredCount"
  service_namespace  = "ecs"
}