variable "environment_name" {
  type = string
}

variable "subnets" {
  type = list(string)
}

variable "security_groups" {
  type = list(string)
}

variable "certificate_arn" {
  type = string
}

variable "route53_zone_id" {
  type = string
}

variable "vpc_id" {
  type = string
}


variable "private_subnets" {
  type = list(string)
}

variable "port" {
  type    = number
  default = 80
}

variable "envoy_image" {
  type    = string
  default = "840364872350.dkr.ecr.us-east-1.amazonaws.com/aws-appmesh-envoy:v1.22.2.1-prod"
}

variable "private_dns_id" {
  type = string
}

variable "ecs_cluster" {
  type = string
}

variable "iam_roles" {
  type = object({
    task_arn      = string
    execution_arn = string
  })
}

variable "mesh_name" {
  type = string
}

variable "shutdown_at_night" {
  type = bool
  default = true
}

variable "shutdown_weekend" {
  type = bool
  default = false
}

variable "paytrack_urls" {
  type    = list(string)
}
