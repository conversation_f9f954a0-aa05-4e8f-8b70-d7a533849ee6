provider "aws" {
  default_tags {
    tags = {
      Name     = var.environment_name
      Ambiente = var.environment_name
    }
  }
}

provider "aws" {
  alias   = "main_account"
  profile = "main_account"
  default_tags {
    tags = {
      Name     = var.environment_name
      Ambiente = var.environment_name
    }
  }
}

data "aws_availability_zones" "available" {
  state = "available"
}

resource "aws_ecs_cluster" "ecs_cluster" {
  name = var.environment_name
}

resource "aws_ecs_cluster_capacity_providers" "fargate_spot" {
  cluster_name = aws_ecs_cluster.ecs_cluster.name

  capacity_providers = ["FARGATE_SPOT", "FARGATE"]

  default_capacity_provider_strategy {
    base              = 1
    weight            = 100
    capacity_provider = "FARGATE_SPOT"
  }
}

resource "aws_vpc" "vpc" {
  cidr_block           = var.vpc_cidr
  enable_dns_hostnames = true
}

resource "aws_internet_gateway" "igw" {
}

resource "aws_internet_gateway_attachment" "igw_attchment" {
  internet_gateway_id = aws_internet_gateway.igw.id
  vpc_id              = aws_vpc.vpc.id
}

resource "aws_subnet" "privateSubnet1" {
  vpc_id                  = aws_vpc.vpc.id
  availability_zone       = data.aws_availability_zones.available.names[0]
  map_public_ip_on_launch = false
  cidr_block              = var.private_subnet1_cidr
}

resource "aws_subnet" "privateSubnet2" {
  vpc_id                  = aws_vpc.vpc.id
  availability_zone       = data.aws_availability_zones.available.names[1]
  map_public_ip_on_launch = false
  cidr_block              = var.private_subnet2_cidr
}

resource "aws_subnet" "publicSubnet1" {
  vpc_id                  = aws_vpc.vpc.id
  availability_zone       = data.aws_availability_zones.available.names[0]
  map_public_ip_on_launch = true
  cidr_block              = var.public_subnet1_cidr
}

resource "aws_subnet" "publicSubnet2" {
  vpc_id                  = aws_vpc.vpc.id
  availability_zone       = data.aws_availability_zones.available.names[1]
  map_public_ip_on_launch = true
  cidr_block              = var.public_subnet2_cidr
}

resource "aws_eip" "natGateway1EIP" {
  depends_on = [
    aws_internet_gateway_attachment.igw_attchment
  ]
  domain = "vpc"
}

resource "aws_nat_gateway" "natGateway1" {
  allocation_id = aws_eip.natGateway1EIP.allocation_id
  subnet_id     = aws_subnet.publicSubnet1.id
}

resource "aws_route_table" "public_route_table" {
  vpc_id = aws_vpc.vpc.id
}

resource "aws_route" "public_default_route" {
  route_table_id         = aws_route_table.public_route_table.id
  destination_cidr_block = "0.0.0.0/0"
  gateway_id             = aws_internet_gateway.igw.id
}

resource "aws_route_table_association" "public_subnet1_association" {
  route_table_id = aws_route_table.public_route_table.id
  subnet_id      = aws_subnet.publicSubnet1.id
}

resource "aws_route_table_association" "public_subnet2_association" {
  route_table_id = aws_route_table.public_route_table.id
  subnet_id      = aws_subnet.publicSubnet2.id
}

resource "aws_route_table" "privateRouteTable1" {
  vpc_id = aws_vpc.vpc.id
}

resource "aws_route" "private_default_route1" {
  route_table_id         = aws_route_table.privateRouteTable1.id
  destination_cidr_block = "0.0.0.0/0"
  nat_gateway_id         = aws_nat_gateway.natGateway1.id
}

resource "aws_route_table_association" "defaultRouteTable1" {
  route_table_id = aws_route_table.privateRouteTable1.id
  subnet_id      = aws_subnet.privateSubnet1.id
}

resource "aws_route_table" "privateRouteTable2" {
  vpc_id = aws_vpc.vpc.id
}

resource "aws_route" "private_default_route2" {
  route_table_id         = aws_route_table.privateRouteTable2.id
  destination_cidr_block = "0.0.0.0/0"
  nat_gateway_id         = aws_nat_gateway.natGateway1.id
}

resource "aws_route_table_association" "defaultRouteTable2" {
  route_table_id = aws_route_table.privateRouteTable2.id
  subnet_id      = aws_subnet.privateSubnet2.id
}

resource "aws_security_group" "security_group" {
  vpc_id = aws_vpc.vpc.id
}

resource "aws_security_group_rule" "security_group_ingress_rule" {
  type              = "ingress"
  security_group_id = aws_security_group.security_group.id
  cidr_blocks       = [var.vpc_cidr, "**********/12"] // Valor fixo das nossas redes internas
  protocol          = -1
  from_port         = 0
  to_port           = 0
}

resource "aws_security_group_rule" "security_group_ingress_rule_egress" {
  type              = "egress"
  security_group_id = aws_security_group.security_group.id
  cidr_blocks       = ["0.0.0.0/0"]
  protocol          = -1
  from_port         = 0
  to_port           = 0
}


resource "aws_iam_policy" "quicksight_full_access" {
    description      = null
    name             = "quicksight_full_access"
    name_prefix      = null
    path             = "/"
    policy           = jsonencode(
        {
            Statement = [
                {
                    Action   = "quicksight:*"
                    Effect   = "Allow"
                    Resource = "*"
                    Sid      = ""
                },
            ]
            Version   = "2012-10-17"
        }
    )
    tags             = {}
    tags_all         = {}
}

resource "aws_iam_role" "task_iam_role" {
  name = "task_${var.environment_name}"
  path = "/"
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      }
    ]
  })
  managed_policy_arns = ["arn:aws:iam::aws:policy/CloudWatchFullAccess", "arn:aws:iam::aws:policy/AWSAppMeshEnvoyAccess", "arn:aws:iam::aws:policy/SecretsManagerReadWrite", "arn:aws:iam::aws:policy/AmazonDynamoDBFullAccess", "arn:aws:iam::aws:policy/AmazonS3FullAccess", "arn:aws:iam::aws:policy/AmazonBedrockLimitedAccess", "arn:aws:iam::aws:policy/AWSLambda_FullAccess", aws_iam_policy.quicksight_full_access.arn]
  inline_policy {
    name   = "assume-any-role"
    policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
        "Sid": "VisualEditor0",
        "Effect": "Allow",
        "Action": "sts:AssumeRole",
        "Resource": "arn:aws:iam::*:role/*"
    },
    {
        "Effect": "Allow",
        "Action": [
            "ssmmessages:CreateControlChannel",
            "ssmmessages:CreateDataChannel",
            "ssmmessages:OpenControlChannel",
            "ssmmessages:OpenDataChannel"
        ],
      "Resource": "*"
    }
  ]
}
EOF
  }
}

resource "aws_iam_role" "task_execution_iam_role" {
  name = "task_execution_${var.environment_name}"
  path = "/"
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      }
    ]
  })
  managed_policy_arns = ["arn:aws:iam::aws:policy/CloudWatchFullAccess", "arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly", "arn:aws:iam::aws:policy/SecretsManagerReadWrite", "arn:aws:iam::aws:policy/AmazonSSMReadOnlyAccess"]
}


resource "aws_service_discovery_private_dns_namespace" "private_dns" {
  name = "${var.environment_name}.paytrack.local"
  vpc  = aws_vpc.vpc.id
}

resource "aws_appmesh_mesh" "mesh" {
  name = var.environment_name
  spec {
    egress_filter {
      type = "ALLOW_ALL"
    }
  }
}

resource "aws_route53_zone" "mesh_hosted_zone" {
  name    = "${var.environment_name}.mesh.local"
  comment = "Zona criada para virtual services do mesh"
  vpc {
    vpc_id = aws_vpc.vpc.id
  }
}

resource "aws_route53_record" "mesh_dns_record" {
  depends_on = [
    aws_route53_zone.mesh_hosted_zone
  ]

  zone_id = aws_route53_zone.mesh_hosted_zone.id
  records = ["*******"]
  ttl     = 900
  name    = "*.${var.environment_name}.mesh.local"
  type    = "A"
}
