output "mesh_id" {
  value = aws_appmesh_mesh.mesh.id
}

output "private_dns_id" {
  value = aws_service_discovery_private_dns_namespace.private_dns.id
}

output "task_iam_role" {
  value = aws_iam_role.task_iam_role.arn
}

output "task_execution_iam_role" {
  value = aws_iam_role.task_execution_iam_role.arn
}

output "private_subnets" {
  value = [
    aws_subnet.privateSubnet1.id,
    aws_subnet.privateSubnet2.id
  ]
}

output "public_subnets" {
  value = [
    aws_subnet.publicSubnet1.id,
    aws_subnet.publicSubnet2.id
  ]
}

output "ecs_cluster" {
  value = aws_ecs_cluster.ecs_cluster.id
}

output "vpc_id" {
  value = aws_vpc.vpc.id
}

output "security_group_id" {
  value = aws_security_group.security_group.id
}

output "hosted_zone_id" {
  value = aws_service_discovery_private_dns_namespace.private_dns.hosted_zone
}

output "vpc_cidr" {
  value = aws_vpc.vpc.cidr_block
}