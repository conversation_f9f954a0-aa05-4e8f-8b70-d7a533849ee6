# ==============================================================================
# COLLECTOR PROXY MODULE - RECURSOS COMPARTILHADOS POR AMBIENTE
# ==============================================================================
#
# Este módulo contém todos os recursos compartilhados do collector-proxy que devem
# ser criados uma única vez por ambiente, independentemente de quantos serviços
# utilizem o sidecar collector-proxy.
#
# RECURSOS COMPARTILHADOS:
# - S3 Bucket para dados de requisições HTTP
# - S3 Bucket para logs de auditoria
# - IAM Policies para acesso de escrita (ECS Tasks)
# - IAM Policies para acesso de leitura (usuários)
#
# RECURSOS ESPECÍFICOS POR SERVIÇO (ficam no módulo service):
# - CloudWatch Log Groups

# ------------------------------------------------------------------------------
# Bucket S3 para Coleta de Requisições HTTP
# ------------------------------------------------------------------------------
# Bucket S3 principal compartilhado por ambiente para armazenar dados de requisições HTTP
# coletados pelos sidecars collector-proxy. Cada serviço organiza seus dados usando
# prefixos específicos (ex: integrador-expedia/, integrador-omie/).
# Configurado com prevent_destroy para proteção contra exclusão acidental.
resource "aws_s3_bucket" "collector_proxy_bucket" {
  bucket = "paytrack-${var.environment_name}-http-request-collector"

  lifecycle {
    prevent_destroy = true
  }

  tags = {
    Name        = "paytrack-${var.environment_name}-http-request-collector"
    Description = "Bucket S3 compartilhado para coleta de requisições HTTP de todos os serviços no ambiente ${var.environment_name}"
    Ambiente    = var.environment_name
    Component   = "collector-proxy"
    Purpose     = "HTTP Request Collection - Shared by all services"
    DataType    = "HTTP Requests"
    Shared      = "true"
  }
}

# Ativa S3 Versioning para proteger contra sobrescrita acidental de dados
# e permitir recuperação de versões anteriores dos arquivos coletados
resource "aws_s3_bucket_versioning" "collector_proxy_bucket_versioning" {
  bucket = aws_s3_bucket.collector_proxy_bucket.id

  lifecycle {
    prevent_destroy = true
  }

  versioning_configuration {
    status = "Enabled"
  }
}

# Configura Server-Side Encryption com AES256 para criptografar todos os objetos
# armazenados no bucket automaticamente, garantindo segurança dos dados em repouso
resource "aws_s3_bucket_server_side_encryption_configuration" "collector_proxy_bucket_encryption" {
  bucket = aws_s3_bucket.collector_proxy_bucket.id

  lifecycle {
    prevent_destroy = true
  }

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

# Bloqueia completamente acesso público ao bucket por segurança,
# garantindo que apenas recursos autorizados via IAM possam acessar os dados
resource "aws_s3_bucket_public_access_block" "collector_proxy_bucket_pab" {
  bucket = aws_s3_bucket.collector_proxy_bucket.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# ------------------------------------------------------------------------------
# Bucket S3 para Logs de Acesso (Auditoria)
# ------------------------------------------------------------------------------
# Bucket S3 dedicado para armazenar access logs do bucket principal,
# fornecendo trilha de auditoria completa de quem acessou quais dados e quando.
# Essencial para compliance e monitoramento de segurança.
resource "aws_s3_bucket" "collector_proxy_access_logs_bucket" {
  bucket = "paytrack-${var.environment_name}-http-request-collector-access-logs"

  lifecycle {
    prevent_destroy = true
  }

  tags = {
    Name        = "paytrack-${var.environment_name}-http-request-collector-access-logs"
    Description = "Bucket S3 para logs de acesso e auditoria do bucket principal de coleta HTTP no ambiente ${var.environment_name}"
    Ambiente    = var.environment_name
    Component   = "collector-proxy"
    Purpose     = "Access Logs for HTTP Request Collection Bucket - Shared by all services"
    DataType    = "Access Logs"
    Shared      = "true"
    Compliance  = "Audit Trail"
  }
}

# Criptografia dos logs de acesso para garantir segurança dos dados de auditoria
resource "aws_s3_bucket_server_side_encryption_configuration" "collector_proxy_access_logs_bucket_encryption" {
  bucket = aws_s3_bucket.collector_proxy_access_logs_bucket.id

  lifecycle {
    prevent_destroy = true
  }

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

# Lifecycle Policy para gerenciamento automático de custos dos logs de auditoria.
# Remove logs após 365 dias e versões antigas após 365 dias para evitar acúmulo desnecessário.
resource "aws_s3_bucket_lifecycle_configuration" "collector_proxy_access_logs_lifecycle" {
  bucket = aws_s3_bucket.collector_proxy_access_logs_bucket.id

  lifecycle {
    prevent_destroy = true
  }

  rule {
    id     = "access_logs_lifecycle"
    status = "Enabled"

    filter {
      prefix = ""
    }

    expiration {
      days = 365
    }

    noncurrent_version_expiration {
      noncurrent_days = 365
    }
  }
}

# Versioning para logs de auditoria - proteção adicional contra alteração/exclusão acidental
resource "aws_s3_bucket_versioning" "collector_proxy_access_logs_bucket_versioning" {
  bucket = aws_s3_bucket.collector_proxy_access_logs_bucket.id
  versioning_configuration {
    status = "Enabled"
  }
}

# Segurança máxima para logs de auditoria - bloqueia todo acesso público
resource "aws_s3_bucket_public_access_block" "collector_proxy_access_logs_bucket_pab" {
  bucket = aws_s3_bucket.collector_proxy_access_logs_bucket.id

  lifecycle {
    prevent_destroy = true
  }

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# ------------------------------------------------------------------------------
# Configuração de Logging de Acesso do Servidor S3
# ------------------------------------------------------------------------------
# Configura S3 Server Access Logging para capturar todas as requisições feitas ao bucket principal.
# Logs incluem: timestamp, IP origem, operação realizada, objeto acessado, response code, etc.
# Essencial para auditoria e compliance.
resource "aws_s3_bucket_logging" "collector_proxy_bucket_logging" {
  bucket = aws_s3_bucket.collector_proxy_bucket.id

  lifecycle {
    prevent_destroy = true
  }

  target_bucket = aws_s3_bucket.collector_proxy_access_logs_bucket.id
  target_prefix = "access-logs/"
}

# ------------------------------------------------------------------------------
# IAM Policy para Task ECS (Acesso de Escrita ao S3)
# ------------------------------------------------------------------------------
# Define as permissões mínimas necessárias para os sidecars collector-proxy
# gravarem dados no bucket S3 compartilhado. Política compartilhada por ambiente
# usando princípio de least privilege.
data "aws_iam_policy_document" "collector_proxy_s3_policy" {
  # Permite upload de objetos e gerenciamento de multipart uploads
  statement {
    effect = "Allow"
    actions = [
      "s3:PutObject",
      "s3:PutObjectAcl",
      "s3:AbortMultipartUpload",
      "s3:ListMultipartUploadParts"
    ]
    resources = [
      "arn:aws:s3:::${aws_s3_bucket.collector_proxy_bucket.bucket}/*"
    ]
  }

  # Permite listar conteúdo do bucket (necessário para operações de escrita)
  statement {
    effect = "Allow"
    actions = [
      "s3:ListBucket"
    ]
    resources = [
      "arn:aws:s3:::${aws_s3_bucket.collector_proxy_bucket.bucket}"
    ]
  }
}

# Cria a IAM Policy compartilhada baseada no documento de política definido acima.
# Política reutilizada por todas as ECS Tasks que possuem sidecar collector-proxy
# no mesmo ambiente, otimizando gerenciamento de permissões.
resource "aws_iam_policy" "collector_proxy_s3_policy" {
  name        = "${var.environment_name}-collector-proxy-s3-policy"
  description = "Shared IAM policy for collector-proxy sidecars to write to S3 buckets in ${var.environment_name} environment"
  policy      = data.aws_iam_policy_document.collector_proxy_s3_policy.json

  lifecycle {
    prevent_destroy = true
  }

  tags = {
    Name        = "${var.environment_name}-collector-proxy-s3-policy"
    Description = "Política IAM compartilhada para acesso de escrita ao S3 pelos sidecars collector-proxy no ambiente ${var.environment_name}"
    Ambiente    = var.environment_name
    Component   = "collector-proxy"
    Purpose     = "Shared collector-proxy S3 access for all services"
    PolicyType  = "S3 Write Access"
    Shared      = "true"
  }
}

# ------------------------------------------------------------------------------
# IAM Policy para Usuários (Acesso de Leitura ao S3)
# ------------------------------------------------------------------------------
# Define permissões de leitura para usuários acessarem dados coletados e logs de auditoria.
# Política não anexada automaticamente - deve ser aplicada manualmente a usuários/grupos
# conforme necessário. Inclui acesso aos dados principais e logs de auditoria.
data "aws_iam_policy_document" "collector_proxy_user_access_policy" {
  # Permite listar buckets (necessário para navegação)
  statement {
    effect = "Allow"
    actions = [
      "s3:ListBucket"
    ]
    resources = [
      "arn:aws:s3:::${aws_s3_bucket.collector_proxy_bucket.bucket}",
      "arn:aws:s3:::${aws_s3_bucket.collector_proxy_access_logs_bucket.bucket}"
    ]
  }

  # Permite leitura de objetos nos buckets de dados e auditoria
  statement {
    effect = "Allow"
    actions = [
      "s3:GetObject"
    ]
    resources = [
      "arn:aws:s3:::${aws_s3_bucket.collector_proxy_bucket.bucket}/*",
      "arn:aws:s3:::${aws_s3_bucket.collector_proxy_access_logs_bucket.bucket}/*"
    ]
  }
}

# Cria IAM Policy para usuários com permissões de leitura aos buckets.
# Política standalone que deve ser anexada manualmente a usuários/grupos conforme necessário.
# Não é anexada automaticamente para controle granular de acesso.
resource "aws_iam_policy" "collector_proxy_user_access_policy" {
  name        = "${var.environment_name}-collector-proxy-user-access-policy"
  description = "Shared IAM policy for users to read from collector-proxy S3 buckets in ${var.environment_name} environment"
  policy      = data.aws_iam_policy_document.collector_proxy_user_access_policy.json

  lifecycle {
    prevent_destroy = true
  }

  tags = {
    Name        = "${var.environment_name}-collector-proxy-user-access-policy"
    Description = "Política IAM para acesso de leitura de usuários aos buckets de coleta HTTP e logs de auditoria no ambiente ${var.environment_name}"
    Ambiente    = var.environment_name
    Component   = "collector-proxy"
    Purpose     = "Shared user access to HTTP request collection buckets"
    PolicyType  = "S3 Read Access"
    Shared      = "true"
    UserPolicy  = "true"
  }
} 