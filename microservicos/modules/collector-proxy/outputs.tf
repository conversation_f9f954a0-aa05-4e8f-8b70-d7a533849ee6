# Outputs for collector-proxy module
output "s3_bucket_name" {
  description = "Nome do bucket S3 para coleta de requisições HTTP"
  value       = aws_s3_bucket.collector_proxy_bucket.bucket
}

output "s3_bucket_arn" {
  description = "ARN do bucket S3 para coleta de requisições HTTP"
  value       = aws_s3_bucket.collector_proxy_bucket.arn
}

output "iam_policy_arn" {
  description = "ARN da política IAM para acesso de escrita ao S3"
  value       = aws_iam_policy.collector_proxy_s3_policy.arn
}

output "iam_user_policy_arn" {
  description = "ARN da política IAM para acesso de leitura de usuários"
  value       = aws_iam_policy.collector_proxy_user_access_policy.arn
}