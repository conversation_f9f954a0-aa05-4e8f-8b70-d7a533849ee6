
provider "aws" {
  default_tags {
    tags = {
      Name     = var.environment_name
      Ambiente = var.environment_name
    }
  }
}

provider "aws" {
  alias   = "main_account"
  profile = "main_account"
  region  = "us-east-1"
  default_tags {
    tags = {
      Name     = var.environment_name
      Ambiente = var.environment_name
    }
  }
}

locals {
  domain_name      = "*.${var.environment_name}.paytrack.com.br"
  hosted_zone_name = "${var.environment_name}.paytrack.com.br"
}

resource "aws_route53_zone" "public_hosted_zone" {
  name    = local.hosted_zone_name
  comment = "Zona para criação de endpoints publicos"
}

data "aws_route53_zone" "main_hosted_zone" {
  provider = aws.main_account
  name     = "paytrack.com.br"
}

resource "aws_route53_record" "staging_record" {
  provider = aws.main_account
  zone_id  = data.aws_route53_zone.main_hosted_zone.id
  name     = aws_route53_zone.public_hosted_zone.name
  type     = "NS"
  ttl      = "30"
  records  = aws_route53_zone.public_hosted_zone.name_servers
}

resource "aws_acm_certificate" "certificate" {
  domain_name       = local.domain_name
  validation_method = "DNS"
  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_route53_record" "certificate_dns" {
  allow_overwrite = true
  name            = tolist(aws_acm_certificate.certificate.domain_validation_options)[0].resource_record_name
  records         = [tolist(aws_acm_certificate.certificate.domain_validation_options)[0].resource_record_value]
  type            = tolist(aws_acm_certificate.certificate.domain_validation_options)[0].resource_record_type
  zone_id         = aws_route53_zone.public_hosted_zone.zone_id
  ttl             = 60
}

resource "aws_acm_certificate_validation" "certificate_validation" {
  certificate_arn         = aws_acm_certificate.certificate.arn
  validation_record_fqdns = [aws_route53_record.certificate_dns.fqdn]
}
