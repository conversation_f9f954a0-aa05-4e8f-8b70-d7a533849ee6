import {
  to = aws_launch_template.sync-executor
  id = "lt-0597d2c09a3f29f5d"
}


resource "aws_launch_template" "sync-executor" {
  name                                  = "sync-executor"
  ebs_optimized                         = "false"
  image_id                              = "ami-0d91834ff1a8d8b4a" 
  instance_type                         = "t3.small"  
  key_name                              = "ZYNA_NV_2" 
  user_data                             = "IyEvYmluL2Jhc2ggLXhlCmF3cyBzMyBjcCBzMzovL25vdm8tc3luYy9leGVjdXRvci9ob3N0cyAvZXRjL2hvc3RzCgp5dW0gaW5zdGFsbCAteSBhd3Nsb2dzCm1rZGlyIC91c3IvcGF5dHJhY2sgLXAKYXdzIHMzIGNwIHMzOi8vbm92by1zeW5jL2V4ZWN1dG9yL2F3c2xvZ3MuY29uZiAvZXRjL2F3c2xvZ3MvYXdzbG9ncy5jb25mCnN5c3RlbWN0bCBzdGFydCBhd3Nsb2dzZAphd3MgczMgY3AgczM6Ly9ub3ZvLXN5bmMvZXhlY3V0b3IvZXhlY3V0b3IuamFyIC91c3IvcGF5dHJhY2svCmF3cyBzMyBjcCBzMzovL25vdm8tc3luYy9leGVjdXRvci9zdGFydC5zaCAvdXNyL3BheXRyYWNrLwphd3MgczMgY3AgczM6Ly9ub3ZvLXN5bmMvZXhlY3V0b3Ivc2VjdXJpdHkucHJvcGVydGllcyAvdXNyL3BheXRyYWNrLwphd3MgczMgc3luYyBzMzovL25vdm8tc3luYy9leGVjdXRvci9jb25maWcgL3Vzci9wYXl0cmFjay9jb25maWcKY2htb2QgNzc3IC91c3IvcGF5dHJhY2svc3RhcnQuc2gKL3Vzci9wYXl0cmFjay9zdGFydC5zaA=="
  vpc_security_group_ids                = [ aws_security_group.sync-executor.id ]
  update_default_version                = true

  block_device_mappings {
    device_name  = "/dev/xvda" 
    ebs {
        delete_on_termination = "true" 
        encrypted             = "false" 
        snapshot_id           = "snap-0fb41b418feeb3ad3"
        volume_size           = 150
        iops                  = 3000 
        volume_type           = "gp3" 
      }
  }
  iam_instance_profile {
    name  = "CloudWatchCustomMetrics"
  }

  monitoring {
    enabled = true
  }

  tags             = {
            "managed-by"                                  = "terraform",
            "env"                                         = "prod"
    }

  lifecycle {
    ignore_changes = [
      description,
      user_data,
      tags,
      block_device_mappings,
      image_id,
      latest_version,
      default_version,
      key_name
    ]
  }
}


resource "aws_security_group" "sync-executor" {
  name        = "sync-executor"
  description = "Allow 8080 TCP"

  vpc_id = "vpc-289f564e"

  ingress {
    from_port   = 8080
    to_port     = 8080
    protocol    = "tcp"
    security_groups   = ["sg-053ea81a9d189bbd3","sg-00ba00e994457f698"] #fullstack lb e internal lb
    #cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = [ "***********/16", "**********/12", "10.0.0.0/8" ]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}
