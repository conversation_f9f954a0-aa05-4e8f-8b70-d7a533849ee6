import {
  to = aws_launch_template.sync-management
  id = "lt-0174e64939c428348"
}


resource "aws_launch_template" "sync-management" {
  name                                  = "sync-management"
  ebs_optimized                         = "false"
  image_id                              = "ami-0d91834ff1a8d8b4a" 
  instance_type                         = "t3.small"  
  key_name                              = "ZYNA_NV_2" 
  user_data                             = "IyEvYmluL2Jhc2ggLXhlCnl1bSBpbnN0YWxsIC15IGF3c2xvZ3MKbWtkaXIgL3Vzci9wYXl0cmFjayAtcAphd3MgczMgY3AgczM6Ly9ub3ZvLXN5bmMvbWFuYWdlbWVudC9hd3Nsb2dzLmNvbmYgL2V0Yy9hd3Nsb2dzL2F3c2xvZ3MuY29uZgpzeXN0ZW1jdGwgc3RhcnQgYXdzbG9nc2QKYXdzIHMzIGNwIHMzOi8vbm92by1zeW5jL21hbmFnZW1lbnQvbWFuYWdlbWVudC5qYXIgL3Vzci9wYXl0cmFjay8KYXdzIHMzIGNwIHMzOi8vbm92by1zeW5jL21hbmFnZW1lbnQvc3RhcnQuc2ggL3Vzci9wYXl0cmFjay8KYXdzIHMzIHN5bmMgczM6Ly9ub3ZvLXN5bmMvbWFuYWdlbWVudC9jb25maWcgL3Vzci9wYXl0cmFjay9jb25maWcKY2htb2QgNzc3IC91c3IvcGF5dHJhY2svc3RhcnQuc2gKL3Vzci9wYXl0cmFjay9zdGFydC5zaA=="
  vpc_security_group_ids                = [ aws_security_group.sync-management.id ]
  update_default_version                = true

  block_device_mappings {
    device_name  = "/dev/xvda" 
    ebs {
        delete_on_termination = "true" 
        encrypted             = "false" 
        snapshot_id           = "snap-0fb41b418feeb3ad3"
        volume_size           = 15
        volume_type           = "gp3" 
      }
  }
  iam_instance_profile {
    name  = "CloudWatchCustomMetrics"
  }

  monitoring {
    enabled = true
  }

  tags             = {
            "managed-by"                                  = "terraform",
            "env"                                         = "prod"
    }

  lifecycle {
    ignore_changes = [
      description,
      user_data,
      tags,
      block_device_mappings,
      image_id,
      latest_version,
      default_version,
      key_name
    ]
  }
}


resource "aws_security_group" "sync-management" {
  name        = "sync-management"
  description = "Allow 8080 TCP"

  vpc_id = "vpc-289f564e"

  ingress {
    from_port   = 8080
    to_port     = 8080
    protocol    = "tcp"
    security_groups   = ["sg-053ea81a9d189bbd3","sg-00ba00e994457f698"] #fullstack lb e internal lb
    #cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = [ "***********/16", "**********/12", "10.0.0.0/8" ]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}
