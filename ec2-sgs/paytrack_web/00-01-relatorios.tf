import {
  to = aws_launch_template.relatorios
  id = "lt-076d9498e8974204c"
}


resource "aws_launch_template" "relatorios" {
  name                                  = "paytrack_prd_relatorios"
  ebs_optimized                         = "false"
  image_id                              = "ami-03631d94974543f76" 
  instance_type                         = "t3.small"  
  user_data                             = "IyEvYmluL2Jhc2ggLXhlCnJtIC1yZiAvdXNyL3BheXRyYWNrCnl1bSBpbnN0YWxsIC15IGF3c2xvZ3MKYXdzIHMzIGNwIHMzOi8vcGF5dHJhY2stcmVsYXRvcmlvcy9wcmQvYXdzbG9ncy5jb25mIC9ldGMvYXdzbG9ncy9hd3Nsb2dzLmNvbmYKc3lzdGVtY3RsIHN0YXJ0IGF3c2xvZ3NkCmF3cyBzMyBzeW5jIHMzOi8vcGF5dHJhY2std2ViL3ByZC9yZXBvcnRzIC91c3IvcGF5dHJhY2svcmVwb3J0cwphd3MgczMgY3AgczM6Ly9wYXl0cmFjay1yZWxhdG9yaW9zL3ByZC9yZWxhdG9yaW9zLmphciAvdXNyL3BheXRyYWNrLwphd3MgczMgY3AgczM6Ly9wYXl0cmFjay1yZWxhdG9yaW9zL3ByZC9hcHBsaWNhdGlvbi5wcm9wZXJ0aWVzIC91c3IvcGF5dHJhY2svCmF3cyBzMyBjcCBzMzovL3BheXRyYWNrLXJlbGF0b3Jpb3MvcHJkL3NldGVudi5zaCAvdXNyL3BheXRyYWNrLwpjaG1vZCA3NzcgL3Vzci9wYXl0cmFjay9zZXRlbnYuc2gKY2QgL3Vzci9wYXl0cmFjawouL3NldGVudi5zaA=="
  vpc_security_group_ids                = [ aws_security_group.relatorios.id ]
  update_default_version                = true
  disable_api_termination               = false
  disable_api_stop                      = false

  block_device_mappings {
    device_name  = "/dev/xvda" 
    ebs {
        delete_on_termination = "true" 
        encrypted             = "false" 
        snapshot_id           = "snap-0c9123cdf264ec066"
        volume_size           = 15 
        iops                  = 3000
        volume_type           = "gp3" 
      }
  }
  iam_instance_profile {
    name  = "CloudWatchCustomMetrics"
  }
  monitoring {
    enabled = true
  }

  tags             = {
            "managed-by"                                  = "terraform",
            "env"                                         = "prod"
    }
  
  lifecycle {
    ignore_changes = [
      description,
      user_data,
      tags,
      block_device_mappings[0].ebs[0].snapshot_id,
      block_device_mappings[0].ebs[0].encrypted,
      image_id,
      latest_version
    ]
  }
}


resource "aws_security_group" "relatorios" {
  name        = "relatorios"
  description = "Allow 8080 TCP"

  vpc_id = "vpc-289f564e"

  ingress {
    from_port         = 8080
    to_port           = 8080
    protocol          = "tcp"
    security_groups   = ["sg-053ea81a9d189bbd3","sg-00ba00e994457f698"] #fullstack lb e internal lb
    #cidr_blocks = ["0.0.0.0/0"]
    
  }

  ingress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = [ "***********/16", "**********/12", "10.0.0.0/8" ]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}
