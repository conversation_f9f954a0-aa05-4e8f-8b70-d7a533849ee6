import {
  to = aws_launch_template.paytrack_prd_web
  id = "lt-04550bd7c7f29bf77"
}


resource "aws_launch_template" "paytrack_prd_web" {
  name                                  = "paytrack_prd_web"
  ebs_optimized                         = "false"
  image_id                              = "ami-015ceed9e4d21cd24" 
  instance_type                         = "t3.medium"  
  key_name                              = "paytrack_prd_web" 
  user_data                             = "IyEvYmluL2Jhc2ggLXhlCnl1bSBpbnN0YWxsIC15IGF3c2xvZ3MKbWtkaXIgL3Vzci9wYXl0cmFjayAtcAphd3MgczMgY3AgczM6Ly9wYXl0cmFjay13ZWIvcHJkL2F3c2xvZ3MuY29uZiAvZXRjL2F3c2xvZ3MvYXdzbG9ncy5jb25mCnN5c3RlbWN0bCBzdGFydCBhd3Nsb2dzZAphd3MgczMgY3AgczM6Ly9wYXl0cmFjay13ZWIvcHJkL3draHRtbHRvaW1hZ2UgL3Vzci9iaW4vd2todG1sdG9pbWFnZQphd3MgczMgY3AgczM6Ly9wYXl0cmFjay13ZWIvcHJkL3draHRtbHRvcGRmIC91c3IvYmluL3draHRtbHRvcGRmCmNobW9kIDc3NyAvdXNyL2Jpbi93ayoKYXdzIHMzIGNwIHMzOi8vcGF5dHJhY2std2ViL3ByZC9sb2NhbC5jb25mIC9ldGMvZm9udHMvbG9jYWwuY29uZgphd3MgczMgY3AgczM6Ly9wYXl0cmFjay13ZWIvcHJkL1JPT1QuamFyIC91c3IvcGF5dHJhY2svCmF3cyBzMyBzeW5jIHMzOi8vcGF5dHJhY2std2ViL3ByZC9yZXBvcnRzIC91c3IvcGF5dHJhY2svcmVwb3J0cwphd3MgczMgY3AgczM6Ly9wYXl0cmFjay13ZWIvcHJkL3BheXRyYWNrLnByb3BlcnRpZXMgL3Vzci9wYXl0cmFjay8KYXdzIHMzIGNwIHMzOi8vcGF5dHJhY2std2ViL3ByZC9zZXRlbnYuc2ggL3Vzci9wYXl0cmFjay8KY2htb2QgNzc3IC91c3IvcGF5dHJhY2svc2V0ZW52LnNoCi91c3IvcGF5dHJhY2svc2V0ZW52LnNo"
  vpc_security_group_ids                = [ aws_security_group.paytrack_prd_web.id ]
  update_default_version                = true

  block_device_mappings {
    device_name  = "/dev/xvda" 
    ebs {
        delete_on_termination = "true" 
        encrypted             = "false" 
        snapshot_id           = "snap-03cb24332efcf16cb"
        volume_size           = 50 
        volume_type           = "gp3" 
      }
  }
  iam_instance_profile {
    arn  = "arn:aws:iam::393346304284:instance-profile/EC2_Paytrack_Web"
  }
  monitoring {
    enabled = true
  }

  tags             = {
            "managed-by"                                  = "terraform",
            "env"                                         = "prod"
    }

  lifecycle {
  ignore_changes = [
    description,
    user_data,
    tags,
    block_device_mappings[0].ebs[0].snapshot_id,
    block_device_mappings[0].ebs[0].encrypted,
    image_id,
    latest_version
    ]
  }
}


resource "aws_security_group" "paytrack_prd_web" {
  name        = "app-protocol"
  description = "Allow 8080 TCP"

  vpc_id = "vpc-289f564e"

  ingress {
    from_port   = 8080
    to_port     = 8080
    protocol    = "tcp"
    security_groups   = ["sg-053ea81a9d189bbd3","sg-00ba00e994457f698"] #fullstack lb e internal lb
    #cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = [ "***********/16", "**********/12", "10.0.0.0/8" ]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}
