import {
  to = aws_launch_template.tradutor
  id = "lt-0f2f2291fa4f3c0a1"
}


resource "aws_launch_template" "tradutor" {
  name                                  = "paytrack_prd_tradutor"
  ebs_optimized                         = "false"
  image_id                              = "ami-03631d94974543f76" 
  instance_type                         = "t3.small"  
  key_name                              = "ZYNA_NV_2" 
  user_data                             = "IyEvYmluL2Jhc2ggLXhlCnl1bSBpbnN0YWxsIC15IGF3c2xvZ3MKYXdzIHMzIGNwIHMzOi8vcGF5dHJhY2stdHJhZHV0b3IvcHJkL2F3c2xvZ3MuY29uZiAvZXRjL2F3c2xvZ3MvYXdzbG9ncy5jb25mCnN5c3RlbWN0bCBzdGFydCBhd3Nsb2dzZAphd3MgczMgY3AgczM6Ly9wYXl0cmFjay10cmFkdXRvci9wcmQvdHJhZHV0b3IuamFyIC91c3IvcGF5dHJhY2svCmF3cyBzMyBjcCBzMzovL3BheXRyYWNrLXRyYWR1dG9yL3ByZC9zZXRlbnYuc2ggL3Vzci9wYXl0cmFjay8KYXdzIHMzIGNwIHMzOi8vcGF5dHJhY2stdHJhZHV0b3IvcHJkL3RyYWR1dG9yLnByb3BlcnRpZXMgL3Vzci9wYXl0cmFjay8KY2htb2QgNzc3IC91c3IvcGF5dHJhY2svc2V0ZW52LnNoCi91c3IvcGF5dHJhY2svc2V0ZW52LnNo"
  vpc_security_group_ids                = [ aws_security_group.tradutor.id ]
  update_default_version                = true

  block_device_mappings {
    device_name  = "/dev/xvda" 
    ebs {
        delete_on_termination = "true" 
        encrypted             = "false" 
        snapshot_id           = "snap-0c9123cdf264ec066"
        volume_size           = 8 
        volume_type           = "gp3" 
      }
  }
  iam_instance_profile {
    name = "CloudWatchCustomMetrics"
  }
  monitoring {
    enabled = true
  }

  tags             = {
            "managed-by"                                  = "terraform",
            "env"                                         = "prod"
    }

  lifecycle {
    ignore_changes = [
      description,
      user_data,
      tags,
      block_device_mappings,
      image_id,
      latest_version,
      default_version,
      key_name
    ]
  }
}


resource "aws_security_group" "tradutor" {
  name        = "tradutor"
  description = "Allow 8080 TCP and rfc 1918"

  vpc_id = "vpc-289f564e"

  ingress {
    from_port   = 8080
    to_port     = 8080
    protocol    = "tcp"
    security_groups   = ["sg-053ea81a9d189bbd3","sg-00ba00e994457f698"] #fullstack lb e internal lb
    #cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = [ "***********/16", "**********/12", "10.0.0.0/8" ]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}
