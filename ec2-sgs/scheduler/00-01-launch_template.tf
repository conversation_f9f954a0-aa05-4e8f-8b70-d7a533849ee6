import {
  to = aws_launch_template.scheduler
  id = "lt-0ea4224f345bd223d"
}


resource "aws_launch_template" "scheduler" {
  name                                  = "scheduler_prd"
  ebs_optimized                         = "false"
  image_id                              = "ami-0ecaf74d8c068eb49" 
  instance_type                         = "t3a.small"  
  key_name                              = "ZYNA_NV_2" 
  user_data                             = "IyEvYmluL2Jhc2ggLXhlCnl1bSBpbnN0YWxsIC15IGF3c2xvZ3MKYXdzIHMzIGNwIHMzOi8vcGF5dHJhY2stc2NoZWR1bGVyL3ByZC9hd3Nsb2dzLmNvbmYgL2V0Yy9hd3Nsb2dzL2F3c2xvZ3MuY29uZgpzeXN0ZW1jdGwgc3RhcnQgYXdzbG9nc2QKYXdzIHMzIGNwIHMzOi8vcGF5dHJhY2stc2NoZWR1bGVyL3ByZC9zY2hlZHVsZXIud2FyIC9vcHQvdG9tY2F0OS93ZWJhcHBzCmF3cyBzMyBjcCBzMzovL3BheXRyYWNrLXNjaGVkdWxlci9wcmQvY29uZmlnLnByb3BlcnRpZXMgL29wdC90b21jYXQ5L2Jpbgphd3MgczMgY3AgczM6Ly9wYXl0cmFjay1zY2hlZHVsZXIvcHJkL3NldGVudi5zaCAvb3B0L3RvbWNhdDkvYmluLwpzZXJ2aWNlIHRvbWNhdCBzdGFydA=="
  vpc_security_group_ids                = [ aws_security_group.scheduler.id ]
  update_default_version                = true

  block_device_mappings {
    device_name  = "/dev/xvda" 
    ebs {
        delete_on_termination = "true" 
        encrypted             = "false" 
        snapshot_id           = "snap-0beac2c21fb6226e2"
        volume_size           = 8 
        volume_type           = "gp3" 
      }
  }
  iam_instance_profile {
    name = "CloudWatchCustomMetrics"
  }
  monitoring {
    enabled = true
  }

  tags             = {
            "managed-by"                                  = "terraform",
            "env"                                         = "prod"
    }

  lifecycle {
    ignore_changes = [
      description,
      user_data,
      tags,
      block_device_mappings[0].ebs[0].snapshot_id,
      block_device_mappings[0].ebs[0].encrypted,
      image_id,
      latest_version,
      key_name
    ]
  }

}


resource "aws_security_group" "scheduler" {
  name        = "scheduler"
  description = "Allow 8080 TCP and rfc 1918"

  vpc_id = "vpc-289f564e"

  ingress {
    from_port   = 8080
    to_port     = 8080
    protocol    = "tcp"
    security_groups   = ["sg-053ea81a9d189bbd3","sg-00ba00e994457f698"] #fullstack lb e internal lb
    #cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = [ "***********/16", "**********/12", "10.0.0.0/8" ]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}
