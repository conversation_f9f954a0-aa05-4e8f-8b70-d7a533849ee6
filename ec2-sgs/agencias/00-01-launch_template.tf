import {
  to = aws_launch_template.paytrack_prd_agencias
  id = "lt-007e792302f81d1ac"
}


resource "aws_launch_template" "paytrack_prd_agencias" {
  name                                  = "paytrack_prd_agencias"
  ebs_optimized                         = "false"
  image_id                              = "ami-015ceed9e4d21cd24" 
  instance_type                         = "t3.medium"  
  key_name                              = "ZYNA_NV_2" 
  user_data                             = "IyEvYmluL2Jhc2ggLXhlCnl1bSBpbnN0YWxsIC15IGF3c2xvZ3MKbWtkaXIgL3Vzci9wYXl0cmFjayAtcAphd3MgczMgY3AgczM6Ly9wYXl0cmFjay1hZ2VuY2lhcy9wcmQvYXdzbG9ncy5jb25mIC9ldGMvYXdzbG9ncy9hd3Nsb2dzLmNvbmYKc3lzdGVtY3RsIHN0YXJ0IGF3c2xvZ3NkCmF3cyBzMyBjcCBzMzovL3BheXRyYWNrLWFnZW5jaWFzL3ByZC9wYXl0cmFjay1hZ2VuY3ktYmFja2VuZC5qYXIgL3Vzci9wYXl0cmFjay8KYXdzIHMzIGNwIHMzOi8vcGF5dHJhY2stYWdlbmNpYXMvcHJkL2FwcGxpY2F0aW9uLXByb2QucHJvcGVydGllcyAvdXNyL3BheXRyYWNrLwphd3MgczMgY3AgczM6Ly9wYXl0cmFjay1hZ2VuY2lhcy9wcmQvc2V0ZW52LnNoIC91c3IvcGF5dHJhY2svCmNobW9kIDc3NyAvdXNyL3BheXRyYWNrL3NldGVudi5zaApjZCAvdXNyL3BheXRyYWNrCi4vc2V0ZW52LnNo"
  vpc_security_group_ids                = [ aws_security_group.paytrack_prd_agencias.id ]
  update_default_version                = true

  block_device_mappings {
    device_name  = "/dev/xvda" 
    ebs {
        delete_on_termination = "true" 
        encrypted             = "false" 
        snapshot_id           = "snap-03cb24332efcf16cb"
        volume_size           = 8 
        volume_type           = "gp3" 
      }
  }
  iam_instance_profile {
    name = "CloudWatchCustomMetrics"
  }
  monitoring {
    enabled = true
  }

  tags             = {
            "managed-by"                                  = "terraform",
            "env"                                         = "prod"
    }

  lifecycle {
    ignore_changes = [
      description,
      user_data,
      tags,
      block_device_mappings,
      image_id,
      latest_version,
      default_version,
      key_name
    ]
  }
}


resource "aws_security_group" "paytrack_prd_agencias" {
  name        = "agencias_app_protocol"
  description = "Allow 8080 TCP and rfc 1918"

  vpc_id = "vpc-289f564e"

  ingress {
    from_port   = 8080
    to_port     = 8080
    protocol    = "tcp"
    security_groups   = ["sg-053ea81a9d189bbd3","sg-00ba00e994457f698"] #fullstack lb e internal lb
    #cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = [ "***********/16", "**********/12", "10.0.0.0/8" ]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}
