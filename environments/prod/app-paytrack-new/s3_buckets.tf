# Import existing S3 buckets used by CloudFront distribution
data "aws_s3_bucket" "paytrack_frontend_repository" {
  bucket = "paytrack-frontend-repository"
}

data "aws_s3_bucket" "paytrack_well_known" {
  bucket = "paytrack-well-known"
}

# Create Origin Access Control for S3 buckets
resource "aws_cloudfront_origin_access_control" "paytrack_frontend_repository_oac" {
  name                              = "paytrack-frontend-repository-oac"
  description                       = "OAC for paytrack-frontend-repository bucket"
  origin_access_control_origin_type = "s3"
  signing_behavior                  = "always"
  signing_protocol                  = "sigv4"
}

resource "aws_cloudfront_origin_access_control" "paytrack_well_known_oac" {
  name                              = "paytrack-well-known-oac"
  description                       = "OAC for paytrack-well-known bucket"
  origin_access_control_origin_type = "s3"
  signing_behavior                  = "always"
  signing_protocol                  = "sigv4"
}