# Output information about the new OAC resources
output "paytrack_frontend_repository_oac_id" {
  description = "ID of the Origin Access Control for paytrack-frontend-repository bucket"
  value       = aws_cloudfront_origin_access_control.paytrack_frontend_repository_oac.id
}

output "paytrack_well_known_oac_id" {
  description = "ID of the Origin Access Control for paytrack-well-known bucket"
  value       = aws_cloudfront_origin_access_control.paytrack_well_known_oac.id
}

output "cloudfront_distribution_arn" {
  description = "ARN of the CloudFront distribution"
  value       = aws_cloudfront_distribution.app-new.arn
}

output "new_origin_ids" {
  description = "List of new origin IDs using OAC"
  value = [
    "paytrack-frontend-repository-oac",
    "root-config-oac",
    "paytrack-well-known-oac"
  ]
}