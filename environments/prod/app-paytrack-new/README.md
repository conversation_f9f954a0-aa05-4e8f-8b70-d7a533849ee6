# CloudFront Distribution - Production Environment

This directory contains the Terraform configuration for the production CloudFront distribution for `app.paytrack.com.br`.

## Recent Changes

### Origin Access Control (OAC) Implementation

The following changes have been implemented to modernize the S3 origin access:

#### New Resources Added:

1. **S3 Bucket Data Sources** (`s3_buckets.tf`):
   - `paytrack-frontend-repository` bucket import
   - `paytrack-well-known` bucket import

2. **Origin Access Control Resources** (`s3_buckets.tf`):
   - `paytrack_frontend_repository_oac` - OAC for frontend repository bucket
   - `paytrack_well_known_oac` - OAC for well-known bucket

3. **New Origins with OAC** (added to `distribution.tf`):
   - `paytrack-frontend-repository-oac` - Uses OAC instead of OAI
   - `root-config-oac` - Uses OAC for root config path
   - `paytrack-well-known-oac` - Uses OAC for well-known content

4. **S3 Bucket Policies** (`s3_bucket_policies.tf`):
   - Policies allowing CloudFront service principal access via OAC
   - Public read access for both buckets
   - Proper condition checks using CloudFront distribution ARN

#### Key Features:

- **Backward Compatibility**: All existing origins and cache behaviors remain unchanged
- **Modern Security**: New origins use OAC (Origin Access Control) instead of deprecated OAI
- **Dual Access**: Bucket policies allow both CloudFront access and public read access
- **Proper Isolation**: Each bucket has its own dedicated OAC resource

#### Migration Path:

The new OAC-enabled origins are ready to use. To migrate cache behaviors:

1. Update `target_origin_id` in cache behaviors from:
   - `paytrack-frontend-repository.s3.us-east-1.amazonaws.com` → `paytrack-frontend-repository-oac`
   - `root-config` → `root-config-oac`
   - `paytrack-well-known.s3.us-east-1.amazonaws.com` → `paytrack-well-known-oac`

2. Test thoroughly before removing old origins

## Files Structure

- `distribution.tf` - Main CloudFront distribution configuration
- `s3_buckets.tf` - S3 bucket data sources and OAC resources
- `s3_bucket_policies.tf` - Bucket policies for OAC and public access
- `response_headers.tf` - Response headers policies
- `variables.tf` - Input variables
- `outputs.tf` - Output values
- `provider.tf` - Provider configuration

## Usage

```bash
# Initialize Terraform
terraform init

# Plan changes
terraform plan

# Apply changes
terraform apply
```

## Important Notes

- The existing cache behaviors and origins are preserved
- New origins are available for gradual migration
- Bucket policies allow both CloudFront and public access as required
- All changes follow AWS best practices for CloudFront and S3 integration