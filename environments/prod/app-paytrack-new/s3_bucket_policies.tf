# Bucket policy for paytrack-frontend-repository
# Keep existing policy exactly as it is in AWS
resource "aws_s3_bucket_policy" "paytrack_frontend_repository_policy" {
  bucket = data.aws_s3_bucket.paytrack_frontend_repository.id

  policy = jsonencode({
    Version = "2008-10-17"
    Id      = "PolicyForCloudFrontPrivateContent"
    Statement = [
      {
        Sid    = "1"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::cloudfront:user/CloudFront Origin Access Identity E29CLMZ21WZNHS"
        }
        Action   = "s3:GetObject"
        Resource = "${data.aws_s3_bucket.paytrack_frontend_repository.arn}/*"
      },
      {
        Sid    = "2"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::cloudfront:user/CloudFront Origin Access Identity E29CLMZ21WZNHS"
        }
        Action   = "s3:ListBucket"
        Resource = data.aws_s3_bucket.paytrack_frontend_repository.arn
      },
      {
        Sid    = "3"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::cloudfront:user/CloudFront Origin Access Identity E29CLMZ21WZNHS"
        }
        Action   = "s3:GetObject"
        Resource = "${data.aws_s3_bucket.paytrack_frontend_repository.arn}/*"
      },
      {
        Sid    = "4"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::cloudfront:user/CloudFront Origin Access Identity E29CLMZ21WZNHS"
        }
        Action   = "s3:GetObject"
        Resource = "${data.aws_s3_bucket.paytrack_frontend_repository.arn}/*"
      },
      {
        Sid    = "5"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::cloudfront:user/CloudFront Origin Access Identity E29CLMZ21WZNHS"
        }
        Action   = "s3:GetObject"
        Resource = "${data.aws_s3_bucket.paytrack_frontend_repository.arn}/*"
      },
      {
        Sid    = "6"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::cloudfront:user/CloudFront Origin Access Identity E29CLMZ21WZNHS"
        }
        Action   = ["s3:Get*", "s3:List*"]
        Resource = "${data.aws_s3_bucket.paytrack_frontend_repository.arn}/*"
      },
      {
        Sid    = "CloudFrontPapa"
        Effect = "Allow"
        Principal = {
          Service = "cloudfront.amazonaws.com"
        }
        Action   = "s3:GetObject"
        Resource = "${data.aws_s3_bucket.paytrack_frontend_repository.arn}/*"
        Condition = {
          StringEquals = {
            "AWS:SourceArn" = "arn:aws:cloudfront::560211763190:distribution/E38X3IBATQ8V41"
          }
        }
      },
      {
        Sid    = "CloudFrontEcho"
        Effect = "Allow"
        Principal = {
          Service = "cloudfront.amazonaws.com"
        }
        Action   = "s3:GetObject"
        Resource = "${data.aws_s3_bucket.paytrack_frontend_repository.arn}/*"
        Condition = {
          StringEquals = {
            "AWS:SourceArn" = "arn:aws:cloudfront::560211763190:distribution/E2HM9YGI9XPP6V"
          }
        }
      },
      {
        Sid    = "CloudFrontAlphaDeveloper"
        Effect = "Allow"
        Principal = {
          Service = "cloudfront.amazonaws.com"
        }
        Action   = "s3:GetObject"
        Resource = "${data.aws_s3_bucket.paytrack_frontend_repository.arn}/*"
        Condition = {
          StringEquals = {
            "AWS:SourceArn" = "arn:aws:cloudfront::560211763190:distribution/ESWKERRGG9RVC"
          }
        }
      },
      {
        Sid    = "CloudFrontXrayDeveloper"
        Effect = "Allow"
        Principal = {
          Service = "cloudfront.amazonaws.com"
        }
        Action   = "s3:GetObject"
        Resource = "${data.aws_s3_bucket.paytrack_frontend_repository.arn}/*"
        Condition = {
          StringEquals = {
            "AWS:SourceArn" = "arn:aws:cloudfront::560211763190:distribution/E38G9F9UJ1JWRP"
          }
        }
      },
      {
        Sid    = "HML"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::560211763190:root"
        }
        Action   = ["s3:Get*", "s3:List*"]
        Resource = [data.aws_s3_bucket.paytrack_frontend_repository.arn, "${data.aws_s3_bucket.paytrack_frontend_repository.arn}/*"]
      },
      {
        Sid    = "CloudFrontAppNew"
        Effect = "Allow"
        Principal = {
          Service = "cloudfront.amazonaws.com"
        }
        Action   = "s3:GetObject"
        Resource = "${data.aws_s3_bucket.paytrack_frontend_repository.arn}/*"
        Condition = {
          StringEquals = {
            "AWS:SourceArn" = aws_cloudfront_distribution.app-new.arn
          }
        }
      }
    ]
  })
}

# Bucket policy for paytrack-well-known
# Keep existing policy exactly as it is in AWS
resource "aws_s3_bucket_policy" "paytrack_well_known_policy" {
  bucket = data.aws_s3_bucket.paytrack_well_known.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect    = "Allow"
        Principal = "*"
        Action    = "s3:GetObject"
        Resource  = "${data.aws_s3_bucket.paytrack_well_known.arn}/*"
      },
      {
        Sid    = "CloudFrontAppNew"
        Effect = "Allow"
        Principal = {
          Service = "cloudfront.amazonaws.com"
        }
        Action   = "s3:GetObject"
        Resource = "${data.aws_s3_bucket.paytrack_well_known.arn}/*"
        Condition = {
          StringEquals = {
            "AWS:SourceArn" = aws_cloudfront_distribution.app-new.arn
          }
        }
      }
    ]
  })
}