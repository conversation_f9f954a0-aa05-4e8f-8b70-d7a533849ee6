locals {
  csp_default = "default-src 'self'; script-src 'report-sample' 'self' 'unsafe-eval' 'unsafe-inline' https://block.opendns.com wss://api.smooch.io https://api.smooch.io https://www.clarity.ms https://static.zdassets.com https://static.userguiding.com https://cdn.jsdelivr.net https://www.gstatic.com https://cdn.jsdelivr.net/npm/js-cookie@3.0.0-rc.2/dist/js.cookie.umd.js https://globalbot.ai https://maps.googleapis.com https://www.google-analytics.com https://www.googletagmanager.com https://www.gstatic.com; object-src 'none'; base-uri 'self'; connect-src *; font-src * data:; frame-src 'self' https://london.chatbot.globalbot.ai https://app.powerbi.com https://us-east-1.quicksight.aws.amazon.com;img-src * data: blob:; manifest-src 'self'; media-src 'self' https://block.opendns.com https://static.zdassets.com data:; report-uri https://67a26d572cd0640989fd026b.endpoint.csper.io?builder=true&v=2; worker-src blob:;style-src-elem 'unsafe-inline' *;style-src 'report-sample' 'unsafe-inline' 'unsafe-hashes' 'self' https://fonts.google.com https://fonts.googleapis.com;script-src-elem * 'unsafe-inline' 'unsafe-eval' blob:;script-src-attr * 'unsafe-inline';"
}
resource "aws_cloudfront_response_headers_policy" "paytrack_no_cache" {
  name    = "paytrack-policy-no-cache-prod"
  comment = "Managed by Terraform"

  cors_config {
    access_control_allow_credentials = true

    access_control_allow_headers {
      items = ["ALL"]
    }

    access_control_allow_methods {
      items = ["ALL"]
    }

    access_control_allow_origins {
      items = [
        "https://app.paytrack.com.br",
        "https://api.paytrack.com.br",
        "https://supplier.paytrack.com.br",
        "https://login.paytrack.com.br"
      ]
    }

    origin_override = true
  }

  custom_headers_config {
    items {
      header   = "Cache-Control"
      override = true
      value    = "no-cache, no-store, must-revalidate"
    }
    items {
      header   = "Pragma"
      override = true
      value    = "no-cache"
    }
  }
  security_headers_config {
    strict_transport_security {
      access_control_max_age_sec = 31536000
      include_subdomains         = true
      override                   = true
      preload                    = true
    }
    content_security_policy {
      override                = true
      content_security_policy = local.csp_default
    }
  }

}

resource "aws_cloudfront_response_headers_policy" "paytrack" {
  name    = "paytrack-policy-prod"
  comment = "Managed by Terraform"

  cors_config {
    access_control_allow_credentials = true

    access_control_allow_headers {
      items = ["content-type"]
    }

    access_control_allow_methods {
      items = ["ALL"]
    }

    access_control_allow_origins {
      items = [
        "https://app.paytrack.com.br",
        "https://api.paytrack.com.br",
        "https://supplier.paytrack.com.br",
        "https://login.paytrack.com.br"
      ]
    }

    origin_override = true
  }

  security_headers_config {
    strict_transport_security {
      access_control_max_age_sec = 31536000
      include_subdomains         = true
      override                   = true
      preload                    = true
    }
    content_security_policy {
      override                = true
      content_security_policy = local.csp_default
    }
  }

}