resource "aws_cloudfront_distribution" "app" {
  aliases = ["app.paytrack.com.br"]

  default_cache_behavior {
    allowed_methods = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
    cached_methods  = ["GET", "HEAD"]
    compress        = "true"
    default_ttl     = "86400"

    forwarded_values {
      cookies {
        forward = "all"
      }

      headers      = ["*"]
      query_string = "true"
    }

    max_ttl                    = "31536000"
    min_ttl                    = "0"
    response_headers_policy_id = aws_cloudfront_response_headers_policy.paytrack.id
    smooth_streaming           = "false"
    target_origin_id           = "fullstack-1074348514.us-east-1.elb.amazonaws.com"
    viewer_protocol_policy     = "redirect-to-https"
  }

  default_root_object = "index.html"
  enabled             = "true"
  http_version        = "http2"
  is_ipv6_enabled     = "true"

  #0
  ordered_cache_behavior {
    allowed_methods            = ["GET", "HEAD", "OPTIONS"]
    cache_policy_id            = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    cached_methods             = ["GET", "HEAD", "OPTIONS"]
    compress                   = "true"
    default_ttl                = "0"
    max_ttl                    = "0"
    min_ttl                    = "0"
    origin_request_policy_id   = "88a5eaf4-2fd4-4709-b370-b4c650ea3fcf"
    path_pattern               = "/legacy/*"
    response_headers_policy_id = aws_cloudfront_response_headers_policy.paytrack.id
    smooth_streaming           = "false"
    target_origin_id           = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    viewer_protocol_policy     = "redirect-to-https"
  }
  #1
  ordered_cache_behavior {
    allowed_methods            = ["GET", "HEAD", "OPTIONS"]
    cache_policy_id            = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    cached_methods             = ["GET", "HEAD", "OPTIONS"]
    compress                   = "true"
    default_ttl                = "0"
    max_ttl                    = "0"
    min_ttl                    = "0"
    path_pattern               = "/menu/*"
    response_headers_policy_id = aws_cloudfront_response_headers_policy.paytrack.id
    smooth_streaming           = "false"
    target_origin_id           = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    viewer_protocol_policy     = "redirect-to-https"
  }
  #2
  ordered_cache_behavior {
    allowed_methods            = ["GET", "HEAD", "OPTIONS"]
    cache_policy_id            = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    cached_methods             = ["GET", "HEAD", "OPTIONS"]
    compress                   = "true"
    default_ttl                = "0"
    max_ttl                    = "0"
    min_ttl                    = "0"
    path_pattern               = "/settings/*"
    response_headers_policy_id = aws_cloudfront_response_headers_policy.paytrack.id
    smooth_streaming           = "false"
    target_origin_id           = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    viewer_protocol_policy     = "redirect-to-https"
  }
  #3
  ordered_cache_behavior {
    allowed_methods            = ["GET", "HEAD", "OPTIONS"]
    cache_policy_id            = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    cached_methods             = ["GET", "HEAD"]
    compress                   = "true"
    default_ttl                = "0"
    max_ttl                    = "0"
    min_ttl                    = "0"
    path_pattern               = "index.html"
    response_headers_policy_id = aws_cloudfront_response_headers_policy.paytrack_no_cache.id
    smooth_streaming           = "false"
    target_origin_id           = "root-config"
    viewer_protocol_policy     = "redirect-to-https"
  }
  #4
  ordered_cache_behavior {
    allowed_methods            = ["GET", "HEAD", "OPTIONS"]
    cache_policy_id            = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    cached_methods             = ["GET", "HEAD", "OPTIONS"]
    compress                   = "true"
    default_ttl                = "0"
    max_ttl                    = "0"
    min_ttl                    = "0"
    path_pattern               = "/js/*"
    response_headers_policy_id = aws_cloudfront_response_headers_policy.paytrack_no_cache.id
    smooth_streaming           = "false"
    target_origin_id           = "root-config"
    viewer_protocol_policy     = "redirect-to-https"
  }
  #5
  ordered_cache_behavior {
    allowed_methods            = ["GET", "HEAD", "OPTIONS"]
    cache_policy_id            = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    cached_methods             = ["GET", "HEAD", "OPTIONS"]
    compress                   = "true"
    default_ttl                = "0"
    max_ttl                    = "0"
    min_ttl                    = "0"
    path_pattern               = "/assets/*"
    response_headers_policy_id = aws_cloudfront_response_headers_policy.paytrack_no_cache.id
    smooth_streaming           = "false"
    target_origin_id           = "root-config"
    viewer_protocol_policy     = "redirect-to-https"
  }
  #6
  ordered_cache_behavior {
    allowed_methods = ["GET", "HEAD", "OPTIONS"]
    cached_methods  = ["GET", "HEAD", "OPTIONS"]
    compress        = "true"
    default_ttl     = "86400"

    forwarded_values {
      cookies {
        forward           = "whitelist"
        whitelisted_names = ["access_token"]
      }

      headers      = ["*"]
      query_string = "false"
    }

    max_ttl                    = "31536000"
    min_ttl                    = "0"
    path_pattern               = "/s3/machadomeyer/*"
    response_headers_policy_id = aws_cloudfront_response_headers_policy.paytrack.id
    smooth_streaming           = "false"
    target_origin_id           = "fullstack-1074348514.us-east-1.elb.amazonaws.com"
    viewer_protocol_policy     = "redirect-to-https"
  }
  #7
  ordered_cache_behavior {
    allowed_methods = ["GET", "HEAD", "OPTIONS"]
    cached_methods  = ["GET", "HEAD", "OPTIONS"]
    compress        = "true"
    default_ttl     = "86400"

    forwarded_values {
      cookies {
        forward           = "whitelist"
        whitelisted_names = ["access_token"]
      }

      headers      = ["Host", "User-Agent"]
      query_string = "true"
    }

    max_ttl                    = "31536000"
    min_ttl                    = "86400"
    path_pattern               = "/s3/*"
    response_headers_policy_id = aws_cloudfront_response_headers_policy.paytrack.id
    smooth_streaming           = "false"
    target_origin_id           = "fullstack-1074348514.us-east-1.elb.amazonaws.com"
    viewer_protocol_policy     = "redirect-to-https"
  }
  #8
  ordered_cache_behavior {
    allowed_methods            = ["GET", "HEAD", "OPTIONS"]
    cache_policy_id            = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    cached_methods             = ["GET", "HEAD", "OPTIONS"]
    compress                   = "true"
    default_ttl                = "0"
    max_ttl                    = "0"
    min_ttl                    = "0"
    path_pattern               = "/banco-bilhetes*"
    response_headers_policy_id = aws_cloudfront_response_headers_policy.paytrack.id
    smooth_streaming           = "false"
    target_origin_id           = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    viewer_protocol_policy     = "redirect-to-https"
  }
  #9
  ordered_cache_behavior {
    allowed_methods            = ["GET", "HEAD", "OPTIONS"]
    cache_policy_id            = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    cached_methods             = ["GET", "HEAD"]
    compress                   = "true"
    default_ttl                = "0"
    max_ttl                    = "0"
    min_ttl                    = "0"
    path_pattern               = "/carteira-digital*"
    response_headers_policy_id = aws_cloudfront_response_headers_policy.paytrack.id
    smooth_streaming           = "false"
    target_origin_id           = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    viewer_protocol_policy     = "redirect-to-https"
  }
  #10
  ordered_cache_behavior {
    allowed_methods            = ["GET", "HEAD", "OPTIONS"]
    cache_policy_id            = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    cached_methods             = ["GET", "HEAD", "OPTIONS"]
    compress                   = "true"
    default_ttl                = "0"
    max_ttl                    = "0"
    min_ttl                    = "0"
    path_pattern               = "/diretorio-hoteis*"
    response_headers_policy_id = aws_cloudfront_response_headers_policy.paytrack.id
    smooth_streaming           = "false"
    target_origin_id           = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    viewer_protocol_policy     = "redirect-to-https"
  }
  #11
  ordered_cache_behavior {
    allowed_methods            = ["GET", "HEAD", "OPTIONS"]
    cache_policy_id            = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    cached_methods             = ["GET", "HEAD", "OPTIONS"]
    compress                   = "true"
    default_ttl                = "0"
    max_ttl                    = "0"
    min_ttl                    = "0"
    path_pattern               = "/cartao/*"
    response_headers_policy_id = aws_cloudfront_response_headers_policy.paytrack.id
    smooth_streaming           = "false"
    target_origin_id           = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    viewer_protocol_policy     = "redirect-to-https"
  }
  #12
  ordered_cache_behavior {
    allowed_methods            = ["GET", "HEAD", "OPTIONS"]
    cache_policy_id            = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    cached_methods             = ["GET", "HEAD", "OPTIONS"]
    compress                   = "true"
    default_ttl                = "0"
    max_ttl                    = "0"
    min_ttl                    = "0"
    path_pattern               = "/solicitacao-travel/*"
    response_headers_policy_id = aws_cloudfront_response_headers_policy.paytrack.id
    smooth_streaming           = "false"
    target_origin_id           = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    viewer_protocol_policy     = "redirect-to-https"
  }
  #13
  ordered_cache_behavior {
    allowed_methods            = ["GET", "HEAD", "OPTIONS"]
    cache_policy_id            = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    cached_methods             = ["GET", "HEAD", "OPTIONS"]
    compress                   = "true"
    default_ttl                = "0"
    max_ttl                    = "0"
    min_ttl                    = "0"
    path_pattern               = "/.well-known/*"
    response_headers_policy_id = aws_cloudfront_response_headers_policy.paytrack.id
    smooth_streaming           = "false"
    target_origin_id           = "paytrack-well-known.s3.us-east-1.amazonaws.com"
    viewer_protocol_policy     = "redirect-to-https"
  }
  #14
  ordered_cache_behavior {
    allowed_methods            = ["GET", "HEAD", "OPTIONS"]
    cache_policy_id            = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    cached_methods             = ["GET", "HEAD", "OPTIONS"]
    compress                   = "true"
    default_ttl                = "0"
    max_ttl                    = "0"
    min_ttl                    = "0"
    path_pattern               = "/configuracoes/*"
    response_headers_policy_id = aws_cloudfront_response_headers_policy.paytrack.id
    smooth_streaming           = "false"
    target_origin_id           = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    viewer_protocol_policy     = "redirect-to-https"
  }
  #15
  ordered_cache_behavior {
    allowed_methods            = ["GET", "HEAD", "OPTIONS"]
    cache_policy_id            = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    cached_methods             = ["GET", "HEAD", "OPTIONS"]
    compress                   = "true"
    default_ttl                = "0"
    max_ttl                    = "0"
    min_ttl                    = "0"
    path_pattern               = "/dados-insights-despesas/*"
    response_headers_policy_id = aws_cloudfront_response_headers_policy.paytrack.id
    smooth_streaming           = "false"
    target_origin_id           = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    viewer_protocol_policy     = "redirect-to-https"
  }
  #16
  ordered_cache_behavior {
    allowed_methods            = ["GET", "HEAD", "OPTIONS"]
    cache_policy_id            = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    cached_methods             = ["GET", "HEAD", "OPTIONS"]
    compress                   = "true"
    default_ttl                = "0"
    max_ttl                    = "0"
    min_ttl                    = "0"
    path_pattern               = "/dados-insights/*"
    response_headers_policy_id = aws_cloudfront_response_headers_policy.paytrack.id
    smooth_streaming           = "false"
    target_origin_id           = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    viewer_protocol_policy     = "redirect-to-https"
  }
  #17
  ordered_cache_behavior {
    allowed_methods = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
    cached_methods  = ["GET", "HEAD", "OPTIONS"]
    compress        = "true"
    default_ttl     = "86400"

    forwarded_values {
      cookies {
        forward = "all"
      }

      headers      = ["*"]
      query_string = "true"
    }

    max_ttl                    = "31536000"
    min_ttl                    = "0"
    path_pattern               = "/clientes*"
    response_headers_policy_id = aws_cloudfront_response_headers_policy.paytrack.id
    smooth_streaming           = "false"
    target_origin_id           = "internal-private-prod-**********.us-east-1.elb.amazonaws.com"
    viewer_protocol_policy     = "redirect-to-https"
  }
  #18
  ordered_cache_behavior {
    allowed_methods            = ["GET", "HEAD", "OPTIONS"]
    cache_policy_id            = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    cached_methods             = ["GET", "HEAD", "OPTIONS"]
    compress                   = "true"
    default_ttl                = "0"
    max_ttl                    = "0"
    min_ttl                    = "0"
    path_pattern               = "/static_assets/*"
    response_headers_policy_id = aws_cloudfront_response_headers_policy.paytrack.id
    smooth_streaming           = "false"
    target_origin_id           = "root-config"
    viewer_protocol_policy     = "redirect-to-https"
  }
  #19
  ordered_cache_behavior {
    allowed_methods            = ["GET", "HEAD", "OPTIONS"]
    cache_policy_id            = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    cached_methods             = ["GET", "HEAD", "OPTIONS"]
    compress                   = "true"
    default_ttl                = "0"
    max_ttl                    = "0"
    min_ttl                    = "0"
    path_pattern               = "/solicitacao-expense/*"
    response_headers_policy_id = aws_cloudfront_response_headers_policy.paytrack.id
    smooth_streaming           = "false"
    target_origin_id           = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    viewer_protocol_policy     = "redirect-to-https"
  }
  #20
  ordered_cache_behavior {
    allowed_methods            = ["GET", "HEAD", "OPTIONS"]
    cache_policy_id            = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    cached_methods             = ["GET", "HEAD", "OPTIONS"]
    compress                   = "true"
    default_ttl                = "0"
    max_ttl                    = "0"
    min_ttl                    = "0"
    path_pattern               = "/gerenciamento-solicitacao/*"
    response_headers_policy_id = aws_cloudfront_response_headers_policy.paytrack.id
    smooth_streaming           = "false"
    target_origin_id           = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    viewer_protocol_policy     = "redirect-to-https"
  }

  origin {
    connection_attempts = "1"
    connection_timeout  = "10"

    custom_origin_config {
      http_port                = "80"
      https_port               = "443"
      origin_keepalive_timeout = "5"
      origin_protocol_policy   = "https-only"
      origin_read_timeout      = "120"
      origin_ssl_protocols     = ["TLSv1.2"]
    }

    domain_name = "fullstack-1074348514.us-east-1.elb.amazonaws.com"
    origin_id   = "fullstack-1074348514.us-east-1.elb.amazonaws.com"
  }

  origin {
    connection_attempts = "3"
    connection_timeout  = "10"

    custom_origin_config {
      http_port                = "80"
      https_port               = "443"
      origin_keepalive_timeout = "5"
      origin_protocol_policy   = "http-only"
      origin_read_timeout      = "120"
      origin_ssl_protocols     = ["TLSv1.2"]
    }

    domain_name = "public-prod-6a028ca7af1513c7.elb.us-east-1.amazonaws.com"
    origin_id   = "internal-private-prod-**********.us-east-1.elb.amazonaws.com"
  }

  origin {
    connection_attempts = "3"
    connection_timeout  = "10"
    domain_name         = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    origin_id           = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    origin_path         = "/prod"

    s3_origin_config {
      origin_access_identity = "origin-access-identity/cloudfront/E29CLMZ21WZNHS"
    }
  }

  origin {
    connection_attempts = "3"
    connection_timeout  = "10"
    domain_name         = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    origin_id           = "root-config"
    origin_path         = "/prod/root-config"

    s3_origin_config {
      origin_access_identity = "origin-access-identity/cloudfront/E29CLMZ21WZNHS"
    }
  }

  origin {
    connection_attempts = "3"
    connection_timeout  = "10"
    domain_name         = "paytrack-well-known.s3.us-east-1.amazonaws.com"
    origin_id           = "paytrack-well-known.s3.us-east-1.amazonaws.com"
    origin_path         = "/prod"
  }

  price_class = "PriceClass_All"

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  retain_on_delete = "false"
  staging          = "false"

  tags = {
    Name = "app.paytrack.com.br"
  }

  tags_all = {
    Name = "app.paytrack.com.br"
  }

  viewer_certificate {
    acm_certificate_arn            = "arn:aws:acm:us-east-1:393346304284:certificate/1b93ea80-acab-4e4c-a475-73f76b22bd34"
    cloudfront_default_certificate = "false"
    minimum_protocol_version       = "TLSv1.2_2021"
    ssl_support_method             = "sni-only"
  }

  web_acl_id = "arn:aws:wafv2:us-east-1:393346304284:global/webacl/waf-prod/1e3dd5bf-5b3b-418f-82e0-c0d1c8659bc1"
}
