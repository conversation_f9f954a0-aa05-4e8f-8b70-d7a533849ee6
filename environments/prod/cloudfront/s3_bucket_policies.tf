# Bucket policy for paytrack-frontend-repository
# Import existing policy and add new OAC permissions
resource "aws_s3_bucket_policy" "paytrack_frontend_repository_policy" {
  bucket = data.aws_s3_bucket.paytrack_frontend_repository.id

  policy = jsonencode({
    Version = "2008-10-17"
    Id      = "PolicyForCloudFrontPrivateContent"
    Statement = [
      # Existing OAI permissions (keep for backward compatibility)
      {
        Sid    = "1"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::cloudfront:user/CloudFront Origin Access Identity E29CLMZ21WZNHS"
        }
        Action   = "s3:GetObject"
        Resource = "${data.aws_s3_bucket.paytrack_frontend_repository.arn}/*"
      },
      {
        Sid    = "2"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::cloudfront:user/CloudFront Origin Access Identity E29CLMZ21WZNHS"
        }
        Action   = "s3:ListBucket"
        Resource = data.aws_s3_bucket.paytrack_frontend_repository.arn
      },
      {
        Sid    = "3"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::cloudfront:user/CloudFront Origin Access Identity E29CLMZ21WZNHS"
        }
        Action   = "s3:GetObject"
        Resource = "${data.aws_s3_bucket.paytrack_frontend_repository.arn}/*"
      },
      {
        Sid    = "4"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::cloudfront:user/CloudFront Origin Access Identity E29CLMZ21WZNHS"
        }
        Action   = "s3:GetObject"
        Resource = "${data.aws_s3_bucket.paytrack_frontend_repository.arn}/*"
      },
      {
        Sid    = "5"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::cloudfront:user/CloudFront Origin Access Identity E29CLMZ21WZNHS"
        }
        Action   = "s3:GetObject"
        Resource = "${data.aws_s3_bucket.paytrack_frontend_repository.arn}/*"
      },
      {
        Sid    = "6"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::cloudfront:user/CloudFront Origin Access Identity E29CLMZ21WZNHS"
        }
        Action   = ["s3:Get*", "s3:List*"]
        Resource = "${data.aws_s3_bucket.paytrack_frontend_repository.arn}/*"
      },
      {
        Sid    = "CloudFrontPapa"
        Effect = "Allow"
        Principal = {
          Service = "cloudfront.amazonaws.com"
        }
        Action   = "s3:GetObject"
        Resource = "${data.aws_s3_bucket.paytrack_frontend_repository.arn}/*"
        Condition = {
          StringEquals = {
            "AWS:SourceArn" = "arn:aws:cloudfront::560211763190:distribution/E38X3IBATQ8V41"
          }
        }
      },
      {
        Sid    = "CloudFrontEcho"
        Effect = "Allow"
        Principal = {
          Service = "cloudfront.amazonaws.com"
        }
        Action   = "s3:GetObject"
        Resource = "${data.aws_s3_bucket.paytrack_frontend_repository.arn}/*"
        Condition = {
          StringEquals = {
            "AWS:SourceArn" = "arn:aws:cloudfront::560211763190:distribution/E2HM9YGI9XPP6V"
          }
        }
      },
      {
        Sid    = "HML"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::560211763190:root"
        }
        Action   = ["s3:Get*", "s3:List*"]
        Resource = [data.aws_s3_bucket.paytrack_frontend_repository.arn, "${data.aws_s3_bucket.paytrack_frontend_repository.arn}/*"]
      },
      # NEW: Add OAC permission for this production distribution
      {
        Sid    = "AllowCloudFrontServicePrincipalReadOnlyProd"
        Effect = "Allow"
        Principal = {
          Service = "cloudfront.amazonaws.com"
        }
        Action   = "s3:GetObject"
        Resource = "${data.aws_s3_bucket.paytrack_frontend_repository.arn}/*"
        Condition = {
          StringEquals = {
            "AWS:SourceArn" = aws_cloudfront_distribution.app.arn
          }
        }
      },
      # NEW: Add public read access as requested
      {
        Sid       = "AllowPublicReadAccess"
        Effect    = "Allow"
        Principal = "*"
        Action    = "s3:GetObject"
        Resource  = "${data.aws_s3_bucket.paytrack_frontend_repository.arn}/*"
      }
    ]
  })
}

# Bucket policy for paytrack-well-known
# Import existing policy and add new OAC permissions
resource "aws_s3_bucket_policy" "paytrack_well_known_policy" {
  bucket = data.aws_s3_bucket.paytrack_well_known.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      # Existing public read access (preserve current functionality)
      {
        Effect    = "Allow"
        Principal = "*"
        Action    = "s3:GetObject"
        Resource  = "${data.aws_s3_bucket.paytrack_well_known.arn}/*"
      },
      # NEW: Add OAC permission for this production distribution
      {
        Sid    = "AllowCloudFrontServicePrincipalReadOnlyProd"
        Effect = "Allow"
        Principal = {
          Service = "cloudfront.amazonaws.com"
        }
        Action   = "s3:GetObject"
        Resource = "${data.aws_s3_bucket.paytrack_well_known.arn}/*"
        Condition = {
          StringEquals = {
            "AWS:SourceArn" = aws_cloudfront_distribution.app.arn
          }
        }
      }
    ]
  })
}