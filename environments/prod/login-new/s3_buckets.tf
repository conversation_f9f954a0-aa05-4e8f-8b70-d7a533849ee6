# Import existing S3 bucket used by CloudFront distribution
data "aws_s3_bucket" "login_paytrack" {
  bucket = "login.paytrack.com.br"
}

# Create Origin Access Control for S3 bucket
resource "aws_cloudfront_origin_access_control" "login_paytrack_oac" {
  name                              = "login-paytrack-oac"
  description                       = "OAC for login.paytrack.com.br bucket"
  origin_access_control_origin_type = "s3"
  signing_behavior                  = "always"
  signing_protocol                  = "sigv4"
}