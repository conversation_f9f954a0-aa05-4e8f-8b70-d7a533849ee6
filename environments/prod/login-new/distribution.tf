resource "aws_cloudfront_distribution" "login_new" {
  aliases = ["login-new.paytrack.com.br"]

  default_cache_behavior {
    allowed_methods = ["GET", "HEAD", "OPTIONS"]
    cached_methods  = ["GET", "HEAD"]
    compress        = true
    default_ttl     = 86400
    max_ttl         = 31536000
    min_ttl         = 0
    smooth_streaming = false
    target_origin_id = "S3-login.paytrack.com.br/prod"
    viewer_protocol_policy = "redirect-to-https"

    forwarded_values {
      cookies {
        forward = "none"
      }
      headers      = []
      query_string = false
    }

    lambda_function_association {
      event_type   = "viewer-response"
      lambda_arn   = "arn:aws:lambda:us-east-1:393346304284:function:ResponseHeaders:16"
      include_body = false
    }
  }

  # Ordered cache behavior for root path
  ordered_cache_behavior {
    allowed_methods  = ["GET", "HEAD"]
    cached_methods   = ["GET", "HEAD"]
    cache_policy_id  = "4135ea2d-6df8-44a3-9df3-4b5a84be39ad"
    compress         = true
    path_pattern     = "/"
    smooth_streaming = false
    target_origin_id = "S3-login.paytrack.com.br/prod"
    viewer_protocol_policy = "https-only"

    lambda_function_association {
      event_type   = "viewer-response"
      lambda_arn   = "arn:aws:lambda:us-east-1:393346304284:function:ResponseHeaders:16"
      include_body = false
    }
  }

  # Custom error responses
  custom_error_response {
    error_code            = 403
    error_caching_min_ttl = 10
    response_code         = 200
    response_page_path    = "/index.html"
  }

  custom_error_response {
    error_code            = 404
    error_caching_min_ttl = 10
    response_code         = 200
    response_page_path    = "/index.html"
  }

  default_root_object = "index.html"
  enabled             = true
  http_version        = "http2"
  is_ipv6_enabled     = true

  # S3 origin with OAC
  origin {
    connection_attempts = 3
    connection_timeout  = 10
    domain_name         = "login.paytrack.com.br.s3.us-east-1.amazonaws.com"
    origin_id           = "S3-login.paytrack.com.br/prod"
    origin_path         = "/prod"

    origin_access_control_id = aws_cloudfront_origin_access_control.login_paytrack_oac.id
  }

  price_class = "PriceClass_All"

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  retain_on_delete = false
  staging          = false

  tags = {
    Name = "login-new.paytrack.com.br"
  }

  tags_all = {
    Name = "login-new.paytrack.com.br"
  }

  viewer_certificate {
    acm_certificate_arn            = "arn:aws:acm:us-east-1:393346304284:certificate/e7707da8-ef86-424a-b641-c6befd4d539c"
    cloudfront_default_certificate = false
    minimum_protocol_version       = "TLSv1.2_2021"
    ssl_support_method             = "sni-only"
  }

  web_acl_id = "arn:aws:wafv2:us-east-1:393346304284:global/webacl/waf-prod/1e3dd5bf-5b3b-418f-82e0-c0d1c8659bc1"
}