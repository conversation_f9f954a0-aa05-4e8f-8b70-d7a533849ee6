# Bucket policy for login.paytrack.com.br
resource "aws_s3_bucket_policy" "login_paytrack_policy" {
  bucket = data.aws_s3_bucket.login_paytrack.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "PublicReadGetObject"
        Effect = "Allow"
        Principal = "*"
        Action    = "s3:GetObject"
        Resource  = "${data.aws_s3_bucket.login_paytrack.arn}/*"
      },
      {
        Sid    = "CloudFrontLoginNew"
        Effect = "Allow"
        Principal = {
          Service = "cloudfront.amazonaws.com"
        }
        Action   = "s3:GetObject"
        Resource = "${data.aws_s3_bucket.login_paytrack.arn}/*"
        Condition = {
          StringEquals = {
            "AWS:SourceArn" = aws_cloudfront_distribution.login_new.arn
          }
        }
      }
    ]
  })
}