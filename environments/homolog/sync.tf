terraform {
  backend "s3" {
    bucket                    = "paytrack-terraform-homolog"
    key                       = "sync/terraform.tfstate"
    region                    = "us-east-1"
    profile                   = "hml"
  }
}

module "sync" {
  profile                       = "hml"
  region                        = "us-east-1"
  source                        = "../../modules/sync"
  environment_name              = "staging"
  #peering EKS
  eks_vpc_id                    = "vpc-0a29b4f42ea593c27"
  eks_cidr_block                = "*********/16"
  eks_rtb                       = "rtb-090a238a7d789c2d8"
  #peering ECS
  ecs_vpc_id                    = "vpc-097093d2681a97b1d"
  ecs_cidr_block                = "172.16.0.0/16"
  ecs_rtbs                      = [  "rtb-01cf5a3c64fc38d7e", "rtb-05611dd84f8fcf046", "rtb-08625dd0784c61976" ]
  #Executor ASG size
  asg_executor_max_size       = 2
  asg_executor_min_size       = 2
  asg_executor_desired_size   = 2
  #Management ASG size
  asg_management_max_size       = 1
  asg_management_min_size       = 1
  asg_management_desired_size   = 1
  
  enable_auto_scaling_schedule  = true
  dns_staging                   = true
  dns_developer                 = false
  #Senha do RDS via input na console (ver cofre de senhas)
  rds_master_password           = var.rds_master_password
  allowed_cidr_blocks           = [ "**********/16", "*********/16" ]
  #rds
  rds_instance_size             = "db.m7i.large"
  storage_size                  = 200
  log-ingester-port             = "12201"
  log-ingester-url              = "logs-dev.paytrack.com.br"
  #database collation
  db_default_collation          = "utf8mb4_general_ci"
  kibana-enabled                = "true"
}



#Senha do banco, informar via INPUT na console!
variable "rds_master_password" {
  type        = string
  description = "Informe senha do RDS. Favor não comitar!"
  sensitive   = true
}

