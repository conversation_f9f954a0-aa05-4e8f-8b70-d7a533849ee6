terraform {
  backend "s3" {
    bucket  = "terraform-iac-v2"
    key     = "paytrack/environments/eks/xray/terraform.tfstate"
    region  = "us-east-1"
    profile = "prod"
  }
}

provider "aws" {
  alias     = "hml"
  region    = "us-east-1"
  profile   = "hml"
}


provider "aws" {
  alias     = "prod"
  region    = "us-east-1"
  profile   = "prod"
}

data "terraform_remote_state" "default" {
  backend = "s3"
  config = {
    bucket  = "terraform-iac-v2"
    key     = "paytrack/terraform.tfstate"
    region  = "us-east-1"
    profile = "prod"
  }
}

locals {
  s3_paytrack_updater_jar = {
    key        = data.terraform_remote_state.default.outputs.s3_paytrack_updater_jar.key
    bucket     = data.terraform_remote_state.default.outputs.s3_paytrack_updater_jar.bucket
    version_id = data.terraform_remote_state.default.outputs.s3_paytrack_updater_jar.version_id
  }
  vpc_id                        = data.terraform_remote_state.default.outputs.aws_vpc_main_id
  lb_fullstack_arn              = data.terraform_remote_state.default.outputs.aws_lb_listener_fullstack_arn
  iam_role_arn_to_ec2_terminate = data.terraform_remote_state.default.outputs.iam_role_arn_to_ec2_terminate
  bucket_config_files_agency    = data.terraform_remote_state.default.outputs.s3_paytrack_agency_bucket
  bucket_config_files_tradutor  = data.terraform_remote_state.default.outputs.s3_paytrack_tradutor_bucket
  bucket_config_files_web       = data.terraform_remote_state.default.outputs.s3_paytrack_web_bucket
  iam_user_arn_to_sqs           = data.terraform_remote_state.default.outputs.iam_user_arn_to_sqs
}
module "xray_network" {
  source                = "../../modules/network"
  environment_name      = "xray"
  domain                = "developer"
  dns_zone_id           = "Z08060781DD0FET8KXURP"
  agencias_backend_lb   = "k8s-developer-ab15ac2c39-1874748148.us-east-1.elb.amazonaws.com"
  profile               = "hml"
}
