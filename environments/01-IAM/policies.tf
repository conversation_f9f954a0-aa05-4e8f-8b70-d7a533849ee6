resource "aws_iam_policy" "allow_cross_account_kinesis" {
  name        = "KinesisProdCrossAccess"
  description = "Allows cross account access for the Kinesis producer role"
  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect   = "Allow",
        Action   = "sts:AssumeRole",
        Resource = "arn:aws:iam::************:role/kinesis-producer-role"
      }
    ]
  })
}

resource "aws_iam_policy" "paytrack_web_dynamo_table" {
  name        = "DynamoPagamentos"
  description = "Policy for accessing specified DynamoDB tables for paytrack web"
  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Sid    = "VisualEditor0",
        Effect = "Allow",
        Action = [
          "dynamodb:BatchGetItem",
          "dynamodb:BatchWriteItem",
          "dynamodb:ConditionCheckItem",
          "dynamodb:PutItem",
          "dynamodb:DeleteItem",
          "dynamodb:Scan",
          "dynamodb:Query",
          "dynamodb:UpdateItem",
          "dynamodb:DeleteTable",
          "dynamodb:CreateTable",
          "dynamodb:GetItem",
          "dynamodb:UpdateTable",
          "dynamodb:GetRecords"
        ],
        Resource = [
          "arn:aws:dynamodb:us-east-1:************:table/pix_participante",
          "arn:aws:dynamodb:us-east-1:************:table/pix_sucesso",
          "arn:aws:dynamodb:us-east-1:************:table/pix_swap",
          "arn:aws:dynamodb:us-east-1:************:table/processo_pix",
          "arn:aws:dynamodb:us-east-1:************:table/integracao-arbi-dev",
          "arn:aws:dynamodb:us-east-1:************:table/zendesk_credenciais",
          "arn:aws:dynamodb:us-east-1:************:table/aporte-arbi-dev"
        ]
      },
      {
        Sid      = "VisualEditor1",
        Effect   = "Allow",
        Action   = "dynamodb:ListTables",
        Resource = "*"
      }
    ]
  })
}

resource "aws_iam_policy" "prod_account_secret_manager" {
  name        = "SecretsProdCrossAccess"
  description = "Allows cross account access to Secrets Manager and KMS decrypt operations"
  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Action = "secretsmanager:GetSecretValue",
        Resource = [
          "arn:aws:secretsmanager:us-east-1:************:secret:Viajante-cryx1u"
        ]
      },
      {
        Effect   = "Allow",
        Action   = "kms:Decrypt",
        Resource = "arn:aws:kms:us-east-1:************:key/02b1e48d-35c2-416e-b256-ab5bdc2ed680"
      }
    ]
  })
}


resource "aws_iam_policy" "allow_kms_usage" {
  name        = "AllowKMSUsage"
  description = "Allows usage of the specified KMS key for encrypt, decrypt, and related operations"
  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Action = [
          "kms:Encrypt",
          "kms:Decrypt",
          "kms:ReEncrypt*",
          "kms:GenerateDataKey",
          "kms:DescribeKey"
        ],
        Resource = "arn:aws:kms:us-east-1:************:key/1d13f567-280e-4567-831f-0395aac09483"
      }
    ]
  })
}


resource "aws_iam_policy" "allow_cross_account_s3" {
  name        = "S3ProdCrossAccess"
  description = "Allows cross account access for the S3 producer role"
  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        "Effect" : "Allow",
        "Action" : [
          "s3:GetObject",
          "s3:ListBucket",
          "s3:PutObject",
          "s3:DeleteObject"
        ],
        "Resource" : [
          "arn:aws:s3:::paytrack-cache",
          "arn:aws:s3:::paytrack-vault-developer",
          "arn:aws:s3:::paytrack-cache/*",
          "arn:aws:s3:::paytrack-vault-developer/*",
          "arn:aws:s3:::paytrack-carteira-digital-test/*",
          "arn:aws:s3:::paytrack-carteira-digital-test",
          "arn:aws:s3:::paytrack-relatorios/*",
          "arn:aws:s3:::paytrack-relatorios",
          "arn:aws:s3:::paytrack-generated-reports/*",
          "arn:aws:s3:::paytrack-generated-reports",
          "arn:aws:s3:::paytrack-relatorios-developer/*",
          "arn:aws:s3:::paytrack-relatorios-developer",
          "arn:aws:s3:::paytrack-reports-developer/*",
          "arn:aws:s3:::paytrack-reports-developer",
          "arn:aws:s3:::paytrack-carteira-digital-dev",
          "arn:aws:s3:::paytrack-carteira-digital-dev/*"

        ]
      }
    ]
  })
}
