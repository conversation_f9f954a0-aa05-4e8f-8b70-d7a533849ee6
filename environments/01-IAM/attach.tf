#attachment das policies gerenciadas pela AWS
resource "aws_iam_role_policy_attachment" "aws-managed" {
  for_each   = var.role_policies
  role       = "EKSWeb"
  policy_arn = each.value
}

#secrets manager
resource "aws_iam_role_policy_attachment" "prod_account_secret_manager" {
  role       = "EKSWeb"
  policy_arn = aws_iam_policy.prod_account_secret_manager.arn
}

#paytrack_web_dynamo_table
resource "aws_iam_role_policy_attachment" "paytrack_web_dynamo_table" {
  role       = "EKSWeb"
  policy_arn = aws_iam_policy.paytrack_web_dynamo_table.arn
}

#allow_cross_account_kinesis
resource "aws_iam_role_policy_attachment" "allow_cross_account_kinesis" {
  role       = "EKSWeb"
  policy_arn = aws_iam_policy.allow_cross_account_kinesis.arn
}

#allow_kms_usage
resource "aws_iam_role_policy_attachment" "allow_kms_usage" {
  role       = "EKSWeb"
  policy_arn = aws_iam_policy.allow_kms_usage.arn
}


#allow_cross_account_s3
resource "aws_iam_role_policy_attachment" "allow_cross_account_s3" {
  role       = "EKSWeb"
  policy_arn = aws_iam_policy.allow_cross_account_s3.arn
}