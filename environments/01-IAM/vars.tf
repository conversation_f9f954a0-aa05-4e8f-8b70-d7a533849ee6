variable "ambientes" {
  description = "A list of code names"
  type        = list(string)
  default     = [
    "alpha", 
    "charlie", 
    "delta", 
    "gama", 
    "juliet", 
    "papa", 
    "romeo", 
    "tango", 
    "xray", 
    "zulu"
    ]
}

variable "role_policies" {
  description = "A map of IAM policies to attach to the role."
  type        = map(string)
  default = {
    AmazonS3ReadOnlyAccess                  = "arn:aws:iam::aws:policy/AmazonS3ReadOnlyAccess"
    AmazonTextractFullAccess                = "arn:aws:iam::aws:policy/AmazonTextractFullAccess"
    SecretsManagerReadWrite                 = "arn:aws:iam::aws:policy/SecretsManagerReadWrite"
  }
}