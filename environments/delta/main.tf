terraform {
  backend "s3" {
    bucket  = "terraform-iac-v2"
    key     = "paytrack/environments/delta/terraform.tfstate"
    region  = "us-east-1"
    profile = "main_account"
  }

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 4.0"
    }
  }

  required_version = "~> 1.1"
}

data "terraform_remote_state" "default" {
  backend = "s3"
  config = {
    bucket = "terraform-iac-v2"
    key    = "paytrack/terraform.tfstate"
    region = "us-east-1"
  }
}

locals {
  s3_paytrack_updater_jar = {
    key        = data.terraform_remote_state.default.outputs.s3_paytrack_updater_jar.key
    bucket     = data.terraform_remote_state.default.outputs.s3_paytrack_updater_jar.bucket
    version_id = data.terraform_remote_state.default.outputs.s3_paytrack_updater_jar.version_id
  }
  vpc_id                        = data.terraform_remote_state.default.outputs.aws_vpc_main_id
  lb_fullstack_arn              = data.terraform_remote_state.default.outputs.aws_lb_listener_fullstack_arn
  iam_role_arn_to_ec2_terminate = data.terraform_remote_state.default.outputs.iam_role_arn_to_ec2_terminate
  bucket_config_files_agency    = data.terraform_remote_state.default.outputs.s3_paytrack_agency_bucket
  bucket_config_files_tradutor  = data.terraform_remote_state.default.outputs.s3_paytrack_tradutor_bucket
  bucket_config_files_web       = data.terraform_remote_state.default.outputs.s3_paytrack_web_bucket
  iam_user_arn_to_sqs           = data.terraform_remote_state.default.outputs.iam_user_arn_to_sqs
}

module "delta_agency" {
  source                  = "../../modules/agency"
  environment_name        = "delta"
  aws_vpc_id              = local.vpc_id
  lb_listener_arn         = local.lb_fullstack_arn
  lb_listener_priority    = "1014"
  bucket_config_files     = local.bucket_config_files_agency
  s3_paytrack_updater_jar = local.s3_paytrack_updater_jar
}

module "delta_network" {
  source           = "../../modules/network"
  environment_name = "delta"
}

module "delta_tradutor" {
  source                        = "../../modules/tradutor"
  environment_name              = "delta"
  aws_vpc_id                    = local.vpc_id
  lb_listener_arn               = local.lb_fullstack_arn
  lb_listener_priority          = "1015"
  iam_role_arn_to_ec2_terminate = local.iam_role_arn_to_ec2_terminate
  bucket_config_files           = local.bucket_config_files_tradutor
  s3_paytrack_updater_jar       = local.s3_paytrack_updater_jar
}

module "delta_web_paytrack" {
  source                        = "../../modules/web_paytrack"
  environment_name              = "delta"
  aws_vpc_id                    = local.vpc_id
  lb_listener_arn               = local.lb_fullstack_arn
  lb_listener_priority          = "1016"
  iam_role_arn_to_ec2_terminate = local.iam_role_arn_to_ec2_terminate
  bucket_config_files           = local.bucket_config_files_web
  s3_paytrack_updater_jar       = local.s3_paytrack_updater_jar
  iam_user_arn_to_sqs           = local.iam_user_arn_to_sqs
}
