terraform {
  backend "s3" {
    bucket  = "terraform-iac-v2"
    key     = "paytrack/cache/dev/terraform.tfstate"
    region  = "us-east-1"
    profile = "main_account"
  }

  required_providers {
    aws = {
      source  = "hashicorp/aws"
    }
  }
}

provider "aws" {
  alias   = "default"
  profile = "default"
  default_tags {
    tags = {
      Name     = var.environment_name
      Ambiente = var.environment_name
    }
  }
}

resource "aws_security_group" "sg-developer-redis-cache" {
  name        = "developer-redis-cache"
  description = "Security group utilizado no redis"
  vpc_id      = "vpc-0a29b4f42ea593c27"

  tags = {
    Name = "redis"
  }
}

resource "aws_vpc_security_group_ingress_rule" "allow_tcp_redis" {
  security_group_id = aws_security_group.sg-developer-redis-cache.id
  from_port         = 6379
  ip_protocol       = "tcp"
  to_port           = 6379
  referenced_security_group_id = "sg-0002e51d2d6eb52b5"
  description = "EKS Dev - Redis"
}

resource "aws_elasticache_subnet_group" "subnetgroup_developer_redis_cache" {
  name       = "subnetgroup-developer-redis-cache"
  subnet_ids = ["subnet-00d65c88c8ecc354c","subnet-03a892f50a58523c8","subnet-0263665ee258b42fe"]
}
resource "aws_elasticache_cluster" "developer-redis-cache" {
  cluster_id           = "developer-redis-cache"
  engine               = "redis"
  node_type            = "cache.t4g.micro"
  num_cache_nodes      = 1
  engine_version       = "7.1"
  port                 = 6379
  security_group_ids = [aws_security_group.sg-developer-redis-cache.id]
  subnet_group_name    = aws_elasticache_subnet_group.subnetgroup_developer_redis_cache.name
}
