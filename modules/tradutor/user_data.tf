locals {
    paytrack_tradutor_user_data = <<EOF
#!/bin/bash -xe
yum install -y awslogs
aws s3 cp s3://paytrack-tradutor/${var.environment_name}/awslogs.conf /etc/awslogs/awslogs.conf
systemctl start awslogsd
aws s3 cp s3://paytrack-tradutor/${var.environment_name}/tradutor.jar /usr/paytrack/
aws s3 cp s3://paytrack-tradutor/${var.environment_name}/setenv.sh /usr/paytrack/
aws s3 cp s3://paytrack-tradutor/${var.environment_name}/tradutor.properties /usr/paytrack/
chmod 777 /usr/paytrack/setenv.sh
/usr/paytrack/setenv.sh
EOF
}