resource "aws_launch_template" "paytrack_tradutor" {
  description     = "java17"
  default_version = 1

  block_device_mappings {
    device_name = "/dev/xvda"

    ebs {
      delete_on_termination = "true"
      encrypted             = "false"
      iops                  = "3000"
      snapshot_id           = "snap-03cb24332efcf16cb"
      volume_size           = "8"
      volume_type           = "gp3"
    }
  }

  disable_api_termination = "false"
  ebs_optimized           = "false"

  iam_instance_profile {
    name = "CloudWatchCustomMetrics"
  }

  image_id      = "ami-015ceed9e4d21cd24"
  instance_type = "t3.small"
  key_name      = "ZYNA_NV_2"

  monitoring {
    enabled = "false"
  }

  name                   = "paytrack_${var.environment_name}_tradutor"
  user_data              = base64encode(local.paytrack_tradutor_user_data)
  vpc_security_group_ids = ["sg-01fefa2db55768412"]

  lifecycle {
    ignore_changes = [image_id, block_device_mappings[0].ebs[0].snapshot_id]
  }
}

resource "aws_lb_target_group" "tradutor" {
  deregistration_delay = "300"

  health_check {
    enabled             = "true"
    healthy_threshold   = "2"
    interval            = "15"
    matcher             = "200"
    path                = "/tradutor/actuator/info"
    port                = "traffic-port"
    protocol            = "HTTP"
    timeout             = "5"
    unhealthy_threshold = "2"
  }

  load_balancing_algorithm_type = "round_robin"
  name                          = "${var.environment_name}-tradutor"
  port                          = "8080"
  protocol                      = "HTTP"
  protocol_version              = "HTTP1"
  slow_start                    = "0"

  stickiness {
    cookie_duration = "86400"
    enabled         = "false"
    type            = "lb_cookie"
  }

  target_type = "instance"
  vpc_id      = var.aws_vpc_id
}

resource "aws_autoscaling_group" "paytrack_tradutor" {
  capacity_rebalance        = true
  default_cooldown          = 300
  desired_capacity          = 1
  force_delete              = false
  force_delete_warm_pool    = false
  health_check_grace_period = 120
  health_check_type         = "ELB"
  max_size                  = 1
  metrics_granularity       = "1Minute"
  min_size                  = 1
  name                      = "paytrack_${var.environment_name}_tradutor"
  protect_from_scale_in     = false
  service_linked_role_arn   = "arn:aws:iam::393346304284:role/aws-service-role/autoscaling.amazonaws.com/AWSServiceRoleForAutoScaling"
  target_group_arns         = [aws_lb_target_group.tradutor.arn]
  vpc_zone_identifier = [
    "subnet-06e109f65da43dbcb",
    "subnet-0c04dabb847ecf9e7",
  ]
  wait_for_capacity_timeout = "10m"

  mixed_instances_policy {
    instances_distribution {
      on_demand_allocation_strategy = "prioritized"
      spot_allocation_strategy      = "price-capacity-optimized"
    }

    launch_template {
      launch_template_specification {
        launch_template_id   = aws_launch_template.paytrack_tradutor.id
        launch_template_name = "paytrack_${var.environment_name}_tradutor"
        version              = "$Latest"
      }

      override {
        instance_type = "t3.small"
      }

      override {
        instance_type = "t3a.small"
      }

      override {
        instance_type = "t4g.small"
      }

      override {
        instance_type = "t4g.medium"
      }

      override {
        instance_type = "t3.medium"
      }

      override {
        instance_type = "t3a.medium"
      }
    }
  }

  tag {
    key                 = "Name"
    propagate_at_launch = true
    value               = "paytrack_${var.environment_name}_tradutor"
  }
}

resource "aws_lb_listener_rule" "tradutor" {
  action {
    order            = "1"
    target_group_arn = aws_lb_target_group.tradutor.arn
    type             = "forward"
  }

  condition {
    host_header {
      values = ["${var.environment_name}.paytrack.com.br"]
    }
  }

  condition {
    path_pattern {
      values = ["/tradutor/*"]
    }
  }

  listener_arn = var.lb_listener_arn
  priority     = var.lb_listener_priority
}

resource "aws_autoscaling_schedule" "stop" {
  scheduled_action_name  = "stop_paytrack_tradutor"
  min_size               = 0
  max_size               = 0
  desired_capacity       = 0
  time_zone              = "America/Sao_Paulo"
  recurrence             = "30 20 * * MON-FRI"
  autoscaling_group_name = aws_autoscaling_group.paytrack_tradutor.name
}

resource "aws_autoscaling_schedule" "start" {
  scheduled_action_name  = "start_paytrack_tradutor"
  min_size               = 1
  max_size               = 4
  desired_capacity       = 1
  time_zone              = "America/Sao_Paulo"
  recurrence             = "0 7 * * MON-FRI"
  autoscaling_group_name = aws_autoscaling_group.paytrack_tradutor.name
}
