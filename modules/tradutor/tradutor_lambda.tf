resource "aws_lambda_permission" "allow_bucket_terminate_tradutor" {
  action         = "lambda:InvokeFunction"
  function_name  = aws_lambda_function.terminate_tradutor.arn
  principal      = "s3.amazonaws.com"
  source_account = "************"
  source_arn     = "arn:aws:s3:::paytrack-tradutor"
}

resource "aws_lambda_function" "terminate_tradutor" {
  architectures = [
    "x86_64",
  ]
  function_name                  = "Terminate-Tradutor-${var.environment_name}"
  handler                        = "br.com.paytrack.updater.UpdaterService::handle"
  memory_size                    = 256
  package_type                   = "Zip"
  reserved_concurrent_executions = -1
  role                           = var.iam_role_arn_to_ec2_terminate
  runtime                        = "java11"
  timeout                        = 600
  s3_key                         = var.s3_paytrack_updater_jar.key
  s3_bucket                      = var.s3_paytrack_updater_jar.bucket
  s3_object_version              = var.s3_paytrack_updater_jar.version_id

  publish = true

  environment {
    variables = {
      "ASG_NAME"      = aws_autoscaling_group.paytrack_tradutor.name
      "MAX_ATTEMPTS"  = "30"
      "MAX_INSTANCES" = "8"
      "SLEEP_TIME"    = "20000"
      "WEBHOOK_URL"   = "https://discordapp.com/api/webhooks/715291188915208283/J3hOxrFMSZ843uajiCIZDEgwNJil3f9TDsgIu3BcA8gkTxHtMLR8xi26Yx6glnOAKc-6"
    }
  }
}
