resource "aws_s3_object" "paytrack_agency_awslogs_conf" {
  bucket = var.bucket_config_files
  key    = "${var.environment_name}/awslogs.conf"
  content = replace(local.file_awslogs_conf_content, "\r\n", "\n")
}

resource "aws_s3_object" "paytrack_agency_setenv_sh" {
  bucket = var.bucket_config_files
  key    = "${var.environment_name}/setenv.sh"
  content = replace(local.file_setenv_sh_content, "\r\n", "\n")
}