locals {
  file_setenv_sh_content = <<EOT
# JAVA OPTS
JAVA_OPTS="$JAVA_OPTS -Xms1024m"
JAVA_OPTS="$JAVA_OPTS -Xmx1024m"
JAVA_OPTS="$JAVA_OPTS -server"


# JMX conection
#JAVA_OPTS="$JAVA_OPTS -Dcom.sun.management.jmxremote.authenticate=false"
#JAVA_OPTS="$JAVA_OPTS -Dcom.sun.management.jmxremote.ssl=false"
#JAVA_OPTS="$JAVA_OPTS -Dcom.sun.management.jmxremote.port=8686"
#JAVA_OPTS="$JAVA_OPTS -Dcom.sun.management.jmxremote.rmi.port=8686"
#JAVA_OPTS="$JAVA_OPTS -Djava.rmi.server.hostname=$RMI_IP"
#JAVA_OPTS="$JAVA_OPTS -Dcom.sun.management.jmxremote.local.only=false"

JAVA_OPTS="$JAVA_OPTS -Dspring.profiles.active=${var.environment_name},logstash"
JAVA_OPTS="$JAVA_OPTS -Duser.Timezone=America/Sao_Paulo"
JAVA_OPTS="$JAVA_OPTS -Dserver.port=8080"

export PAYTRACK_AMBIENTE=${var.environment_name}

# Other
JAVA_OPTS="$JAVA_OPTS -XX:+ShowCodeDetailsInExceptionMessages"
nohup java $JAVA_OPTS -jar /usr/paytrack/paytrack-agency-backend.jar </dev/null >/dev/null 2>&1 &

EOT
}