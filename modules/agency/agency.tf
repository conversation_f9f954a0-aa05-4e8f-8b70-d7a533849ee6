resource "aws_launch_template" "paytrack_agencias" {
  block_device_mappings {
    device_name = "/dev/xvda"

    ebs {
      delete_on_termination = "true"
      encrypted             = "false"
      iops                  = "0"
      snapshot_id           = "snap-03cb24332efcf16cb"
      volume_size           = "8"
      volume_type           = "gp3"
    }
  }

  default_version         = 1
  disable_api_termination = "false"
  ebs_optimized           = "false"

  iam_instance_profile {
    name = "CloudWatchCustomMetrics"
  }

  image_id      = "ami-03631d94974543f76"
  instance_type = "t3.small"
  key_name      = "ZYNA_NV_2"

  monitoring {
    enabled = "false"
  }

  name                   = "paytrack_${var.environment_name}_agencias"
  user_data              = base64encode(local.paytrack_agencias_user_data)
  vpc_security_group_ids = ["sg-0d821b46b061be8a1"]
}

resource "aws_lb_target_group" "agencias_api" {
  deregistration_delay = "300"

  health_check {
    enabled             = "true"
    healthy_threshold   = "2"
    interval            = "15"
    matcher             = "200-499"
    path                = "/healthcheck"
    port                = "traffic-port"
    protocol            = "HTTP"
    timeout             = "5"
    unhealthy_threshold = "2"
  }

  load_balancing_algorithm_type = "round_robin"
  name                          = "${var.environment_name}-agencias-api"
  port                          = "8080"
  protocol                      = "HTTP"
  protocol_version              = "HTTP1"
  slow_start                    = "0"

  stickiness {
    cookie_duration = "86400"
    enabled         = "false"
    type            = "lb_cookie"
  }

  target_type = "instance"
  vpc_id      = var.aws_vpc_id
}

resource "aws_autoscaling_group" "paytrack_agencias" {
  capacity_rebalance        = "true"
  default_cooldown          = "300"
  desired_capacity          = "1"
  force_delete              = "false"
  health_check_grace_period = "300"
  health_check_type         = "ELB"

  max_instance_lifetime = "0"
  max_size              = "1"
  metrics_granularity   = "1Minute"
  min_size              = "1"
  vpc_zone_identifier = [
    "subnet-06e109f65da43dbcb",
    "subnet-0c04dabb847ecf9e7",
  ]

  mixed_instances_policy {
    instances_distribution {
      on_demand_allocation_strategy            = "prioritized"
      on_demand_base_capacity                  = "0"
      on_demand_percentage_above_base_capacity = "0"
      spot_allocation_strategy                 = "price-capacity-optimized"
      spot_instance_pools                      = "0"
    }

    launch_template {
      launch_template_specification {
        launch_template_id   = aws_launch_template.paytrack_agencias.id
        launch_template_name = aws_launch_template.paytrack_agencias.name
        version              = "$Default"
      }

      override {
        instance_type = "t3.small"
      }

      override {
        instance_type = "t3a.small"
      }

      override {
        instance_type = "t4g.small"
      }

      override {
        instance_type = "t4g.medium"
      }

      override {
        instance_type = "t3.medium"
      }

      override {
        instance_type = "t3a.medium"
      }
    }
  }

  name                    = "paytrack_${var.environment_name}_agencias"
  protect_from_scale_in   = "false"
  service_linked_role_arn = "arn:aws:iam::393346304284:role/aws-service-role/autoscaling.amazonaws.com/AWSServiceRoleForAutoScaling"

  tag {
    key                 = "Name"
    propagate_at_launch = "true"
    value               = "paytrack_${var.environment_name}_agencias"
  }

  target_group_arns = [aws_lb_target_group.agencias_api.arn]

  wait_for_capacity_timeout = "10m"

  lifecycle {
    ignore_changes = [desired_capacity, max_size]
  }
}

resource "aws_lb_listener_rule" "agencias" {
  action {
    order            = "1"
    target_group_arn = aws_lb_target_group.agencias_api.arn
    type             = "forward"
  }

  condition {
    host_header {
      values = ["supplier-${var.environment_name}-api.paytrack.com.br"]
    }
  }

  listener_arn = var.lb_listener_arn
  priority     = var.lb_listener_priority
}

resource "aws_autoscaling_schedule" "stop" {
  scheduled_action_name  = "stop_paytrack_agencias"
  min_size               = 0
  max_size               = 0
  desired_capacity       = 0
  time_zone              = "America/Sao_Paulo"
  recurrence             = "30 20 * * MON-FRI"
  autoscaling_group_name = aws_autoscaling_group.paytrack_agencias.name
}

resource "aws_autoscaling_schedule" "start" {
  scheduled_action_name  = "start_paytrack_agencias"
  min_size               = 1
  max_size               = 4
  desired_capacity       = 1
  time_zone              = "America/Sao_Paulo"
  recurrence             = "0 7 * * MON-FRI"
  autoscaling_group_name = aws_autoscaling_group.paytrack_agencias.name
}
