locals {
  paytrack_agencias_user_data = <<EOF
#!/bin/bash -xe
yum install -y awslogs
mkdir /usr/paytrack -p
aws s3 cp s3://paytrack-agencias/${var.environment_name}/awslogs.conf /etc/awslogs/awslogs.conf
systemctl start awslogsd
aws s3 cp s3://paytrack-agencias/${var.environment_name}/paytrack-agency-backend.jar /usr/paytrack/
aws s3 cp s3://paytrack-agencias/${var.environment_name}/application-${var.environment_name}.properties /usr/paytrack/
aws s3 cp s3://paytrack-agencias/${var.environment_name}/setenv.sh /usr/paytrack/
chmod 777 /usr/paytrack/setenv.sh
cd /usr/paytrack
./setenv.sh
EOF
}