terraform {
  backend "s3" {
    bucket  = "terraform-iac-v2"
    key     = "modules/eventbridge.tfstate"
    region  = "us-east-1"
    profile = "main_account"
  }

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 4.0"
    }
    awscc = {
      source  = "hashicorp/awscc"
      version = "~> 0.1"
    }
  }

  required_version = "~> 1.1"
}

locals {
  uber_tenants = ["advancemedica","4biomedicamentossa","ailos","apsen","bethasistemas","comportamento","contelestruturas",
  "europlus","esss","figroup","govbr","grupoep","ilikia","playkids",
  "sapphire","sicredi", "sicoobsacramento","viajor", "pontaagro", "alaresinternet", "htb", "ciscre",
  "redemarista", "planar", "bbmlogistica", "itafrotas", "linkcomercial", "grupoherval", "altageotecnia", "vca",
  "grupomateus", "all4labels", "cipalam", "msccruisesbrasil", "equiplano", "vasco", "prafrente", "installautomacao",
  "globalconsultoria", "damasco", "bnpapeis", "sicoobpaulista", "movecta", "greylogix", "eurogestao", "mpd"]
  novenove_tenants = ["apsen","contelestruturas","equatorial","govbr","nobregapimenta","sicredi","sicoobsacramento",
  "viajor", "advancemedica", "msccruisesbrasil","mpd", "damasco"]
  fatura_tenants = ["sicredi"]
  colaboradores_tenants = distinct(concat(local.uber_tenants, local.novenove_tenants))
}

locals {
  environment_name = "prod"
  aws_region = "us-east-1"
  aws_account = "************"
}

resource "aws_cloudwatch_event_rule" "corridas_novenove" {
  for_each = toset(local.novenove_tenants)
  name = "corridas_novenove_${each.key}"
  # description         = "Trigger test room price cron"
  schedule_expression = "cron(0 * * * ? *)"
  # event_pattern       = null
}
#corridas
resource "aws_cloudwatch_event_rule" "corridas_uber" {
  for_each = toset(local.uber_tenants)
  name = "corridas_uber_${each.key}"
  # description         = "Trigger test room price cron"
  schedule_expression = "cron(0 16 * * ? *)"
  # event_pattern       = null
}

resource "aws_cloudwatch_event_rule" "fatura_uber" {
  for_each = toset(local.fatura_tenants)
  name = "fatura_uber"
  # description         = "Trigger test room price cron"
  schedule_expression = "cron(0 5 3 * ? *)"
  # event_pattern       = null
}
#fatura
resource "aws_cloudwatch_event_rule" "fatura_novenove" {
  for_each = toset(local.fatura_tenants)
  name = "fatura_novenove"
  # description         = "Trigger test room price cron"
  schedule_expression = "cron(0 4 2 * ? *)"
  # event_pattern       = null
}

#colaboradores
resource "aws_cloudwatch_event_rule" "sincronizar_colaboradores" {
  for_each = toset(local.colaboradores_tenants)
  name = "sincronizar_colaboradores_${each.key}"
  # description         = "Trigger test room price cron"
  schedule_expression = "cron(0 * * * ? *)"
  # event_pattern       = null

}

resource "aws_iam_policy" "invoke_api_policy" {

  name        = "invoke-api-policy"
  path        = "/"
  description = "Allows invocation of target http api"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "events:InvokeApiDestination"
        ]
        Effect = "Allow"
        Resource = [
          "arn:aws:events:${local.aws_region}:${local.aws_account}:api-destination/*"
        ]
      },
      {
        Action = ["sqs:SendMessage"]
        Effect = "Allow"
        Resource = [
          "arn:aws:sqs:${local.aws_region}:${local.aws_account}:${aws_sqs_queue.mobilidade_queue.name}"
        ]
      }
    ]
  })
}

resource "aws_iam_policy_attachment" "invoke_api_policy_attachment" {
  name = "Policy Attachement"
  policy_arn = aws_iam_policy.invoke_api_policy.arn
  roles       = [aws_iam_role.api_dest_role.name]
}

# create the IAM role
resource "aws_iam_role" "api_dest_role" {
  name = "ApiDestinationRole"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "events.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      }
    ]
  })
}

resource "aws_sqs_queue" "mobilidade_queue" {
  name                        = "mobilidade_queue"
}

########################################################################
# Permissao para que o eventbridge possa publicar mensagem no SQS
########################################################################

resource "aws_sqs_queue_policy" "corrida_uber_sqs_queue_policy" {
  queue_url = "https://sqs.us-east-1.amazonaws.com/************/mobilidade_async_queue"

  policy = jsonencode({
      Version = "2012-10-17"
      Statement = [
        {
          Effect = "Allow"
          Principal = {
            Service = "events.amazonaws.com"
          }
          Action = "sqs:SendMessage"
          Resource = "arn:aws:sqs:us-east-1:************:mobilidade_async_queue"
          Condition = {
            ArnEquals = {
              "aws:SourceArn" = [
                "arn:aws:events:us-east-1:************:rule/corridas_*",
                "arn:aws:events:us-east-1:************:rule/fatura_*",
                "arn:aws:events:us-east-1:************:rule/sincronizar_*"
              ]
            }
          }
        }
      ]
    })
}

########################################################################
# Configuração dos targets
########################################################################
resource "aws_cloudwatch_event_target" "corridas_uber" {

    for_each = toset(local.uber_tenants)

    rule = aws_cloudwatch_event_rule.corridas_uber[each.key].name
    arn = "arn:aws:sqs:us-east-1:************:mobilidade_async_queue"

    input_transformer {
        input_template = jsonencode({
          httpMethod = "POST",
          headers = {
            "X-Paytrack-Path" = "/${each.key}/api/v1/mobilidade/recibos/uber/fromApi",
            "X-Paytrack-Env"  = "prd"
          }
        })
      }

    dead_letter_config {
      arn = aws_sqs_queue.mobilidade_queue.arn
    }
}

resource "aws_cloudwatch_event_target" "corridas_novenove" {
    for_each = toset(local.novenove_tenants)

    rule = aws_cloudwatch_event_rule.corridas_novenove[each.key].name
    arn = "arn:aws:sqs:us-east-1:************:mobilidade_async_queue"

    input_transformer {
        input_template = jsonencode({
          httpMethod = "POST",
          headers = {
            "X-Paytrack-Path" = "/${each.key}/api/v1/mobilidade/recibos/99/fromApi",
            "X-Paytrack-Env"  = "prd"
          }
        })
      }

    dead_letter_config {
      arn = aws_sqs_queue.mobilidade_queue.arn
    }
}

resource "aws_cloudwatch_event_target" "fatura_uber" {
    for_each = toset(local.fatura_tenants)

    rule = aws_cloudwatch_event_rule.fatura_uber[each.key].name
    arn = "arn:aws:sqs:us-east-1:************:mobilidade_async_queue"

    input_transformer {
            input_template = jsonencode({
              httpMethod = "POST",
              headers = {
                "X-Paytrack-Path" = "/${each.key}/api/v1/mobilidade/faturas/fromApi?fornecedor=UBERFORBUSINESS",
                "X-Paytrack-Env"  = "prd"
              }
            })
          }

    dead_letter_config {
      arn = aws_sqs_queue.mobilidade_queue.arn
    }
}

resource "aws_cloudwatch_event_target" "fatura_novenove" {
    for_each = toset(local.fatura_tenants)

    rule = aws_cloudwatch_event_rule.fatura_novenove[each.key].name
    arn = "arn:aws:sqs:us-east-1:************:mobilidade_async_queue"

   input_transformer {
           input_template = jsonencode({
             httpMethod = "POST",
             headers = {
               "X-Paytrack-Path" = "/${each.key}/api/v1/mobilidade/faturas/fromApi?fornecedor=NOVENOVEEMPRESAS",
               "X-Paytrack-Env"  = "prd"
             }
           })
         }

    dead_letter_config {
      arn = aws_sqs_queue.mobilidade_queue.arn
    }
}

resource "aws_cloudwatch_event_target" "sincronizar" {
    for_each = toset(local.colaboradores_tenants)

    rule = aws_cloudwatch_event_rule.sincronizar_colaboradores[each.key].name
    arn = "arn:aws:sqs:us-east-1:************:mobilidade_async_queue"

    input_transformer {
            input_template = jsonencode({
              httpMethod = "POST",
              headers = {
                "X-Paytrack-Path" = "/${each.key}/api/v1/mobilidade/colaboradores/sincronizar",
                "X-Paytrack-Env"  = "prd"
              }
            })
          }

    dead_letter_config {
      arn = aws_sqs_queue.mobilidade_queue.arn
    }
}

# Schedule para auditoria Herbie
resource "aws_scheduler_schedule" "auditoria_herbie_rateio" {
  name = "auditoria-herbie-RATEIO_X_ADIANTAMENTO_INCORRETO"

  flexible_time_window {
    mode = "OFF"
  }

  schedule_expression = "cron(0/5 * * * ? *)"

  target {
    arn      = "arn:aws:lambda:us-east-1:************:function:auditoria-herbie"
    role_arn = "arn:aws:iam::************:role/service-role/Amazon_EventBridge_Scheduler_LAMBDA_999ff880e5"

    input = jsonencode({
      pathParameters = {
        tipo_auditoria = "RATEIO_DIVERGENTE_ADTO"
      }
    })
  }
}
