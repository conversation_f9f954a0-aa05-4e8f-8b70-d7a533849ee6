################################################################################
##                       Permissao de get scripts e properties s3             ##
################################################################################


data "aws_iam_policy_document" "s3_get" {
  statement {
    actions = [
      "s3:GetObject",
    ]

    resources = [
      "${aws_s3_bucket.sync-config.arn}",
      "${aws_s3_bucket.sync-config.arn}/*",
    ]
  }
}

resource "aws_iam_policy" "s3_get" {
  name   = "GetSyncConfig"
  path   = "/"
  policy = data.aws_iam_policy_document.s3_get.json
}


resource "aws_iam_role_policy_attachment" "s3GetManagement" {
  role       = aws_iam_role.management_role.name
  policy_arn = aws_iam_policy.s3_get.arn
}

resource "aws_iam_role_policy_attachment" "s3GetExecutor" {
  role       = aws_iam_role.executor_role.name
  policy_arn = aws_iam_policy.s3_get.arn
}

resource "aws_iam_role_policy_attachment" "s3GetScheduler" {
  role       = aws_iam_role.scheduler_role.name
  policy_arn = aws_iam_policy.s3_get.arn
}
