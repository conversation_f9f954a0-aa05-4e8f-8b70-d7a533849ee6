locals {
  management_properties = <<EOT
%${var.environment_name}.kafka.bootstrap.servers=b-1.staging.4khtvk.c13.kafka.us-east-1.amazonaws.com:9094,b-2.staging.4khtvk.c13.kafka.us-east-1.amazonaws.com:9094,b-3.staging.4khtvk.c13.kafka.us-east-1.amazonaws.com:9094
%${var.environment_name}.scheduler-api/mp-rest/url=https://scheduler.${var.environment_name}.paytrack.com.br/scheduler
%${var.environment_name}.executor.url=https://sync.${var.environment_name}.paytrack.com.br/executor
bucket.name=paytrack-appconfig-sync-${var.environment_name}


cogna.user.password=055d538317778cfab81b71d9257aff50
cogna.user.login=cogna


# Database
quarkus.datasource.db-kind=mysql
%${var.environment_name}.quarkus.datasource.username=${var.sync_management_db_user}
%${var.environment_name}.quarkus.datasource.password=${random_password.sync_management_db.result}
%${var.environment_name}.quarkus.datasource.jdbc.url=jdbc:mysql://sync-db.${var.environment_name}.paytrack.com.br:3306/${var.sync_database}
quarkus.datasource.metrics.enabled=true
quarkus.datasource.jdbc.enable-metrics=true

# Liquibase
quarkus.liquibase.migrate-at-start=true
quarkus.liquibase.change-log=db/master.xml

# HTTP
quarkus.http.cors=true
quarkus.http.cors.access-control-allow-credentials=true
quarkus.http.enable-compression=true

# Messaging
%${var.environment_name}.kafka.security.protocol=SSL
%${var.environment_name}.kafka.compression.type=gzip
%${var.environment_name}.kafka.group.id=management
%${var.environment_name}.kafka.max.request.size=15242880
%${var.environment_name}.mp.messaging.incoming.events.topic=events
%${var.environment_name}.mp.messaging.incoming.events.connector=smallrye-kafka
%${var.environment_name}.mp.messaging.incoming.events.key.deserializer=org.apache.kafka.common.serialization.StringDeserializer
%${var.environment_name}.mp.messaging.incoming.events.value.deserializer=br.com.paytrack.sync.deserializer.EventDeserializer

# Scheduler
scheduler-api/mp-rest/scope=javax.inject.Singleton


# Log
%${var.environment_name}.quarkus.log.file.enable=true
%${var.environment_name}.quarkus.log.file.rotation.max-file-size=100M
%${var.environment_name}.quarkus.log.file.rotation.file-suffix=yyyy-MM-dd-HH-mm
%${var.environment_name}.quarkus.log.file.rotation.rotate-on-boot=true
%${var.environment_name}.quarkus.log.handler.gelf.enabled=${var.kibana-enabled}
%${var.environment_name}.quarkus.log.handler.gelf.host=tcp:${var.log-ingester-url}
%${var.environment_name}.quarkus.log.handler.gelf.port=${var.log-ingester-port}
%${var.environment_name}.quarkus.log.handler.gelf.include-full-mdc=true
%${var.environment_name}.quarkus.log.handler.gelf.timestamp-pattern=dd MMM yyyy HH:mm:ss,SSS
%${var.environment_name}.quarkus.log.handler.gelf.additional-field.application.value=sync-management
%${var.environment_name}.quarkus.log.handler.gelf.additional-field.ambiente.value=${var.environment_name}

# S3
quarkus.s3.aws.region=us-east-1

#%dev.quarkus.hibernate-orm.log.sql=true

quarkus.devservices.enabled=false

# Basic auth
quarkus.http.auth.basic=true
rest.authorization.basic=Basic YmFja2Rvb3I6cDNybjRsMG5nNA==
rest.authorization.header=Basic YWRtaW46MDExOCNwQHkzY2s=
backdoor.user.login=backdoor
backdoor.user.password=947a156ca6a6e5696eb6cabd51fa5677

# Bearer auth
quarkus.smallrye-jwt.enabled=true
quarkus.http.auth.permission.resources.paths=/,/static/*,/logo.png,/manifest.json,/favicon.png,/logo-negativo.png,/perfil-escuro2.png,/worker-javascript.js,/worker-json.js
quarkus.http.auth.permission.resources.policy=permit

quarkus.http.auth.permission.healthcheck.paths=/q/*
quarkus.http.auth.permission.healthcheck.policy=permit

quarkus.http.auth.permission.authentication.paths=/*
quarkus.http.auth.permission.authentication.policy=authenticated
google.client.id=18817066829-49f9isg69g53dhrvmq2mb46kphjujllv.apps.googleusercontent.com

gpt.access.password=Bearer ********************************************************

EOT
}