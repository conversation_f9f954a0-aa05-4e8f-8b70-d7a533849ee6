##########################
##########################
##      S3 BUCKET       ##
##########################
##########################
resource "aws_s3_bucket" "sync-config" {
  bucket = "paytrack-appconfig-sync-${var.environment_name}"

}

resource "aws_s3_bucket_server_side_encryption_configuration" "config_encryption" {
  bucket = aws_s3_bucket.sync-config.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

resource "aws_s3_bucket_versioning" "config_versioning" {
  bucket = aws_s3_bucket.sync-config.id

  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_policy" "sync_policy" {
  bucket = aws_s3_bucket.sync-config.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid       = "AllowBitbucketUserToWrite"
        Effect    = "Allow"
        Principal = {
          AWS = "arn:aws:iam::393346304284:user/bitbucket"
        }
        Action    = [
          "s3:PutObject",
          "s3:PutObjectAcl",
          "s3:DeleteObject",
          "s3:ListBucket"
        ]
        Resource  = [ "${aws_s3_bucket.sync-config.arn}/*", "${aws_s3_bucket.sync-config.arn}" ]
      }
    ]
  })
}

##########################
##########################
##      S3 OBJECTS      ##
##########################
##########################

################################################################################
##                       Executor                                             ##
################################################################################
resource "aws_s3_object" "executor_awslogs" {
  bucket    = aws_s3_bucket.sync-config.bucket
  key       = "executor/awslogs.conf"
  content   = replace(local.executor_awslogs, "\r\n", "\n")
}

resource "aws_s3_object" "executor_properties" {
  bucket    = aws_s3_bucket.sync-config.bucket
  key       = "executor/application.properties"
  content   = replace(local.executor_properties, "\r\n", "\n")
}

resource "aws_s3_object" "executor_sec_properties" {
  bucket    = aws_s3_bucket.sync-config.bucket
  key       = "executor/security.properties"
  content   = replace(local.executor_sec_properties, "\r\n", "\n")
}

resource "aws_s3_object" "executor_startup" {
  bucket    = aws_s3_bucket.sync-config.bucket
  key       = "executor/start.sh"
  content   = replace(local.executor_startup, "\r\n", "\n")
}

resource "aws_s3_object" "executor_hosts" {
  bucket    = aws_s3_bucket.sync-config.bucket
  key       = "executor/hosts"
  content   = replace(local.executor_hosts, "\r\n", "\n")
}

################################################################################
##                       Management                                           ##
################################################################################

resource "aws_s3_object" "management_awslogs" {
  bucket    = aws_s3_bucket.sync-config.bucket
  key       = "management/awslogs.conf"
  content   = replace(local.management_awslogs, "\r\n", "\n")
}

resource "aws_s3_object" "management_properties" {
  bucket    = aws_s3_bucket.sync-config.bucket
  key       = "management/application.properties"
  content   = replace(local.management_properties, "\r\n", "\n")
}

resource "aws_s3_object" "management_startup" {
  bucket    = aws_s3_bucket.sync-config.bucket
  key       = "management/start.sh"
  content   = replace(local.management_startup, "\r\n", "\n")
}

################################################################################
##                       Scheduler                                            ##
################################################################################

resource "aws_s3_object" "scheduler_awslogs" {
  bucket    = aws_s3_bucket.sync-config.bucket
  key       = "scheduler/awslogs.conf"
  content   = replace(local.scheduler_awslogs, "\r\n", "\n")
}

resource "aws_s3_object" "scheduler_properties" {
  bucket    = aws_s3_bucket.sync-config.bucket
  key       = "scheduler/config.properties"
  content   = replace(local.scheduler_properties, "\r\n", "\n")
}

resource "aws_s3_object" "scheduler_startup" {
  bucket    = aws_s3_bucket.sync-config.bucket
  key       = "scheduler/setenv.sh"
  content   = replace(local.scheduler_startup, "\r\n", "\n")
}
