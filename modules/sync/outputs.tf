output "vpc_id" {
  value = aws_vpc.sync.id
}

output "vpc_cidr_block" {
  description = "CIDR Block da VPC existente"
  value       = aws_vpc.sync.cidr_block
}

output "alb_dns_name" {
  value = aws_lb.sync.dns_name
}

output "private_subnet_ids" {
  description = "IDs das subnets privadas"
  value       = [
    aws_subnet.sync-east1-a-private.id,
    aws_subnet.sync-east1-b-private.id,
    aws_subnet.sync-east1-c-private.id
  ]
}
output "public_subnet_ids" {
  description = "IDs das subnets públicas"
  value       = [
    aws_subnet.sync-east1-a-public.id,
    aws_subnet.sync-east1-b-public.id,
    aws_subnet.sync-east1-c-public.id
  ]
}
output "private_route_table_id" {
  description = "ID da tabela de rotas privada"
  value       = aws_route_table.sync_private.id
}
output "public_route_table_id" {
  description = "ID da tabela de rotas pública"
  value       = aws_route_table.sync_public.id
}