resource "aws_db_instance" "sync" {
  identifier                    = "sync"
  engine                        = "mysql"
  engine_version                = "8.0"
  username                      = "koda<PERSON>"
  password                      = var.rds_master_password
  parameter_group_name          = aws_db_parameter_group.sync.name
  skip_final_snapshot           = true
  vpc_security_group_ids        = [ aws_security_group.sync_rds.id ]
  db_subnet_group_name          = aws_db_subnet_group.sync_subnet_group.name
  publicly_accessible           = false
  apply_immediately             = true
  performance_insights_enabled  = false
  #Atualizações
  auto_minor_version_upgrade    = true
  maintenance_window            = "Mon:21:31-Mon:22:59"
  #Backup
  backup_window                 = "21:00-21:30"
  backup_retention_period       = 7
  copy_tags_to_snapshot         = true
  #Hardware resources
  instance_class                = var.rds_instance_size
  allocated_storage             = var.storage_size
  #collation
  #character_set_name            = "utf8mb4_general_ci"

  tags = {
    schedule    = "Auto-Shutdown"
    Name        =   "sync"
  }

}

# Subnet group
resource "aws_db_subnet_group" "sync_subnet_group" {
  name       = "sync"
  subnet_ids = [ aws_subnet.sync-east1-a-private.id, aws_subnet.sync-east1-b-private.id, aws_subnet.sync-east1-c-private.id ]

}

# SG RDS
resource "aws_security_group" "sync_rds" {
  name   = "sync-rds-security-group"
  vpc_id = aws_vpc.sync.id

  ingress {
    from_port   = 3306
    to_port     = 3306
    protocol    = "tcp"
    cidr_blocks = concat([aws_vpc.sync.cidr_block], var.allowed_cidr_blocks)
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = [aws_vpc.sync.cidr_block]
  }

  tags = {
    Name = "rds-sg"
  }
}

