variable "sync-ami" {
  type    = string
  default = "ami-0d91834ff1a8d8b4a"
}

variable "asg_executor_max_size" {
  type    = number
  default = 2
}

variable "asg_executor_min_size" {
  type    = number
  default = 2
}

variable "asg_executor_desired_size" {
  type    = number
  default = 2
}

variable "asg_management_max_size" {
  type    = number
  default = 2
}

variable "asg_management_min_size" {
  type    = number
  default = 2
}

variable "asg_management_desired_size" {
  type    = number
  default = 2
}

variable "asg_scheduler_max_size" {
  type    = number
  default = 1
}

variable "asg_scheduler_min_size" {
  type    = number
  default = 1
}

variable "asg_scheduler_desired_size" {
  type    = number
  default = 1
}

variable "allowed_internal_subnets" {
  type    = list(string)
  default = [
    "192.168.0.0/16",
    "172.16.0.0/12",
    "10.0.0.0/8"
  ]
}

variable "destinations-eks-dev-vpc" {
  type = set(string)
  default = [
    "10.20.0.0/16"
  ]
}

variable "destinations-vpn" {
  type = set(string)
  default = [ 
    "172.24.52.101/32",
    "172.26.16.37/32",
    "10.0.0.81/32",
    "10.24.1.101/32",
    "10.33.115.160/32",
    "10.33.115.238/32",
    "10.103.248.7/32",
    "172.26.140.0/24",
    "192.168.9.0/24",
    "192.168.11.0/24",
    "192.168.12.0/24",
    "10.0.16.0/24",
    "10.50.100.0/24",
    "10.198.7.0/24",
    "10.198.19.0/24",
    "10.198.24.0/24",
    "10.55.200.0/21",
    "10.1.0.0/16",
    "10.10.0.0/16",
    "10.98.0.0/16",
    "10.209.0.0/16"
  ]
}

#MySQL databases and users
variable "sync_management_db_user" {
  type    = string
  default = "sync_management"
}
variable "sync_database" {
  type    = string
  default = "sync"
}
variable "scheduler_db_user" {
  type    = string
  default = "scheduler"
}
variable "scheduler_database" {
  type    = string
  default = "scheduler"
}

data "aws_ami" "latest_imagebuilder_ami_java17" {
  owners      = ["self"]
  most_recent = true

  name_regex = "^paytrack_sync_hml_java_17_amazonlinux_2023.*"

  filter {
    name   = "state"
    values = ["available"]
  }

  filter {
    name   = "owner-id"
    values = ["${data.aws_caller_identity.current.account_id}"]
  }
}

data "aws_ami" "latest_imagebuilder_ami_java8" {
  owners      = ["self"]
  most_recent = true

  name_regex = "^paytrack_sync_scheduler_hml_java_8*"

  filter {
    name   = "state"
    values = ["available"]
  }

  filter {
    name   = "owner-id"
    values = ["${data.aws_caller_identity.current.account_id}"]
  }
}

data "aws_caller_identity" "current" {}
