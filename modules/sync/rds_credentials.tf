resource "aws_secretsmanager_secret" "sync_management_db" {
  name     = "sync_management_db_password"
}

resource "random_password" "sync_management_db" {
  length  = 20
  special = false
}

resource "aws_secretsmanager_secret_version" "sync_management_db" {
  secret_id = aws_secretsmanager_secret.sync_management_db.id
  secret_string = jsonencode({
    username = var.sync_management_db_user
    password = random_password.sync_management_db.result
  })
}

resource "aws_secretsmanager_secret" "scheduler_db" {
  name     = "scheduler_db_password"
}

resource "random_password" "scheduler_db" {
  length  = 20
  special = false
}

resource "aws_secretsmanager_secret_version" "scheduler_db" {
  secret_id     = aws_secretsmanager_secret.scheduler_db.id
  secret_string = jsonencode({
    username = var.sync_management_db_user
    password = random_password.scheduler_db.result
  })
}

#Paystore
resource "random_password" "paystore" {
  length  = 20
  special = false
}

resource "aws_secretsmanager_secret" "paystore" {
  name     = "paystore_sync"
}

resource "aws_secretsmanager_secret_version" "paystore" {
  secret_id     = aws_secretsmanager_secret.paystore.id
  secret_string = jsonencode({
    username = "paystore"
    password = random_password.paystore.result
  })
}