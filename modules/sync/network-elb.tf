# Application Load Balancer
resource "aws_lb" "sync" {
  name               = "sync-${var.environment_name}"
  internal           = false
  load_balancer_type = "application"
  security_groups    = [  aws_security_group.sync-alb.id ]
  subnets            = [  aws_subnet.sync-east1-a-public.id,
                          aws_subnet.sync-east1-b-public.id,
                          aws_subnet.sync-east1-c-public.id
                          ]

  depends_on = [ aws_vpc.sync ]
}

resource "aws_security_group" "sync-alb" {
  name        = "sync"
  description = "Allow HTTP related protocols"

  vpc_id = aws_vpc.sync.id
  
  #Allow application traffic 
  ingress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = [ "0.0.0.0/0" ]
  }
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = [ "0.0.0.0/0" ]
  }

  depends_on = [ aws_vpc.sync ]
}

# Listener HTTPS
resource "aws_lb_listener" "https" {
  load_balancer_arn = aws_lb.sync.arn
  port              = 443
  protocol          = "HTTPS"
  ssl_policy        = "ELBSecurityPolicy-2016-08"
  certificate_arn   = "arn:aws:acm:us-east-1:560211763190:certificate/674d5a1f-073d-437d-8592-39e87158f1c9"

  default_action {
    type             = "fixed-response"
    fixed_response {
      content_type = "text/plain"
      message_body = "Default response"
      status_code  = "404"
    }
  }
}

#listener HTTP, redirect HTTPS
resource "aws_lb_listener" "http_redirect" {
  load_balancer_arn = aws_lb.sync.arn
  port              = "80"
  protocol          = "HTTP"

  default_action {
    type = "redirect"

    redirect {
      port        = "443"
      protocol    = "HTTPS"
      status_code = "HTTP_301"
    }
  }
}

##############################################################################################
##                                                                                          ##
##                  EXECUTOR RULES                                                          ##
##                                                                                          ##
##############################################################################################

resource "aws_lb_listener_rule" "sync_executor" {
  action {
    order            = "2"
    target_group_arn = aws_lb_target_group.executor.arn
    type             = "forward"
  }

  condition {
    host_header {
      values = ["sync.${var.environment_name}.paytrack.com.br"]
    }
  }

  condition {
    path_pattern {
      values = ["/executor/*"]
    }
  }

  listener_arn = aws_lb_listener.https.arn
  priority     = 1
}

resource "aws_lb_target_group" "executor" {
  deregistration_delay = 300

  health_check {
    enabled             = true
    healthy_threshold   = 2
    interval            = 30
    matcher             = "200"
    path                = "/executor/q/health"
    port                = "traffic-port"
    protocol            = "HTTP"
    timeout             = 5
    unhealthy_threshold = 2
  }

  load_balancing_algorithm_type = "round_robin"
  name                          = "sync-executor-${var.environment_name}"
  port                          = 8080
  protocol                      = "HTTP"
  protocol_version              = "HTTP1"
  slow_start                    = 0

  stickiness {
    cookie_duration = 86400
    enabled         = true
    type            = "lb_cookie"
  }

  target_type = "instance"
  vpc_id      = aws_vpc.sync.id
}


##############################################################################################
##                                                                                          ##
##                  MANAGEMENT RULES                                                        ##
##                                                                                          ##
##############################################################################################

resource "aws_lb_listener_rule" "sync_management" {
  action {
    order            = "1"
    target_group_arn = aws_lb_target_group.management.arn
    type             = "forward"
  }

  condition {
    host_header {
      values = ["sync.${var.environment_name}.paytrack.com.br"]
    }
  }

  listener_arn = aws_lb_listener.https.arn
  priority     = 3
}

resource "aws_lb_target_group" "management" {
  deregistration_delay = 300

  health_check {
    enabled             = true
    healthy_threshold   = 2
    interval            = 30
    matcher             = "200"
    path                = "/q/health"
    port                = "traffic-port"
    protocol            = "HTTP"
    timeout             = 5
    unhealthy_threshold = 2
  }

  load_balancing_algorithm_type = "round_robin"
  name                          = "sync-management-${var.environment_name}"
  port                          = 8080
  protocol                      = "HTTP"
  protocol_version              = "HTTP1"
  slow_start                    = 0

  stickiness {
    cookie_duration = 86400
    enabled         = false
    type            = "lb_cookie"
  }

  target_type = "instance"
  vpc_id      = aws_vpc.sync.id
}


##############################################################################################
##                                                                                          ##
##                                 SCHEDULER RULES                                          ##
##                                                                                          ##
##############################################################################################

resource "aws_lb_listener_rule" "sync_scheduler_staging" {
  action {
    order            = "1"
    target_group_arn = aws_lb_target_group.sync_scheduler_staging.arn
    type             = "forward"
  }

  condition {
    host_header {
      values = ["scheduler.${var.environment_name}.paytrack.com.br"]
    }
  }

  listener_arn = aws_lb_listener.https.arn
  priority     = 2
}

resource "aws_lb_target_group" "sync_scheduler_staging" {
  deregistration_delay = 300

  health_check {
    enabled             = true
    healthy_threshold   = 2
    interval            = 30
    matcher             = "200-404"
    path                = "/executor/q/metrics"
    port                = "traffic-port"
    protocol            = "HTTP"
    timeout             = 5
    unhealthy_threshold = 2
  }

  load_balancing_algorithm_type = "round_robin"
  name                          = "sync-scheduler-${var.environment_name}"
  port                          = 8080
  protocol                      = "HTTP"
  protocol_version              = "HTTP1"
  slow_start                    = 0

  stickiness {
    cookie_duration = 86400
    enabled         = false
    type            = "lb_cookie"
  }

  target_type = "instance"
  vpc_id      = aws_vpc.sync.id
}
