#################################
##    Comunicação externa      ##
#################################

resource "aws_nat_gateway" "sync" {
  connectivity_type   = "public"
  subnet_id           = aws_subnet.sync-east1-a-public.id
  allocation_id       = aws_eip.nat_gateway.id

  depends_on = [
    aws_vpc.sync,
    aws_eip.nat_gateway
  ]
}

resource "aws_eip" "nat_gateway" {
  domain   = "vpc"
}

resource "aws_internet_gateway" "sync" {
  vpc_id            = aws_vpc.sync.id

  depends_on = [
    aws_vpc.sync
  ]

}

###########################
##  Public subnets route
###########################
resource "aws_route" "public_subnet" {
  route_table_id                = aws_route_table.sync_public.id
  destination_cidr_block        = "0.0.0.0/0"
  gateway_id                    = aws_internet_gateway.sync.id
  
  depends_on = [
    aws_internet_gateway.sync
  ]
}


#Private subnet to NAT Gateway
resource "aws_route" "private_subnet_to_internet" {
  route_table_id                = aws_route_table.sync_private.id
  destination_cidr_block        = "0.0.0.0/0"
  nat_gateway_id                = aws_nat_gateway.sync.id
  
  depends_on = [
    aws_nat_gateway.sync
  ]
}

###########################
##  S3 VPC Endpoint
###########################

resource "aws_vpc_endpoint" "s3" {
  vpc_id       = aws_vpc.sync.id
  service_name = "com.amazonaws.us-east-1.s3"
}

resource "aws_vpc_endpoint_route_table_association" "s3_sync_private" {
  route_table_id  = aws_route_table.sync_private.id
  vpc_endpoint_id = aws_vpc_endpoint.s3.id
}

resource "aws_vpc_endpoint_route_table_association" "s3_sync_public" {
  route_table_id  = aws_route_table.sync_public.id
  vpc_endpoint_id = aws_vpc_endpoint.s3.id
}

###########################
##  SSM VPC Endpoint
###########################

resource "aws_vpc_endpoint" "ssm" {
  vpc_id              = aws_vpc.sync.id
  service_name        = "com.amazonaws.us-east-1.ssm"
  vpc_endpoint_type   = "Interface"
}

#resource "aws_vpc_endpoint_route_table_association" "ssm_sync_private" {
#  route_table_id  = aws_route_table.sync_private.id
#  vpc_endpoint_id = aws_vpc_endpoint.ssm.id
#}
#
#resource "aws_vpc_endpoint_route_table_association" "ssm_sync_public" {
#  route_table_id  = aws_route_table.sync_public.id
#  vpc_endpoint_id = aws_vpc_endpoint.ssm.id
#}
