#################
##    VPC      ##
#################
resource "aws_vpc" "sync" {
  cidr_block            = "**********/24"
  enable_dns_hostnames  = "true"

}

#########################
#                       #
#   Private subnets     #
#                       #
#########################
resource "aws_subnet" "sync-east1-a-private" {
  vpc_id            = aws_vpc.sync.id
  cidr_block        = "**********/28"
  availability_zone = "us-east-1a"

  depends_on = [ aws_vpc.sync ]

}

resource "aws_subnet" "sync-east1-b-private" {
  vpc_id            = aws_vpc.sync.id
  cidr_block        = "***********/28"
  availability_zone = "us-east-1b"

  depends_on = [ aws_vpc.sync ]

}

resource "aws_subnet" "sync-east1-c-private" {
  vpc_id            = aws_vpc.sync.id
  cidr_block        = "***********/28"
  availability_zone = "us-east-1c"

  depends_on = [ aws_vpc.sync ]

}
#########################
#                       #
#   Public subnets      #
#                       #
#########################
resource "aws_subnet" "sync-east1-a-public" {
  vpc_id            = aws_vpc.sync.id
  cidr_block        = "***********/28"
  availability_zone = "us-east-1a"

  depends_on = [ aws_vpc.sync ]

  tags = {
    Name = "public-sync"
  }

}

resource "aws_subnet" "sync-east1-b-public" {
  vpc_id            = aws_vpc.sync.id
  cidr_block        = "172.28.0.64/28"
  availability_zone = "us-east-1b"

  depends_on = [ aws_vpc.sync ]

  tags = {
    Name = "public-sync"
  }


}

resource "aws_subnet" "sync-east1-c-public" {
  vpc_id            = aws_vpc.sync.id
  cidr_block        = "172.28.0.80/28"
  availability_zone = "us-east-1c"
  
  depends_on = [ aws_vpc.sync ]

  tags = {
    Name = "public-sync"
  }

}


######################################
##                                  ##
##                                  ##
##      ROUTE TABLES                ##
##                                  ##
##                                  ##
######################################


resource "aws_route_table" "sync_private" {
  vpc_id      = aws_vpc.sync.id

  depends_on = [ aws_vpc.sync ]

}

resource "aws_route_table_association" "sync-east1-a-private" {
    subnet_id      = aws_subnet.sync-east1-a-private.id
    route_table_id = aws_route_table.sync_private.id

    depends_on = [ aws_route_table.sync_private ]

}

resource "aws_route_table_association" "sync-east1-b-private" {
    subnet_id      = aws_subnet.sync-east1-b-private.id
    route_table_id = aws_route_table.sync_private.id

    depends_on = [ aws_route_table.sync_private ]

}

resource "aws_route_table_association" "sync-east1-c-private" {
    subnet_id      = aws_subnet.sync-east1-c-private.id
    route_table_id = aws_route_table.sync_private.id

    depends_on = [ aws_route_table.sync_private ]

}

####################################
##                                ##
##                                ##
##      PUBLIC ROUTE TABLE        ##
##                                ## 
####################################

resource "aws_route_table" "sync_public" {
  vpc_id      = aws_vpc.sync.id

}

resource "aws_route_table_association" "sync-east1-a-public" {
    subnet_id      = aws_subnet.sync-east1-a-public.id
    route_table_id = aws_route_table.sync_public.id

    depends_on = [ aws_route_table.sync_public ]

}


resource "aws_route_table_association" "sync-east1-b-public" {
    subnet_id      = aws_subnet.sync-east1-b-public.id
    route_table_id = aws_route_table.sync_public.id

    depends_on = [ aws_route_table.sync_public ]
    
}


resource "aws_route_table_association" "sync-east1-c-public" {
    subnet_id      = aws_subnet.sync-east1-c-public.id
    route_table_id = aws_route_table.sync_public.id

    depends_on = [ aws_route_table.sync_public ]
    
}

#########################
#                       #
#   VPC subnet          #
#                       #
#########################
resource "aws_subnet" "sync-east1-a-vpn" {
  vpc_id            = aws_vpc.sync.id
  cidr_block        = "172.28.0.96/27"
  availability_zone = "us-east-1a"

  tags = {
    Name = "vpn-client"
  }

  depends_on = [ aws_vpc.sync ]

}