locals {
  executor_properties = <<EOT

drive.delegated.user=<EMAIL>

quarkus.ssl.native=true
# HTTP
quarkus.http.root-path=/executor
quarkus.http.cors=true
quarkus.http.cors.access-control-allow-credentials=true

# Database
quarkus.datasource.db-kind=mysql
%${var.environment_name}.quarkus.datasource.username=${var.sync_management_db_user}
%${var.environment_name}.quarkus.datasource.password=${random_password.sync_management_db.result}
%${var.environment_name}.quarkus.datasource.jdbc.url=jdbc:mysql://sync-db.${var.environment_name}.paytrack.com.br:3306/${var.sync_database}
quarkus.datasource.metrics.enabled=true
quarkus.datasource.jdbc.enable-metrics=true

# KAFKA
%${var.environment_name}.kafka.bootstrap.servers=b-1.staging.4khtvk.c13.kafka.us-east-1.amazonaws.com:9094,b-2.staging.4khtvk.c13.kafka.us-east-1.amazonaws.com:9094,b-3.staging.4khtvk.c13.kafka.us-east-1.amazonaws.com:9094
%${var.environment_name}.kafka.group.id=executor
%${var.environment_name}.kafka.max.request.size=25165824
%${var.environment_name}.kafka.compression.type=gzip
%${var.environment_name}.kafka.security.protocol=SSL

# JWT
mp.jwt.verify.publickey.location=publickey.pem

# Log
%${var.environment_name}.quarkus.log.file.format=%d{yyyy-MM-dd HH:mm:ss,SSS} %h %N[%i] %-5p [%c{3.}] [%X] (%t) %s%e%n
%${var.environment_name}.quarkus.log.file.enable=true
%${var.environment_name}.quarkus.log.file.rotation.max-file-size=100M
%${var.environment_name}.quarkus.log.file.rotation.file-suffix=yyyy-MM-dd
%${var.environment_name}.quarkus.log.file.rotation.rotate-on-boot=true
%${var.environment_name}.quarkus.log.handler.gelf.enabled=${var.kibana-enabled}
%${var.environment_name}.quarkus.log.handler.gelf.host=tcp:${var.log-ingester-url}
%${var.environment_name}.quarkus.log.handler.gelf.port=${var.log-ingester-port}
%${var.environment_name}.quarkus.log.handler.gelf.include-full-mdc=true
%${var.environment_name}.quarkus.log.handler.gelf.timestamp-pattern=dd MMM yyyy HH:mm:ss,SSS
%${var.environment_name}.quarkus.log.handler.gelf.additional-field.application.value=sync-executor
%${var.environment_name}.quarkus.log.handler.gelf.additional-field.ambiente.value=${var.environment_name}


# S3
quarkus.s3.aws.region=us-east-1
bucket.name=paytrack-sync-artifacts

#Test
%test.drive.delegated.user=<EMAIL>

quarkus.devservices.enabled=false

#LDAP
quarkus.naming.enable-jndi=true

# Basic auth
quarkus.http.auth.basic=true
rest.authorization.basic=Basic YmFja2Rvb3I6cDNybjRsMG5nNA==
rest.authorization.header=Basic YWRtaW46MDExOCNwQHkzY2s=
backdoor.user.login=backdoor
backdoor.user.password=947a156ca6a6e5696eb6cabd51fa5677

# Bearer auth
quarkus.smallrye-jwt.enabled=true
quarkus.http.auth.permission.healthcheck.paths=/executor/q/*
quarkus.http.auth.permission.healthcheck.policy=permit

quarkus.http.auth.permission.authentication.paths=/*
quarkus.http.auth.permission.authentication.policy=authenticated
google.client.id=18817066829-49f9isg69g53dhrvmq2mb46kphjujllv.apps.googleusercontent.com

EOT
}