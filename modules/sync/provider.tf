provider "aws" {
  default_tags {
    tags = {
      Name          =   "sync"
      Ambiente      =   var.environment_name
      Managed-by    =   "Terraform"
    }
  }

  region  = var.region
  profile = var.profile

}

provider "mysql" {
  endpoint = "${aws_db_instance.sync.address}"
  username = "${aws_db_instance.sync.username}"
  password = "${aws_db_instance.sync.password}"
}
terraform {
  required_providers {
    mysql = {
      source = "petoju/mysql"
      version = "3.0.67"
    }
    aws = {
      source = "hashicorp/aws"
      version = "5.82.2"
    }
  }
}
