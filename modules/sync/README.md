## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | 5.82.2 |
| <a name="requirement_mysql"></a> [mysql](#requirement\_mysql) | 3.0.67 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | 5.82.2 |
| <a name="provider_mysql"></a> [mysql](#provider\_mysql) | 3.0.67 |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [aws_autoscaling_group.executor](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/autoscaling_group) | resource |
| [aws_autoscaling_group.management](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/autoscaling_group) | resource |
| [aws_autoscaling_group.scheduler](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/autoscaling_group) | resource |
| [aws_autoscaling_schedule.executor_start](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/autoscaling_schedule) | resource |
| [aws_autoscaling_schedule.executor_stop](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/autoscaling_schedule) | resource |
| [aws_autoscaling_schedule.management_start](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/autoscaling_schedule) | resource |
| [aws_autoscaling_schedule.management_stop](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/autoscaling_schedule) | resource |
| [aws_autoscaling_schedule.scheduler_start](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/autoscaling_schedule) | resource |
| [aws_autoscaling_schedule.scheduler_stop](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/autoscaling_schedule) | resource |
| [aws_db_instance.sync](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/db_instance) | resource |
| [aws_db_parameter_group.sync](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/db_parameter_group) | resource |
| [aws_db_subnet_group.sync_subnet_group](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/db_subnet_group) | resource |
| [aws_eip.nat_gateway](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/eip) | resource |
| [aws_iam_instance_profile.executor](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/iam_instance_profile) | resource |
| [aws_iam_instance_profile.management](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/iam_instance_profile) | resource |
| [aws_iam_instance_profile.scheduler](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/iam_instance_profile) | resource |
| [aws_iam_policy.s3_get](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/iam_policy) | resource |
| [aws_iam_role.executor_role](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/iam_role) | resource |
| [aws_iam_role.management_role](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/iam_role) | resource |
| [aws_iam_role.scheduler_role](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/iam_role) | resource |
| [aws_iam_role_policy_attachment.Executor_AmazonEC2RoleforSSM](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.Executor_AmazonSSMManagedEC2InstanceDefaultPolicy](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.Executor_CloudWatchAgentServerPolicy](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.Management_AmazonEC2RoleforSSM](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.Management_AmazonSSMManagedEC2InstanceDefaultPolicy](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.Management_CloudWatchAgentServerPolicy](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.Scheduler_AmazonEC2RoleforSSM](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.Scheduler_AmazonSSMManagedEC2InstanceDefaultPolicy](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.Scheduler_CloudWatchAgentServerPolicy](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.s3GetExecutor](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.s3GetManagement](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.s3GetScheduler](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/iam_role_policy_attachment) | resource |
| [aws_internet_gateway.sync](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/internet_gateway) | resource |
| [aws_launch_template.executor](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/launch_template) | resource |
| [aws_launch_template.management](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/launch_template) | resource |
| [aws_launch_template.scheduler](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/launch_template) | resource |
| [aws_lb.sync](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/lb) | resource |
| [aws_lb_listener.http_redirect](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/lb_listener) | resource |
| [aws_lb_listener.https](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/lb_listener) | resource |
| [aws_lb_listener_rule.sync_executor](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/lb_listener_rule) | resource |
| [aws_lb_listener_rule.sync_management](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/lb_listener_rule) | resource |
| [aws_lb_target_group.executor](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/lb_target_group) | resource |
| [aws_lb_target_group.management](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/lb_target_group) | resource |
| [aws_nat_gateway.sync](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/nat_gateway) | resource |
| [aws_route.ecs_to_sync](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/route) | resource |
| [aws_route.eks_to_sync](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/route) | resource |
| [aws_route.private_subnet_to_internet](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/route) | resource |
| [aws_route.public_subnet](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/route) | resource |
| [aws_route.sync_to_ecs](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/route) | resource |
| [aws_route.sync_to_eks](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/route) | resource |
| [aws_route53_record.rds_developer](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/route53_record) | resource |
| [aws_route53_record.rds_staging](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/route53_record) | resource |
| [aws_route53_record.sync_developer](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/route53_record) | resource |
| [aws_route53_record.sync_staging](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/route53_record) | resource |
| [aws_route_table.sync_private](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/route_table) | resource |
| [aws_route_table.sync_public](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/route_table) | resource |
| [aws_route_table_association.sync-east1-a-private](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/route_table_association) | resource |
| [aws_route_table_association.sync-east1-a-public](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/route_table_association) | resource |
| [aws_route_table_association.sync-east1-b-private](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/route_table_association) | resource |
| [aws_route_table_association.sync-east1-b-public](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/route_table_association) | resource |
| [aws_route_table_association.sync-east1-c-private](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/route_table_association) | resource |
| [aws_route_table_association.sync-east1-c-public](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/route_table_association) | resource |
| [aws_s3_bucket.sync-config](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/s3_bucket) | resource |
| [aws_s3_bucket_policy.sync_policy](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/s3_bucket_policy) | resource |
| [aws_s3_bucket_server_side_encryption_configuration.config_encryption](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/s3_bucket_server_side_encryption_configuration) | resource |
| [aws_s3_bucket_versioning.config_versioning](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/s3_bucket_versioning) | resource |
| [aws_s3_object.executor_awslogs](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/s3_object) | resource |
| [aws_s3_object.executor_hosts](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/s3_object) | resource |
| [aws_s3_object.executor_properties](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/s3_object) | resource |
| [aws_s3_object.executor_sec_properties](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/s3_object) | resource |
| [aws_s3_object.executor_startup](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/s3_object) | resource |
| [aws_s3_object.management_awslogs](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/s3_object) | resource |
| [aws_s3_object.management_properties](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/s3_object) | resource |
| [aws_s3_object.management_startup](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/s3_object) | resource |
| [aws_s3_object.scheduler_awslogs](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/s3_object) | resource |
| [aws_s3_object.scheduler_properties](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/s3_object) | resource |
| [aws_s3_object.scheduler_startup](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/s3_object) | resource |
| [aws_security_group.executor](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/security_group) | resource |
| [aws_security_group.management](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/security_group) | resource |
| [aws_security_group.scheduler](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/security_group) | resource |
| [aws_security_group.sync-alb](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/security_group) | resource |
| [aws_security_group.sync_rds](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/security_group) | resource |
| [aws_subnet.sync-east1-a-private](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/subnet) | resource |
| [aws_subnet.sync-east1-a-public](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/subnet) | resource |
| [aws_subnet.sync-east1-a-vpn](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/subnet) | resource |
| [aws_subnet.sync-east1-b-private](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/subnet) | resource |
| [aws_subnet.sync-east1-b-public](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/subnet) | resource |
| [aws_subnet.sync-east1-c-private](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/subnet) | resource |
| [aws_subnet.sync-east1-c-public](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/subnet) | resource |
| [aws_vpc.sync](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/vpc) | resource |
| [aws_vpc_endpoint.s3](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/vpc_endpoint) | resource |
| [aws_vpc_endpoint.ssm](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/vpc_endpoint) | resource |
| [aws_vpc_endpoint_route_table_association.s3_sync_private](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/vpc_endpoint_route_table_association) | resource |
| [aws_vpc_endpoint_route_table_association.s3_sync_public](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/vpc_endpoint_route_table_association) | resource |
| [aws_vpc_peering_connection.ecs_hml](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/vpc_peering_connection) | resource |
| [aws_vpc_peering_connection.eks_hml](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/resources/vpc_peering_connection) | resource |
| [mysql_database.scheduler](https://registry.terraform.io/providers/petoju/mysql/3.0.67/docs/resources/database) | resource |
| [mysql_database.sync](https://registry.terraform.io/providers/petoju/mysql/3.0.67/docs/resources/database) | resource |
| [mysql_grant.scheduler](https://registry.terraform.io/providers/petoju/mysql/3.0.67/docs/resources/grant) | resource |
| [mysql_grant.sync_management](https://registry.terraform.io/providers/petoju/mysql/3.0.67/docs/resources/grant) | resource |
| [mysql_user.scheduler](https://registry.terraform.io/providers/petoju/mysql/3.0.67/docs/resources/user) | resource |
| [mysql_user.sync_management](https://registry.terraform.io/providers/petoju/mysql/3.0.67/docs/resources/user) | resource |
| [aws_iam_policy_document.executor_assume_role](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/data-sources/iam_policy_document) | data source |
| [aws_iam_policy_document.executor_s3_get_object](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/data-sources/iam_policy_document) | data source |
| [aws_iam_policy_document.management_assume_role](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/data-sources/iam_policy_document) | data source |
| [aws_iam_policy_document.s3_get](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/data-sources/iam_policy_document) | data source |
| [aws_iam_policy_document.scheduler_assume_role](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/data-sources/iam_policy_document) | data source |
| [aws_secretsmanager_random_password.scheduler_db](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/data-sources/secretsmanager_random_password) | data source |
| [aws_secretsmanager_random_password.sync_backdoor](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/data-sources/secretsmanager_random_password) | data source |
| [aws_secretsmanager_random_password.sync_management_db](https://registry.terraform.io/providers/hashicorp/aws/5.82.2/docs/data-sources/secretsmanager_random_password) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_allowed_internal_subnets"></a> [allowed\_internal\_subnets](#input\_allowed\_internal\_subnets) | n/a | `list(string)` | <pre>[<br/>  "***********/16",<br/>  "**********/12",<br/>  "10.0.0.0/8"<br/>]</pre> | no |
| <a name="input_asg_executor_desired_size"></a> [asg\_executor\_desired\_size](#input\_asg\_executor\_desired\_size) | n/a | `number` | `2` | no |
| <a name="input_asg_executor_max_size"></a> [asg\_executor\_max\_size](#input\_asg\_executor\_max\_size) | n/a | `number` | `2` | no |
| <a name="input_asg_executor_min_size"></a> [asg\_executor\_min\_size](#input\_asg\_executor\_min\_size) | n/a | `number` | `2` | no |
| <a name="input_asg_management_desired_size"></a> [asg\_management\_desired\_size](#input\_asg\_management\_desired\_size) | n/a | `number` | `2` | no |
| <a name="input_asg_management_max_size"></a> [asg\_management\_max\_size](#input\_asg\_management\_max\_size) | n/a | `number` | `2` | no |
| <a name="input_asg_management_min_size"></a> [asg\_management\_min\_size](#input\_asg\_management\_min\_size) | n/a | `number` | `2` | no |
| <a name="input_asg_scheduler_desired_size"></a> [asg\_scheduler\_desired\_size](#input\_asg\_scheduler\_desired\_size) | n/a | `number` | `1` | no |
| <a name="input_asg_scheduler_max_size"></a> [asg\_scheduler\_max\_size](#input\_asg\_scheduler\_max\_size) | n/a | `number` | `1` | no |
| <a name="input_asg_scheduler_min_size"></a> [asg\_scheduler\_min\_size](#input\_asg\_scheduler\_min\_size) | n/a | `number` | `1` | no |
| <a name="input_destinations-eks-dev-vpc"></a> [destinations-eks-dev-vpc](#input\_destinations-eks-dev-vpc) | n/a | `set(string)` | <pre>[<br/>  "10.20.0.0/16"<br/>]</pre> | no |
| <a name="input_destinations-vpn"></a> [destinations-vpn](#input\_destinations-vpn) | n/a | `set(string)` | <pre>[<br/>  "172.24.52.101/32",<br/>  "172.26.16.37/32",<br/>  "10.0.0.81/32",<br/>  "10.24.1.101/32",<br/>  "10.33.115.160/32",<br/>  "10.33.115.238/32",<br/>  "10.103.248.7/32",<br/>  "172.26.140.0/24",<br/>  "192.168.9.0/24",<br/>  "192.168.11.0/24",<br/>  "192.168.12.0/24",<br/>  "10.0.16.0/24",<br/>  "10.50.100.0/24",<br/>  "10.198.7.0/24",<br/>  "10.198.19.0/24",<br/>  "10.198.24.0/24",<br/>  "10.55.200.0/21",<br/>  "10.1.0.0/16",<br/>  "10.10.0.0/16",<br/>  "10.98.0.0/16",<br/>  "10.209.0.0/16"<br/>]</pre> | no |
| <a name="input_dns_developer"></a> [dns\_developer](#input\_dns\_developer) | Create Route 53 records for developer subdomain | `bool` | `false` | no |
| <a name="input_dns_staging"></a> [dns\_staging](#input\_dns\_staging) | Create Route 53 records for staging subdomain | `bool` | `false` | no |
| <a name="input_ecs_cidr_block"></a> [ecs\_cidr\_block](#input\_ecs\_cidr\_block) | [ECS] Bloco em notação CIDR para roteamento | `string` | n/a | yes |
| <a name="input_ecs_rtbs"></a> [ecs\_rtbs](#input\_ecs\_rtbs) | [ECS] Tabelas de roteamento para peering | `list(string)` | n/a | yes |
| <a name="input_ecs_vpc_id"></a> [ecs\_vpc\_id](#input\_ecs\_vpc\_id) | [ECS] VPC ID | `string` | n/a | yes |
| <a name="input_eks_cidr_block"></a> [eks\_cidr\_block](#input\_eks\_cidr\_block) | [EKS] Bloco em notação CIDR para roteamento | `string` | n/a | yes |
| <a name="input_eks_rtb"></a> [eks\_rtb](#input\_eks\_rtb) | [EKS] Tabela de roteamento para peering | `string` | n/a | yes |
| <a name="input_eks_vpc_id"></a> [eks\_vpc\_id](#input\_eks\_vpc\_id) | [EKS] VPC ID | `string` | n/a | yes |
| <a name="input_enable_auto_scaling_schedule"></a> [enable\_auto\_scaling\_schedule](#input\_enable\_auto\_scaling\_schedule) | Enable or disable the automatic scaling schedule | `bool` | `false` | no |
| <a name="input_environment_name"></a> [environment\_name](#input\_environment\_name) | Nome do ambiente | `string` | n/a | yes |
| <a name="input_profile"></a> [profile](#input\_profile) | Conta onde os recursos serão criados | `string` | n/a | yes |
| <a name="input_rds_master_password"></a> [rds\_master\_password](#input\_rds\_master\_password) | Informe senha do RDS. Favor não comitar! | `string` | n/a | yes |
| <a name="input_region"></a> [region](#input\_region) | Região onde recursos serão criados | `string` | n/a | yes |
| <a name="input_scheduler_database"></a> [scheduler\_database](#input\_scheduler\_database) | n/a | `string` | `"scheduler"` | no |
| <a name="input_scheduler_db_user"></a> [scheduler\_db\_user](#input\_scheduler\_db\_user) | n/a | `string` | `"scheduler"` | no |
| <a name="input_sync-ami"></a> [sync-ami](#input\_sync-ami) | n/a | `string` | `"ami-0d91834ff1a8d8b4a"` | no |
| <a name="input_sync_database"></a> [sync\_database](#input\_sync\_database) | n/a | `string` | `"sync"` | no |
| <a name="input_sync_management_db_user"></a> [sync\_management\_db\_user](#input\_sync\_management\_db\_user) | MySQL databases and users | `string` | `"sync_management"` | no |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_alb_dns_name"></a> [alb\_dns\_name](#output\_alb\_dns\_name) | n/a |
| <a name="output_vpc_id"></a> [vpc\_id](#output\_vpc\_id) | n/a |
