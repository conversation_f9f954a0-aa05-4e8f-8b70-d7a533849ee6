variable "environment_name" {
  description = "Nome do ambiente"
  type        = string
}

variable "eks_vpc_id" {
  type = string
  description = "[EKS] VPC ID"
}

variable "eks_rtb" {
  type = string
  description = "[EKS] Tabela de roteamento para peering"
}

variable "eks_cidr_block" {
  type = string
  description = "[EKS] Bloco em notação CIDR para roteamento "
}

variable "ecs_vpc_id" {
  type = string
  description = "[ECS] VPC ID"
}

variable "ecs_rtbs" {
  type = list(string)
  description = "[ECS] Tabelas de roteamento para peering"
}


variable "ecs_cidr_block" {
  type = string
  description = "[ECS] Bloco em notação CIDR para roteamento "
}

variable "profile" {
  type = string
  description = "Conta onde os recursos serão criados"
}

variable "region" {
  type = string
  description = "Região onde recursos serão criados"
}

variable "enable_auto_scaling_schedule" {
  description = "Enable or disable the automatic scaling schedule"
  type        = bool
  default     = false
}

variable "dns_staging" {
  description = "Create Route 53 records for staging subdomain"
  type        = bool
  default     = false
}

variable "dns_developer" {
  description = "Create Route 53 records for developer subdomain"
  type        = bool
  default     = false
}

variable "rds_master_password" {
  type        = string
  description = "Informe senha do RDS. Favor não comitar!"
  sensitive   = true
}

variable "allowed_cidr_blocks" {
  description = "Lista de CIDRs adicionais permitidos para a porta 3306"
  type        = list(string)
  default     = []
}

variable "rds_instance_size" {
  type        = string
  description = "RDS instance class"
}

variable "storage_size" {
  type        = number
  description = "Disk size"
  default     = 100
}

variable "log-ingester-url" {
  type        = string
  description = "LOG ingester endpoint"
}

variable "log-ingester-port" {
  type        = string
  description = "LOG ingester port"
}

variable "db_default_collation" {
  type        = string
  description = "Database collation"
}

variable "kibana-enabled" {
  type        = string
  description = "Habilita envio para OpenSearch"
}