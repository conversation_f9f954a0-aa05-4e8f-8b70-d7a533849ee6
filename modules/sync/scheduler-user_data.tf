locals {
    sync_scheduler_user_data = <<EOF
#!/bin/bash -xe
aws s3 cp s3://${aws_s3_bucket.sync-config.bucket}/scheduler/awslogs.conf /etc/awslogs/awslogs.conf
yum install awslogs -y
systemctl enable --now awslogsd
aws s3 cp s3://${aws_s3_bucket.sync-config.bucket}/scheduler/scheduler.war /opt/tomcat9/webapps
aws s3 cp s3://${aws_s3_bucket.sync-config.bucket}/scheduler/config.properties /opt/tomcat9/bin
aws s3 cp s3://${aws_s3_bucket.sync-config.bucket}/scheduler/setenv.sh /opt/tomcat9/bin/
service tomcat start
EOF
}