locals {
  scheduler_startup = <<EOT
CATALINA_PID="$CATALINA/logs/catalina-daemon.pid"
export CATALINA_PID

JAVA_HOME=/usr/java/latest
export JAVA_HOME

export PATH=$JAVA_HOME/bin:$PATH

# JAVA OPTS
#JAVA_OPTS="$JAVA_OPTS -Duser.region=BR"
#JAVA_OPTS="$JAVA_OPTS -Duser.language=pt"
JAVA_OPTS="$JAVA_OPTS -Xms1024m"
JAVA_OPTS="$JAVA_OPTS -Xmx1536m"
JAVA_OPTS="$JAVA_OPTS -server"


# JMX conection
#JAVA_OPTS="$JAVA_OPTS -Dcom.sun.management.jmxremote.authenticate=false"
#JAVA_OPTS="$JAVA_OPTS -Dcom.sun.management.jmxremote.ssl=false"
#JAVA_OPTS="$JAVA_OPTS -Dcom.sun.management.jmxremote.port=8686"
#JAVA_OPTS="$JAVA_OPTS -Dcom.sun.management.jmxremote.rmi.port=8686"
#JAVA_OPTS="$JAVA_OPTS -Djava.rmi.server.hostname=$RMI_IP"
#JAVA_OPTS="$JAVA_OPTS -Dcom.sun.management.jmxremote.local.only=false"

# GC
JAVA_OPTS="$JAVA_OPTS -XX:+UseParallelGC"
JAVA_OPTS="$JAVA_OPTS -XX:+DisableExplicitGC"


# Other
JAVA_OPTS="$JAVA_OPTS -XX:+UnlockDiagnosticVMOptions"
JAVA_OPTS="$JAVA_OPTS -Djava.security.egd=file:/dev/./urandom"
JAVA_OPTS="$JAVA_OPTS -Djava.awt.headless=true"
JAVA_OPTS="$JAVA_OPTS -Djdk.tls.ephemeralDHKeySize=2048"
JAVA_OPTS="$JAVA_OPTS -Dfile.encoding=UTF-8"

# APP
JAVA_OPTS="$JAVA_OPTS -Dcom.paytrack.scheduler.configuration=file:/opt/tomcat9/bin/config.properties"

export JAVA_OPTS

EOT
}