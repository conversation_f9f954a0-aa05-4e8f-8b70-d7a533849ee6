resource "aws_route53_record" "rds_staging" {
  count           = var.dns_staging ? 1 : 0
  zone_id         = "Z07065491X07GH8XR89CQ"
  name            = "sync-db.staging.paytrack.com.br"
  type            = "CNAME"
  ttl             = 300
  records         = [ aws_db_instance.sync.address ]
}

resource "aws_route53_record" "sync_staging" {
  count           = var.dns_staging ? 1 : 0
  zone_id         = "Z07065491X07GH8XR89CQ"
  name            = "sync.staging.paytrack.com.br"
  type            = "CNAME"
  ttl             = 300
  records         = [ aws_lb.sync.dns_name ]
}

resource "aws_route53_record" "sync_scheduler_staging" {
  count           = var.dns_staging ? 1 : 0
  zone_id         = "Z07065491X07GH8XR89CQ"
  name            = "scheduler.staging.paytrack.com.br"
  type            = "CNAME"
  ttl             = 300
  records         = [ aws_lb.sync.dns_name ]
}

resource "aws_route53_record" "rds_developer" {
  count           = var.dns_developer ? 1 : 0
  zone_id         = "Z0362491AFLEMDOXT8I8"
  name            = "sync-db.developer.paytrack.com.br"
  type            = "CNAME"
  ttl             = 300
  records         = [ aws_db_instance.sync.address ]
}

resource "aws_route53_record" "sync_developer" {
  count           = var.dns_developer ? 1 : 0
  zone_id         = "Z0362491AFLEMDOXT8I8"
  name            = "sync.developer.paytrack.com.br"
  type            = "CNAME"
  ttl             = 300
  records         = [ aws_lb.sync.dns_name ]
}