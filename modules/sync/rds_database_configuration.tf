########################
##                    ##
##        SYNC        ##
##                    ##
########################

resource "mysql_database" "sync" {
  name                  = var.sync_database
  default_collation     = var.db_default_collation
  depends_on = [ aws_db_instance.sync ]

}

resource "mysql_user" "sync_management" {
  user                  = var.sync_management_db_user
  host                  = aws_vpc.sync.cidr_block
  plaintext_password    = random_password.sync_management_db.result

  depends_on = [ aws_db_instance.sync ]

}

resource "mysql_grant" "sync_management" {
  user                  = var.sync_management_db_user
  host                  = aws_vpc.sync.cidr_block
  database              = var.sync_database
  privileges            = ["ALL"]

  depends_on = [ aws_db_instance.sync ]
}

########################
##                    ##
##      SCHEDULER     ##
##                    ##
########################

resource "mysql_database" "scheduler" {
  name                  = var.scheduler_database
  default_collation     = var.db_default_collation
  depends_on = [ aws_db_instance.sync ]
}

resource "mysql_user" "scheduler" {
  user                  = var.scheduler_db_user
  host                  = aws_vpc.sync.cidr_block
  plaintext_password    = random_password.scheduler_db.result

  depends_on = [ aws_db_instance.sync ]
}

resource "mysql_grant" "scheduler" {
  user                  = var.scheduler_db_user
  host                  = aws_vpc.sync.cidr_block
  database              = var.scheduler_database
  privileges            = ["ALL"]

  depends_on = [ aws_db_instance.sync ]
}

########################
##                    ##
##     Paystore       ##
##                    ##
########################

resource "mysql_user" "paystore" {
  user                  = "paystore"
  host                  = aws_vpc.sync.cidr_block
  plaintext_password    = random_password.sync_management_db.result

  depends_on = [ aws_db_instance.sync ]

}

resource "mysql_grant" "paystore" {
  user                  = "paystore"
  host                  = aws_vpc.sync.cidr_block
  database              = var.sync_database
  privileges            = ["ALL"]

  depends_on = [ aws_db_instance.sync ]
}