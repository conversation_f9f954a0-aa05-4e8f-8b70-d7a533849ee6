resource "aws_launch_template" "management" {
  name                                  = "sync_management_${var.environment_name}"
  description                           = "java17"
  update_default_version                = true
  

  block_device_mappings {
    device_name = "/dev/xvda"

    ebs {
      delete_on_termination = "true"
      volume_size           = "10"
      volume_type           = "gp3"
    }
  }

  disable_api_termination = "false"
  ebs_optimized           = "false"

  iam_instance_profile {
    name = aws_iam_instance_profile.management.name
  }

  image_id      = data.aws_ami.latest_imagebuilder_ami_java17.id
  instance_type = "t3.small"

  monitoring {
    enabled = "true"
  }
  user_data              = base64encode(local.sync_management_user_data)
  vpc_security_group_ids = [ aws_security_group.management.id ]

  depends_on = [ aws_security_group.management ]
}

resource "aws_autoscaling_group" "management" {
  capacity_rebalance        = true
  default_cooldown          = 60
  desired_capacity          = var.asg_management_desired_size
  force_delete              = false
  force_delete_warm_pool    = false
  health_check_grace_period = 120
  health_check_type         = "ELB"
  max_size                  = var.asg_management_max_size
  metrics_granularity       = "1Minute"
  min_size                  = var.asg_management_min_size
  name                      = "sync_management_${var.environment_name}"
  protect_from_scale_in     = false
  target_group_arns         = [aws_lb_target_group.management.arn]
  vpc_zone_identifier       = [
    aws_subnet.sync-east1-a-private.id,
    aws_subnet.sync-east1-b-private.id,
    aws_subnet.sync-east1-c-private.id
  ]

  mixed_instances_policy {
    instances_distribution {
      on_demand_allocation_strategy = "prioritized"
      spot_allocation_strategy      = "price-capacity-optimized"
    }

    launch_template {
      launch_template_specification {
        launch_template_id   = aws_launch_template.management.id
        launch_template_name = "sync_management_${var.environment_name}"
        version              = "$Default"
      }
      override {
        instance_type = "t3.medium"
      }

      override {
        instance_type = "t3a.medium"
      }
    }
  }

  tag {
    key                 = "Name"
    propagate_at_launch = true
    value               = "sync_management_${var.environment_name}"
  }

  depends_on = [ aws_vpc.sync ]
}

############################
##                        ##
##                        ##
##    SECURITY GROUP      ##
##                        ##
##                        ##
############################

resource "aws_security_group" "management" {
  name          = "sync_management"
  description   = "Allow application protocol and RFC 1918"
  vpc_id        = aws_vpc.sync.id

  ingress {
    from_port   = 8080
    to_port     = 8080
    protocol    = "tcp"
    cidr_blocks = [ "0.0.0.0/0" ]
  }

  egress {
    from_port        = 0
    to_port          = 0
    protocol         = "-1"
    cidr_blocks      = ["0.0.0.0/0"]
  }
  
  depends_on = [ aws_vpc.sync ]
}

############################
##                        ##
##                        ##
##        SCHEDULE        ##
##                        ##
##                        ##
############################

resource "aws_autoscaling_schedule" "management_stop" {
  count                   = var.enable_auto_scaling_schedule ? 1 : 0
  scheduled_action_name   = "scale-down-management"
  min_size                = 0
  max_size                = 0
  desired_capacity        = 0
  time_zone               = "America/Sao_Paulo"
  recurrence              = "0 20 * * MON-FRI"
  autoscaling_group_name  = aws_autoscaling_group.management.name
}

resource "aws_autoscaling_schedule" "management_start" {
  scheduled_action_name  = "scale-up-management"
  min_size               = 2
  max_size               = 4
  desired_capacity       = 2
  time_zone              = "America/Sao_Paulo"
  recurrence             = "0 7 * * MON-FRI"
  autoscaling_group_name = aws_autoscaling_group.management.name
}