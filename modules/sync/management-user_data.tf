locals {
    sync_management_user_data = <<EOF
#!/bin/bash -xe
mkdir -p /usr/paytrack/config/

#setup cloudwatch
sudo yum install -y amazon-cloudwatch-agent
sudo aws s3 cp s3://${aws_s3_bucket.sync-config.bucket}/management/config.json /opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json
sudo sudo systemctl restart amazon-cloudwatch-agent

#setup application
aws s3 cp s3://${aws_s3_bucket.sync-config.bucket}/management/management.jar /usr/paytrack/
aws s3 cp s3://${aws_s3_bucket.sync-config.bucket}/management/start.sh /usr/paytrack/
aws s3 cp s3://${aws_s3_bucket.sync-config.bucket}/management/application.properties /usr/paytrack/config/
chmod +x /usr/paytrack/start.sh

#APP start
/usr/paytrack/start.sh
EOF
}