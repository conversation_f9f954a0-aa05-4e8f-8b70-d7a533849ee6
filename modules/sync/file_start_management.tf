locals {
  management_startup = <<EOT
JAVA_HOME=/usr/java/latest
export JAVA_HOME

export PATH=$JAVA_HOME/bin:$PATH

JAVA_OPTS="$JAVA_OPTS -Xms1024m"
JAVA_OPTS="$JAVA_OPTS -XX:MaxRAMPercentage=70"
JAVA_OPTS="$JAVA_OPTS -Dquarkus.profile=${var.environment_name}"
JAVA_OPTS="$JAVA_OPTS -Dquarkus.config.locations=/usr/paytrack/config/"
JAVA_OPTS="$JAVA_OPTS -server"


export JAVA_OPTS
cd /usr/paytrack
nohup java $JAVA_OPTS -jar /usr/paytrack/management.jar 2>&1 >/var/log/java.log &
EOT
}