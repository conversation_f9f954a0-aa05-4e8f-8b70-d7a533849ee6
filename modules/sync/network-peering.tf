###################################################################
###################################################################
###################################################################
#                         ECS

resource "aws_vpc_peering_connection" "ecs_hml" {
  peer_vpc_id   = var.ecs_vpc_id
  vpc_id        = aws_vpc.sync.id
  auto_accept   = true

  accepter {
    allow_remote_vpc_dns_resolution = true
  }

  requester {
    allow_remote_vpc_dns_resolution = true
  }

  depends_on = [ aws_vpc.sync ]

}

resource "aws_route" "ecs_to_sync" {
  for_each                    = toset(var.ecs_rtbs)
  route_table_id              = each.value
  destination_cidr_block      = aws_vpc.sync.cidr_block
  vpc_peering_connection_id   = aws_vpc_peering_connection.ecs_hml.id

  depends_on = [ aws_vpc.sync ]
}

resource "aws_route" "sync_to_ecs" {
  route_table_id              = aws_route_table.sync_private.id
  destination_cidr_block      = var.ecs_cidr_block
  vpc_peering_connection_id   = aws_vpc_peering_connection.ecs_hml.id

  depends_on = [ aws_vpc.sync ]
}

#                         ECS
###################################################################
###################################################################
###################################################################
###################################################################
#                         EKS

resource "aws_vpc_peering_connection" "eks_hml" {
  peer_vpc_id   = var.eks_vpc_id
  vpc_id        = aws_vpc.sync.id
  auto_accept   = true

  accepter {
    allow_remote_vpc_dns_resolution = true
  }

  requester {
    allow_remote_vpc_dns_resolution = true
  }

  depends_on = [ aws_vpc.sync ]

}

resource "aws_route" "eks_to_sync" {
  route_table_id              = var.eks_rtb
  destination_cidr_block      = aws_vpc.sync.cidr_block
  vpc_peering_connection_id   = aws_vpc_peering_connection.eks_hml.id

}

resource "aws_route" "sync_to_eks" {
  route_table_id              = aws_route_table.sync_private.id
  destination_cidr_block      = var.eks_cidr_block
  vpc_peering_connection_id   = aws_vpc_peering_connection.eks_hml.id

  depends_on = [ aws_vpc.sync ]
}


#                       EKS
###################################################################
###################################################################
###################################################################