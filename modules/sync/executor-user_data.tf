locals {
    sync_executor_user_data = <<EOF
#!/bin/bash -xe
aws s3 cp s3://${aws_s3_bucket.sync-config.bucket}/executor/hosts /etc/hosts

#setup cloudwatch
sudo yum install -y amazon-cloudwatch-agent
sudo aws s3 cp s3://${aws_s3_bucket.sync-config.bucket}/executor/config.json /opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json
sudo sudo systemctl restart amazon-cloudwatch-agent

#setup application
mkdir -p /usr/paytrack/config/
aws s3 cp s3://${aws_s3_bucket.sync-config.bucket}/executor/executor.jar /usr/paytrack/
aws s3 cp s3://${aws_s3_bucket.sync-config.bucket}/executor/start.sh /usr/paytrack/
aws s3 cp s3://${aws_s3_bucket.sync-config.bucket}/executor/security.properties /usr/paytrack/config/
aws s3 cp s3://${aws_s3_bucket.sync-config.bucket}/executor/application.properties /usr/paytrack/config/
chmod +x /usr/paytrack/start.sh

#Inica app
/usr/paytrack/start.sh
EOF
}