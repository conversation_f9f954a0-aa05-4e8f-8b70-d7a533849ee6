resource "aws_iam_instance_profile" "executor" {
  name = "executor_iam_profile_${var.environment_name}"
  role = aws_iam_role.executor_role.name
}

data "aws_iam_policy_document" "executor_assume_role" {
  statement {
    effect = "Allow"

    principals {
      type        = "Service"
      identifiers = ["ec2.amazonaws.com"]
    }

    actions = ["sts:AssumeRole"]
  }

}

data "aws_iam_policy_document" "executor_s3_get_object" {
  statement {
    effect = "Allow"

    principals {
      type        = "Service"
      identifiers = ["s3.amazonaws.com"]
    }

    actions = ["s3:GetObject"]
  }
}

resource "aws_iam_role" "executor_role" {
  name               = "sync_executor_${var.environment_name}"
  path               = "/"
  assume_role_policy = data.aws_iam_policy_document.executor_assume_role.json
}

resource "aws_iam_role_policy_attachment" "Executor_AmazonEC2RoleforSSM" {
  role       = aws_iam_role.executor_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonEC2RoleforSSM"
}

resource "aws_iam_role_policy_attachment" "Executor_AmazonSSMManagedEC2InstanceDefaultPolicy" {
  role       = aws_iam_role.executor_role.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSSMManagedEC2InstanceDefaultPolicy"
}

resource "aws_iam_role_policy_attachment" "Executor_CloudWatchAgentServerPolicy" {
  role       = aws_iam_role.executor_role.name
  policy_arn = "arn:aws:iam::aws:policy/CloudWatchAgentServerPolicy"
}