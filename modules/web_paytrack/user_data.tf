locals {
    paytrack_web_user_data = <<EOF
#!/bin/bash -xe
yum install -y awslogs
mkdir /usr/paytrack -p
aws s3 cp s3://paytrack-web/${var.environment_name}/awslogs.conf /etc/awslogs/awslogs.conf
systemctl start awslogsd
aws s3 cp s3://paytrack-web/${var.environment_name}/wkhtmltoimage /usr/bin/wkhtmltoimage
aws s3 cp s3://paytrack-web/${var.environment_name}/wkhtmltopdf /usr/bin/wkhtmltopdf
chmod 777 /usr/bin/wk*
aws s3 cp s3://paytrack-web/${var.environment_name}/local.conf /etc/fonts/local.conf
aws s3 cp s3://paytrack-web/${var.environment_name}/ROOT.jar /usr/paytrack/
aws s3 sync s3://paytrack-web/${var.environment_name}/reports /usr/paytrack/reports
aws s3 cp s3://paytrack-web/${var.environment_name}/paytrack.properties /usr/paytrack/
aws s3 cp s3://paytrack-web/${var.environment_name}/setenv.sh /usr/paytrack/
chmod 777 /usr/paytrack/setenv.sh
/usr/paytrack/setenv.sh
EOF
}