locals {
  file_setenv_sh_content = <<EOT
JAVA_HOME=/usr/java/latest
export JAVA_HOME

export PATH=$JAVA_HOME/bin:$PATH

# JAVA OPTS
#JAVA_OPTS="$JAVA_OPTS -Duser.region=BR"
#JAVA_OPTS="$JAVA_OPTS -Duser.language=pt"
#JAVA_OPTS="$JAVA_OPTS -server"
JAVA_OPTS="$JAVA_OPTS -Xms1024m"
JAVA_OPTS="$JAVA_OPTS -Xmx1400m"


# JMX conection usado para depurar
#RMI_HOST=`wget -q -O - http://***************/latest/meta-data/public-hostname`
#JAVA_OPTS="$JAVA_OPTS -Dcom.sun.management.jmxremote=true"
#JAVA_OPTS="$JAVA_OPTS -Dcom.sun.management.jmxremote.authenticate=false"
#JAVA_OPTS="$JAVA_OPTS -Dcom.sun.management.jmxremote.ssl=false"
#JAVA_OPTS="$JAVA_OPTS -Dcom.sun.management.jmxremote.port=8686"
#JAVA_OPTS="$JAVA_OPTS -Dcom.sun.management.jmxremote.rmi.port=8686"
#JAVA_OPTS="$JAVA_OPTS -Djava.rmi.server.hostname=$RMI_HOST"
#JAVA_OPTS="$JAVA_OPTS -Dcom.sun.management.jmxremote.local.only=false"

# GC
JAVA_OPTS="$JAVA_OPTS -XX:+UseParallelGC"
JAVA_OPTS="$JAVA_OPTS -XX:+DisableExplicitGC"


# Other
JAVA_OPTS="$JAVA_OPTS -XX:+UnlockDiagnosticVMOptions"
JAVA_OPTS="$JAVA_OPTS -Djava.security.egd=file:/dev/./urandom"
JAVA_OPTS="$JAVA_OPTS -Djava.awt.headless=true"
JAVA_OPTS="$JAVA_OPTS -Djdk.tls.ephemeralDHKeySize=2048"
JAVA_OPTS="$JAVA_OPTS -Dhibernate.query.plan_cache_max_size=248"
JAVA_OPTS="$JAVA_OPTS -Dfile.encoding=UTF-8"
JAVA_OPTS="$JAVA_OPTS -XX:+ShowCodeDetailsInExceptionMessages"


# APP
JAVA_OPTS="$JAVA_OPTS -Dpaytrack.config=/usr/paytrack/paytrack.properties" 
JAVA_OPTS="$JAVA_OPTS -Dspring.profiles.active=logstash"

export PAYTRACK_AMBIENTE=${var.environment_name}

export JAVA_OPTS
cd /usr/paytrack
nohup java $JAVA_OPTS -jar /usr/paytrack/ROOT.jar </dev/null >/dev/null 2>&1 &

EOT
}