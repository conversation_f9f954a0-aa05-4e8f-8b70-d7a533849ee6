variable "environment_name" {
  description = "Nome do ambiente"
  type        = string
}

variable "aws_vpc_id" {
  description = "Id da vpc"
  type        = string
}

variable "lb_listener_arn" {
  description = "Arn do loadbalancer"
  type        = string
}

variable "lb_listener_priority" {
  description = "Código de prioridade para regra de entrada no loadbalancer"
  type        = string
}

variable "s3_paytrack_updater_jar" {
  description = "Informações para acesso ao jar do paytrack updater no s3"
  type = object({
    key        = string
    bucket     = string
    version_id = string
  })
}

variable "iam_role_arn_to_ec2_terminate" {
  description = "Iam role para executar a lambda de terminate das ec2"
  type        = string
}

variable "bucket_config_files" {
  description = "Bucket para criar os arquivos de configuração (Ex: .properties)"
  type        = string
}

variable "iam_user_arn_to_sqs" {
  description = "IAM user para SQS do paytrack"
  type        = string
}
