resource "aws_s3_object_copy" "local_conf" {
  bucket = var.bucket_config_files
  key    = "${var.environment_name}/local.conf"
  source = "${var.bucket_config_files}/common-files/local.conf"
  lifecycle {
    ignore_changes = [ tags_all ]
  }
}

resource "aws_s3_object_copy" "wkhtmltoimage" {
  bucket = var.bucket_config_files
  key    = "${var.environment_name}/wkhtmltoimage"
  source = "${var.bucket_config_files}/common-files/wkhtmltoimage"
  lifecycle {
    ignore_changes = [ tags_all ]
  }
}

resource "aws_s3_object_copy" "wkhtmltopdf" {
  bucket = var.bucket_config_files
  key    = "${var.environment_name}/wkhtmltopdf"
  source = "${var.bucket_config_files}/common-files/wkhtmltopdf"
  lifecycle {
    ignore_changes = [ tags_all ]
  }
}

resource "aws_s3_object" "paytrack_web_awslogs_conf" {
  bucket  = var.bucket_config_files
  key     = "${var.environment_name}/awslogs.conf"
  content = replace(local.file_awslogs_conf_content, "\r\n", "\n")
}

resource "aws_s3_object" "paytrack_web_properties" {
  bucket  = var.bucket_config_files
  key     = "${var.environment_name}/paytrack.properties"
  content = replace(local.file_properties_content, "\r\n", "\n")
}

resource "aws_s3_object" "paytrack_web_setenv_sh" {
  bucket  = var.bucket_config_files
  key     = "${var.environment_name}/setenv.sh"
  content = replace(local.file_setenv_sh_content, "\r\n", "\n")
}
