variable "environment_name" {
  type = string
  description = "nome do ambiente, será usado no DNS"
}

variable "domain" {
  type = string
  description = "developer ou staging"
}

variable "dns_zone_id" {
  type = string
  description = "Z08060781DD0FET8KXURP = developer, Z07065491X07GH8XR89CQ = staging"
}

variable "agencias_backend_lb" {
  type = string
  description = "DNS do backend do agencias api"
}

variable "profile" {
  type = string
  description = "Conta onde será criado os recursos, ver .aws/config"
}

variable "region" {
  type = string
  description = "Padrão Virgínia"
  default = "us-east-1"
}