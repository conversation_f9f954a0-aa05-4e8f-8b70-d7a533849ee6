resource "aws_route53_record" "paytrack" {
  name    = "${var.environment_name}.${var.domain}.paytrack.com.br"
  type    = "A"
  zone_id = var.dns_zone_id

  alias {
    name                   = aws_cloudfront_distribution.paytrack.domain_name
    zone_id                = aws_cloudfront_distribution.paytrack.hosted_zone_id
    evaluate_target_health = false
  }
}

resource "aws_route53_record" "paytrack_login" {
  name    = "login-${var.environment_name}.${var.domain}.paytrack.com.br"
  type    = "A"
  zone_id = var.dns_zone_id

  alias {
    name                   = aws_cloudfront_distribution.paytrack_login.domain_name
    zone_id                = aws_cloudfront_distribution.paytrack_login.hosted_zone_id
    evaluate_target_health = false
  }
}

resource "aws_route53_record" "supplier_api" {
  name    = "supplier-${var.environment_name}-api.${var.domain}.paytrack.com.br"
  type    = "A"
  zone_id = var.dns_zone_id

  alias {
    name                   = var.agencias_backend_lb
    zone_id                = "Z35SXDOTRQ7X7K"
    evaluate_target_health = true
  }
}

resource "aws_route53_record" "supplier" {
  name    = "supplier-${var.environment_name}.${var.domain}.paytrack.com.br"
  type    = "A"
  zone_id = var.dns_zone_id

  alias {
    name                   = aws_cloudfront_distribution.supplier.domain_name
    zone_id                = aws_cloudfront_distribution.supplier.hosted_zone_id
    evaluate_target_health = false
  }
}
