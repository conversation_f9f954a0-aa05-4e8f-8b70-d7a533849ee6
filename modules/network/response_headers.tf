resource "aws_cloudfront_response_headers_policy" "paytrack_no_cache" {
  name    = "paytrack-policy-no-cache${var.environment_name}"
  comment = "Managed by Terraform"

  cors_config {
    access_control_allow_credentials = false

    access_control_allow_headers {
      items = ["ALL"]
    }

    access_control_allow_methods {
      items = ["ALL"]
    }

    access_control_allow_origins {
      items = [
        "https://${var.environment_name}.${var.domain}.paytrack.com.br",
        "https://${var.environment_name}-web.${var.domain}.paytrack.com.br",
        "https://relatorios-${var.environment_name}.${var.domain}.paytrack.com.br",
        "https://api.${var.domain}.paytrack.com.br"
        ]
    }

    origin_override = true
  }

  custom_headers_config {
    items {
        header   = "Cache-Control"
        override = true
        value    = "no-cache, no-store, must-revalidate"
      }
    items {
        header   = "Pragma"
        override = true
        value    = "no-cache"
    }
  }
  security_headers_config {
    strict_transport_security {
        access_control_max_age_sec = 31536000
        include_subdomains         = true
        override                   = true
        preload                    = true
    }
  }

}

resource "aws_cloudfront_response_headers_policy" "paytrack" {
  name    = "paytrack-policy-${var.environment_name}"
  comment = "Managed by Terraform"

  cors_config {
    access_control_allow_credentials = true

    access_control_allow_headers {
      items = ["ALL"]
    }

    access_control_allow_methods {
      items = ["ALL"]
    }

    access_control_allow_origins {
      items = [
        "https://${var.environment_name}.${var.domain}.paytrack.com.br",
        "https://${var.environment_name}-web.${var.domain}.paytrack.com.br",
        "https://relatorios-${var.environment_name}.${var.domain}.paytrack.com.br",
        "https://api.${var.domain}.paytrack.com.br"
        ]
    }

    origin_override = true
  }

  security_headers_config {
    strict_transport_security {
        access_control_max_age_sec = 31536000
        include_subdomains         = true
        override                   = true
        preload                    = true
    }
  }

}