resource "aws_cloudfront_distribution" "paytrack" {
  aliases = [
    "${var.environment_name}.${var.domain}.paytrack.com.br",
  ]
  default_root_object = "index.html"
  enabled             = true
  http_version        = "http2"
  is_ipv6_enabled     = true
  price_class         = "PriceClass_All"
  retain_on_delete    = false
  wait_for_deployment = true

  default_cache_behavior {
    allowed_methods = [
      "DELETE",
      "GET",
      "HEAD",
      "OPTIONS",
      "PATCH",
      "POST",
      "PUT",
    ]
    cached_methods = [
      "GET",
      "HEAD",
    ]
    compress               = true
    default_ttl            = 86400
    max_ttl                = 31536000
    smooth_streaming       = false
    target_origin_id       = "${var.environment_name}-web.${var.domain}.paytrack.com.br"
    viewer_protocol_policy = "allow-all"
    response_headers_policy_id  = aws_cloudfront_response_headers_policy.paytrack.id

    forwarded_values {
      headers = ["*"]

      query_string = true

      cookies {
        forward = "all"
      }
    }
    #ajustar devido troca de conta
    #lambda_function_association {
    #          event_type   = "viewer-response"
    #          include_body = false
    #          lambda_arn   = "arn:aws:lambda:us-east-1:393346304284:function:CspResponseHeaders:2"
    #}

  }

  ordered_cache_behavior {
    allowed_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    cached_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    compress               = true
    default_ttl            = 86400
    max_ttl                = 31536000
    path_pattern           = "/legacy/*"
    smooth_streaming       = false
    target_origin_id       = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    response_headers_policy_id  = aws_cloudfront_response_headers_policy.paytrack.id
    viewer_protocol_policy = "redirect-to-https"

    forwarded_values {
      headers = [
        "Access-Control-Request-Headers",
        "Access-Control-Request-Method",
        "Origin",
      ]
      query_string = false

      cookies {
        forward = "none"
      }
    }
  }
  ordered_cache_behavior {
    allowed_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    cache_policy_id             = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    response_headers_policy_id  = aws_cloudfront_response_headers_policy.paytrack.id
    cached_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    compress               = true
    path_pattern           = "/menu/*"
    smooth_streaming       = false
    target_origin_id       = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    viewer_protocol_policy = "redirect-to-https"
  }
  ordered_cache_behavior {
    allowed_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    cache_policy_id             = "4135ea2d-6df8-44a3-9df3-4b5a84be39ad"
    response_headers_policy_id  = aws_cloudfront_response_headers_policy.paytrack.id
    cached_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    compress               = true
    path_pattern           = "/settings/*"
    smooth_streaming       = false
    target_origin_id       = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    viewer_protocol_policy = "redirect-to-https"
  }
  ordered_cache_behavior {
    allowed_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    cache_policy_id             = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    response_headers_policy_id  = aws_cloudfront_response_headers_policy.paytrack_no_cache.id
    cached_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    compress               = true
    default_ttl            = 0
    max_ttl                = 0
    min_ttl                = 0
    path_pattern           = "index.html"
    smooth_streaming       = false
    target_origin_id       = "root-config"
    viewer_protocol_policy = "redirect-to-https"
  }
  ordered_cache_behavior {
    allowed_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    cache_policy_id             = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    response_headers_policy_id  = aws_cloudfront_response_headers_policy.paytrack_no_cache.id
    cached_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    compress               = true
    path_pattern           = "/js/*"
    smooth_streaming       = false
    target_origin_id       = "root-config"
    viewer_protocol_policy = "redirect-to-https"
  }
  ordered_cache_behavior {
    allowed_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    cache_policy_id             = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    response_headers_policy_id  = aws_cloudfront_response_headers_policy.paytrack_no_cache.id
    cached_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    compress               = true
    path_pattern           = "/assets/*"
    smooth_streaming       = false
    target_origin_id       = "root-config"
    viewer_protocol_policy = "allow-all"
  }
    ordered_cache_behavior {
    allowed_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    cache_policy_id             = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    response_headers_policy_id  = aws_cloudfront_response_headers_policy.paytrack.id
    cached_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    compress               = true
    path_pattern           = "/banco-bilhetes*"
    smooth_streaming       = false
    target_origin_id       = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    viewer_protocol_policy = "redirect-to-https"
  }
  ordered_cache_behavior {
    allowed_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    cache_policy_id             = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    response_headers_policy_id  = aws_cloudfront_response_headers_policy.paytrack.id
    cached_methods = [
      "GET",
      "HEAD",
    ]
    compress               = true
    path_pattern           = "/carteira-digital*"
    smooth_streaming       = false
    target_origin_id       = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    viewer_protocol_policy = "redirect-to-https"
  }
  ordered_cache_behavior {
    allowed_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    cache_policy_id             = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    response_headers_policy_id  = aws_cloudfront_response_headers_policy.paytrack.id
    cached_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    compress               = true
    path_pattern           = "/solicitacao-travel/*"
    smooth_streaming       = false
    target_origin_id       = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    viewer_protocol_policy = "redirect-to-https"
  }
  ordered_cache_behavior {
    allowed_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    cache_policy_id             = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    response_headers_policy_id  = aws_cloudfront_response_headers_policy.paytrack.id    
    cached_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    compress               = true
    path_pattern           = "/configuracoes/*"
    smooth_streaming       = false
    target_origin_id       = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    viewer_protocol_policy = "redirect-to-https"
  }
  ordered_cache_behavior {
    allowed_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    cache_policy_id             = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    response_headers_policy_id  = aws_cloudfront_response_headers_policy.paytrack.id
    cached_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    compress               = true
    path_pattern           = "/diretorio-hoteis*"
    smooth_streaming       = false
    target_origin_id       = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    viewer_protocol_policy = "redirect-to-https"
  }

  ordered_cache_behavior {
    allowed_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    cache_policy_id             = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    response_headers_policy_id  = aws_cloudfront_response_headers_policy.paytrack.id
    cached_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    compress               = true
    path_pattern           = "/dados-insights/*"
    smooth_streaming       = false
    target_origin_id       = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    viewer_protocol_policy = "redirect-to-https"
  }

  ordered_cache_behavior {
    allowed_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    cache_policy_id             = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    response_headers_policy_id  = aws_cloudfront_response_headers_policy.paytrack.id
    cached_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    compress               = true
    path_pattern           = "/dados-insights-despesas/*"
    smooth_streaming       = false
    target_origin_id       = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    viewer_protocol_policy = "redirect-to-https"
  }
    ordered_cache_behavior {
    allowed_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    cache_policy_id             = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    response_headers_policy_id  = aws_cloudfront_response_headers_policy.paytrack.id
    cached_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    compress               = true
    path_pattern           = "/solicitacao-expense/*"
    smooth_streaming       = false
    target_origin_id       = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    viewer_protocol_policy = "redirect-to-https"
  }
  ordered_cache_behavior {
    allowed_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    cache_policy_id             = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    response_headers_policy_id  = aws_cloudfront_response_headers_policy.paytrack.id
    cached_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    compress               = true
    path_pattern           = "/static_assets/*"
    smooth_streaming       = false
    target_origin_id       = "root-config"
    viewer_protocol_policy = "redirect-to-https"
  }
  ordered_cache_behavior {
    allowed_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    cached_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    compress               = true
    default_ttl            = 86400
    max_ttl                = 31536000
    path_pattern           = "/conciliacao/*"
    smooth_streaming       = false
    target_origin_id       = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    response_headers_policy_id  = aws_cloudfront_response_headers_policy.paytrack.id
    viewer_protocol_policy = "redirect-to-https"

    forwarded_values {
      headers = [
        "Access-Control-Request-Headers",
        "Access-Control-Request-Method",
        "Origin",
      ]
      query_string = false

      cookies {
        forward = "none"
      }
    }
  }

  ordered_cache_behavior {
    allowed_methods            = ["GET", "HEAD", "OPTIONS"]
    cache_policy_id            = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    cached_methods             = ["GET", "HEAD", "OPTIONS"]
    compress                   = "true"
    default_ttl                = "0"
    max_ttl                    = "0"
    min_ttl                    = "0"
    path_pattern               = "/gerenciamento-solicitacao/*"
    smooth_streaming           = false
    target_origin_id           = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    response_headers_policy_id = aws_cloudfront_response_headers_policy.paytrack.id
    viewer_protocol_policy     = "redirect-to-https"

  }

  origin {
    connection_attempts       = 3
    connection_timeout        = 10
    domain_name               = "${var.environment_name}-web.${var.domain}.paytrack.com.br"
    origin_id                 = "${var.environment_name}-web.${var.domain}.paytrack.com.br"

    custom_origin_config {
      http_port                = 80
      https_port               = 443
      origin_keepalive_timeout = 5
      origin_protocol_policy   = "https-only"
      origin_read_timeout      = 60
      origin_ssl_protocols = [
        "TLSv1.2",
      ]
    }
  }
  origin {
    connection_attempts       = 3
    connection_timeout        = 10
    domain_name               = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    origin_id                 = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    origin_path               = "/${var.environment_name}"
    origin_access_control_id  = aws_cloudfront_origin_access_control.default.id


  }
  origin {
    connection_attempts       = 3
    connection_timeout        = 10
    domain_name               = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    origin_id                 = "root-config"
    origin_path               = "/${var.environment_name}/root-config"
    origin_access_control_id  = aws_cloudfront_origin_access_control.default.id

  }

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  viewer_certificate {
    acm_certificate_arn            = "arn:aws:acm:us-east-1:560211763190:certificate/e5e1dbcf-c413-48ec-ac26-da169be693b8"
    cloudfront_default_certificate = false
    minimum_protocol_version       = "TLSv1.2_2021"
    ssl_support_method             = "sni-only"
  }
}

resource "aws_cloudfront_distribution" "paytrack_login" {
  aliases = [
    "login-${var.environment_name}.${var.domain}.paytrack.com.br",
  ]
  default_root_object = "index.html"
  enabled             = true
  http_version        = "http2"
  is_ipv6_enabled     = true
  price_class         = "PriceClass_All"
  retain_on_delete    = false

  wait_for_deployment = true

  custom_error_response {
    error_caching_min_ttl = 10
    error_code            = 403
    response_code         = 200
    response_page_path    = "/index.html"
  }
  custom_error_response {
    error_caching_min_ttl = 10
    error_code            = 404
    response_code         = 200
    response_page_path    = "/index.html"
  }

  default_cache_behavior {
    allowed_methods = [
      "DELETE",
      "GET",
      "HEAD",
      "OPTIONS",
      "PATCH",
      "POST",
      "PUT",
    ]
    cached_methods = [
      "GET",
      "HEAD",
    ]
    compress               = true
    default_ttl            = 86400
    max_ttl                = 31536000
    smooth_streaming       = false
    target_origin_id       = "S3-login.paytrack.com.br/${var.environment_name}"
    viewer_protocol_policy = "redirect-to-https"

    forwarded_values {
      query_string = false

      cookies {
        forward = "none"
      }
    }
  }

  origin {
    connection_attempts       = 3
    connection_timeout        = 10
    domain_name               = "login.paytrack.com.br.s3.amazonaws.com"
    origin_id                 = "S3-login.paytrack.com.br/${var.environment_name}"
    origin_path               = "/${var.environment_name}"
    origin_access_control_id  = aws_cloudfront_origin_access_control.default.id
  }

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  viewer_certificate {
    acm_certificate_arn            = "arn:aws:acm:us-east-1:560211763190:certificate/e5e1dbcf-c413-48ec-ac26-da169be693b8"
    cloudfront_default_certificate = false
    minimum_protocol_version       = "TLSv1.2_2019"
    ssl_support_method             = "sni-only"
  }

  
}

resource "aws_cloudfront_distribution" "supplier" {
  aliases = [
    "supplier-${var.environment_name}.${var.domain}.paytrack.com.br",
  ]
  default_root_object = "index.html"
  enabled             = true
  http_version        = "http2"
  is_ipv6_enabled     = true
  price_class         = "PriceClass_All"
  retain_on_delete    = false

  wait_for_deployment = true
  
  default_cache_behavior {
    allowed_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    cache_policy_id             = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    response_headers_policy_id  = aws_cloudfront_response_headers_policy.paytrack.id
    cached_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    compress               = true
    smooth_streaming       = false
    target_origin_id       = "agencias.paytrack.com.br.s3.us-east-1.amazonaws.com"
    viewer_protocol_policy = "redirect-to-https"
  }

  origin {
    connection_attempts       = 3
    connection_timeout        = 10
    domain_name               = "agencias.paytrack.com.br.s3.us-east-1.amazonaws.com"
    origin_id                 = "agencias.paytrack.com.br.s3.us-east-1.amazonaws.com"
    origin_path               = "/${var.environment_name}"
    origin_access_control_id  = aws_cloudfront_origin_access_control.default.id
  }

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  viewer_certificate {
    acm_certificate_arn            = "arn:aws:acm:us-east-1:560211763190:certificate/e5e1dbcf-c413-48ec-ac26-da169be693b8"
    cloudfront_default_certificate = false
    minimum_protocol_version       = "TLSv1.2_2021"
    ssl_support_method             = "sni-only"
  }
}


resource "aws_cloudfront_origin_access_control" "default" {
  name                              = "${var.environment_name}"
  description                       = "s3 Policy"
  origin_access_control_origin_type = "s3"
  signing_behavior                  = "always"
  signing_protocol                  = "sigv4"
}