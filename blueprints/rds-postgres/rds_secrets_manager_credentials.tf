#RDS Admin credential
resource "random_password" "db_admin_password" {
  length  = 20
  special = false
}

resource "aws_secretsmanager_secret" "db_admin_password" {
  name = "db-admin-${var.environment_name}-${var.application_name}"
}

resource "aws_secretsmanager_secret_version" "db_admin_password_version" {
  secret_id     = aws_secretsmanager_secret.db_admin_password.id
  secret_string = jsonencode({
    username = "kodawari" 
    password = random_password.db_admin_password.result
  })
}

#App credential
resource "random_password" "app_database_password" {
  length  = 20
  special = false
}

resource "aws_secretsmanager_secret" "app_database_secret" {
  name = "${var.environment_name}-${var.application_name}"
}

resource "aws_secretsmanager_secret_version" "app_database_secret_version" {
  secret_id     = aws_secretsmanager_secret.app_database_secret.id
  secret_string = jsonencode({
    username = var.application_name,
    password = random_password.app_database_password.result
  })
}

#Grafana
resource "random_password" "grafana_database_password" {
  length  = 20
  special = false
}

resource "aws_secretsmanager_secret" "grafana_database_secret" {
  name = "${var.environment_name}-${var.application_name}-grafana"
}

resource "aws_secretsmanager_secret_version" "grafana_database_secret_version" {
  secret_id     = aws_secretsmanager_secret.grafana_database_secret.id
  secret_string = jsonencode({
    username = "grafana",
    password = random_password.grafana_database_password.result
  })
}

#Paystore
resource "random_password" "paystore_database_password" {
  length  = 20
  special = false
}

resource "aws_secretsmanager_secret" "paystore_database_secret" {
  name = "${var.environment_name}-${var.application_name}-paystore"
}

resource "aws_secretsmanager_secret_version" "paystore_database_secret_version" {
  secret_id     = aws_secretsmanager_secret.paystore_database_secret.id
  secret_string = jsonencode({
    username = "paystore",
    password = random_password.paystore_database_password.result
  })
}