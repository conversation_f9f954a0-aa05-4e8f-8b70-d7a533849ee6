resource "aws_db_parameter_group" "postgresql16" {
  name        = "${var.environment_name}-${var.application_name}-pg16-params"
  family      = "postgres16"
  description = "Custom PostgreSQL 16 parameter group for ${var.environment_name}"

  parameter {
    name  = "log_min_duration_statement"
    value = "5000"
    apply_method = "pending-reboot"
  }

  parameter {
    name  = "log_connections"
    value = "1"
    apply_method = "immediate"
  }

  parameter {
    name  = "log_disconnections"
    value = "1"
    apply_method = "immediate"
  }

  parameter {
    name  = "idle_in_transaction_session_timeout"
    value = "60000"
    apply_method = "pending-reboot"
  }

  parameter {
    name         = "rds.force_ssl"
    value        = "0"
    apply_method = "pending-reboot"
  }

  tags = {
    Environment   = var.environment_name
    Name          = var.application_name
  }
}
