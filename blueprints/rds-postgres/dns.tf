resource "aws_route53_record" "rds_staging" {
  count           = var.dns_staging ? 1 : 0
  zone_id         = "Z07065491X07GH8XR89CQ"
  name            = "db-${var.application_name}.staging.paytrack.com.br"
  type            = "CNAME"
  ttl             = 300
  records         = [ aws_db_instance.app.address ]
}



resource "aws_route53_record" "rds_developer" {
  count           = var.dns_developer ? 1 : 0
  zone_id         = "Z08060781DD0FET8KXURP"
  name            = "db-${var.application_name}.developer.paytrack.com.br"
  type            = "CNAME"
  ttl             = 300
  records         = [ aws_db_instance.app.address ]
}

resource "aws_route53_record" "rds_prod" {
  count           = var.dns_prod ? 1 : 0
  zone_id         = "ZOQML0WI6K6IU"
  name            = "db-${var.application_name}.paytrack.com.br"
  type            = "CNAME"
  ttl             = 300
  records         = [ aws_db_instance.app.address ]
}
