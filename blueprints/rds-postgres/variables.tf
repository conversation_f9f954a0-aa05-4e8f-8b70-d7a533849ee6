variable "environment_name" {
  description = "Nome do ambiente: developer, staging, prod"
  type        = string
}

variable "application_name" {
  description = "Nome do serviço/aplicação"
  type        = string
}


variable "profile" {
  type = string
  description = "Nome do profile onde os recursos serão criados"
}

variable "region" {
  type = string
  description = "Região onde recursos serão criados"
}

variable "allowed_cidr_blocks" {
  description = "Lista de CIDRs adicionais permitidos para a porta 3306"
  type        = list(string)
  default     = []
}

variable "subnet_group" {
  description = "Subnet group para criar a instancia de banco"
  type = string  
}

variable "vpc_id" {
  description = "Subnet group para criar a instancia de banco"
  type = string  
}

variable "instance_size" {
  description = "Instance size and hardware type"
  type = string  
}

variable "pg_version" {
  description = "aws rds describe-db-engine-versions --engine postgres --query 'DBEngineVersions[].EngineVersion' --output text"
  type = string  
}


variable "dns_staging" {
  description = "Create Route 53 records for staging subdomain"
  type        = bool
  default     = false
}

variable "dns_developer" {
  description = "Create Route 53 records for developer subdomain"
  type        = bool
  default     = false
}

variable "dns_prod" {
  description = "Create Route 53 records for paytrack main domain"
  type        = bool
  default     = false
}

variable "security_group_name" {
  description = "Needs to be overriden if VPC other than eks (vpc-06c7423cc4349d603)"
  type        = string
  default     = "app-rds-security-group"
}

variable "create_rds_instance" {
  description = "Cria ou não RDS. Se não for criado, executa somente os Grants"
  type        = bool
  default     = true
}

variable "db_instance_addr" {
  description = "Endereço do RDS, se não for criado na execução. Obrigatório quando create_rds_instance = false"
  type        = string
  default     = null
  nullable    = true

  validation {
    condition = var.create_rds_instance || var.db_instance_addr != null
    error_message = "db_instance_addr é obrigatório quando create_rds_instance = false."
  }
}

variable "master_credential_password" {
  description = "Senha do usuário master do RDS, se não for criado na execução. Obrigatório quando create_rds_instance = false"
  type        = string
  default     = null
  nullable    = true
  sensitive   = true

  validation {
    condition = var.create_rds_instance || var.master_credential_password != null
    error_message = "master_credential_password é obrigatório quando create_rds_instance = false."
  }
}

