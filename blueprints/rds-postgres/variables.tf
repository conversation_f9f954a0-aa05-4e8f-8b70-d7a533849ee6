variable "environment_name" {
  description = "Nome do ambiente: developer, staging, prod"
  type        = list(string)
}

variable "application_name" {
  description = "Nome do serviço/aplicação"
  type        = string
}


variable "profile" {
  type = string
  description = "Nome do profile onde os recursos serão criados"
}

variable "region" {
  type = string
  description = "Região onde recursos serão criados"
}

variable "allowed_cidr_blocks" {
  description = "Lista de CIDRs adicionais permitidos para a porta 3306"
  type        = list(string)
  default     = []
}

variable "subnet_group" {
  description = "Subnet group para criar a instancia de banco"
  type = string  
}

variable "vpc_id" {
  description = "Subnet group para criar a instancia de banco"
  type = string  
}

variable "instance_size" {
  description = "Instance size and hardware type"
  type = string  
}

variable "pg_version" {
  description = "aws rds describe-db-engine-versions --engine postgres --query 'DBEngineVersions[].EngineVersion' --output text"
  type = string  
}


variable "dns_staging" {
  description = "Create Route 53 records for staging subdomain"
  type        = bool
  default     = false
}

variable "dns_developer" {
  description = "Create Route 53 records for developer subdomain"
  type        = bool
  default     = false
}

variable "dns_prod" {
  description = "Create Route 53 records for paytrack main domain"
  type        = bool
  default     = false
}

variable "security_group_name" {
  description = "Needs to be overriden if VPC other than eks (vpc-06c7423cc4349d603)"
  type        = string
  default     = "app-rds-security-group"
}