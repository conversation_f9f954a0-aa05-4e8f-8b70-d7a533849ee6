########################
##                    ##
##        app        ##
##                    ##
########################

#DATABASE
resource "postgresql_database" "app" {
  provider                = postgresql.admin
  for_each                = var.environment_name
  name                    = "${each.value}-${var.application_name}"
  owner                   = postgresql_role.app.name
  template                = "template0"
  lc_collate              = "pt_BR.UTF-8"
  encoding                = "UTF8"
  connection_limit        = -1
  allow_connections       = true
  alter_object_ownership  = true

  depends_on = [ aws_secretsmanager_secret.app_database_secret ]
}

#SCHEMA
resource "postgresql_schema" "app" {
  provider        = postgresql.admin
  for_each        = var.environment_name
  name            = "${each.value}-${var.application_name}"
  owner           = postgresql_role.app.name
  if_not_exists   = true
  database        = postgresql_database.app.name
}

#APP USER
resource "postgresql_role" "app" {
  provider          = postgresql.admin
  name              = "${var.application_name}"
  login             = true
  password          = jsondecode(aws_secretsmanager_secret_version.app_database_secret_version.secret_string).password
  roles             = [postgresql_role.rw.name]
}

#Grafana USER
resource "postgresql_role" "grafana" {
  provider          = postgresql.admin
  name              = "grafana"
  login             = true
  password          = jsondecode(aws_secretsmanager_secret_version.grafana_database_secret_version.secret_string).password
  roles             = [postgresql_role.ro.name]
}


#Paystore USER
resource "postgresql_role" "paystore" {
  provider          = postgresql.admin
  name              = "paystore"
  login             = true
  password          = jsondecode(aws_secretsmanager_secret_version.paystore_database_secret_version.secret_string).password
  roles             = [postgresql_role.ro.name]
}


#Grupos/roles ro e rw
resource "postgresql_role" "ro" {
  provider          = postgresql.admin
  for_each          = toset([var.environment_name])
  name              = "${each.value}-${var.application_name}-ro"
  login             = false
}

resource "postgresql_role" "rw" {
  provider          = postgresql.admin
  for_each          = toset([var.environment_name])
  name              = "${each.value}-${var.application_name}-rw"
  login             = false
}

#Grant Permissions by role

#APP RW
resource "postgresql_grant_role" "app" {
  provider          = postgresql.admin
  role              = postgresql_role.app.name
  grant_role        = postgresql_role.rw.name
  with_admin_option = false
}

#Grafana RO
resource "postgresql_grant_role" "grafana" {
  provider          = postgresql.admin
  role              = postgresql_role.grafana.name
  grant_role        = postgresql_role.ro.name
  with_admin_option = false
}

#Paystore RO
resource "postgresql_grant_role" "paystore" {
  provider          = postgresql.admin
  role              = postgresql_role.paystore.name
  grant_role        = postgresql_role.ro.name
  with_admin_option = false
}
