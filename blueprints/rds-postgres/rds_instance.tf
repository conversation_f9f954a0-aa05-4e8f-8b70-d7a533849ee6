resource "aws_db_instance" "app" {
  allocated_storage             = 100
  identifier                    = var.application_name
  engine                        = "postgres"
  engine_version                = var.pg_version
  instance_class                = var.instance_size
  username                      = "kodawari"
  password                      = jsondecode(aws_secretsmanager_secret_version.db_admin_password_version.secret_string).password
  parameter_group_name          = aws_db_parameter_group.postgresql17.name
  skip_final_snapshot           = true
  vpc_security_group_ids        = [ aws_security_group.app_rds.id ]
  db_subnet_group_name          = var.subnet_group
  publicly_accessible           = false
  apply_immediately             = true
  #Monitoring
  performance_insights_enabled            = true
  performance_insights_retention_period   = 7
  #Atualizações
  auto_minor_version_upgrade    = true
  maintenance_window            = "Mon:21:31-Mon:22:59"
  allow_major_version_upgrade   = true
  #Backup
  backup_window                 = "21:00-21:30"
  backup_retention_period       = 7
  copy_tags_to_snapshot         = true
  #HA/DR
  multi_az                      = false
  storage_encrypted             = true
  


  tags = {
    schedule    = var.dns_prod ? "No-Auto-Shutdown" : "Auto-Shutdown"
    environment = var.environment_name[0]
    Name        =  var.application_name
  }

  depends_on = [ aws_secretsmanager_secret_version.db_admin_password_version ]
}

# SG RDS
resource "aws_security_group" "app_rds" {
  name   = var.security_group_name
  vpc_id = var.vpc_id

  ingress {
    from_port   = 5432
    to_port     = 5432
    protocol    = "tcp"
    cidr_blocks = var.allowed_cidr_blocks
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = var.allowed_cidr_blocks
  }

  tags = {
    Name = "rds-sg"
  }
}

