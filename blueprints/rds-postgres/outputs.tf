output "master_password" {
  value = jsondecode(aws_secretsmanager_secret_version.db_admin_password_version.secret_string).password
}

output "rds_endpoint" {
  value = aws_db_instance.app.address
}

output "db_password_secret_arn" {
  value = aws_secretsmanager_secret.app_database_secret.arn
}
output "app_password" {
  value = jsondecode(aws_secretsmanager_secret_version.app_database_secret_version.secret_string).password
  sensitive = true
}

output "app_user" {
  value = jsondecode(aws_secretsmanager_secret_version.app_database_secret_version.secret_string).username
  sensitive = true
}
