# Compute actual values based on whether RDS is being created or not
locals {
  # Use provided address if RDS is not being created, otherwise use the created instance address
  actual_db_instance_addr = var.create_rds_instance ? aws_db_instance.app.address : var.db_instance_addr

  # Use provided password if RDS is not being created, otherwise use the generated password
  actual_master_password = var.create_rds_instance ? jsondecode(aws_secretsmanager_secret_version.db_admin_password_version.secret_string).password : var.master_credential_password
}

provider "aws" {
  default_tags {
    tags = {
      Name          =   "app"
      Ambiente      =   var.environment_name
      Managed-by    =   "Terraform"
    }
  }

  region  = var.region
  profile = var.profile

}

provider "postgresql" {
  alias           = "admin"
  host            = local.actual_db_instance_addr
  port            = 5432
  database        = "postgres"
  username        = "kodawari"
  password        = local.actual_master_password
  sslmode         = "disable"
  connect_timeout = 5
  superuser       = false
}

provider "postgresql" {
  alias           = "app"
  host            = local.actual_db_instance_addr
  port            = 5432
  database        = var.create_rds_instance ? aws_db_instance.app[0].db_name : "${var.environment_name}-${var.application_name}"
  username        = var.application_name
  password        = jsondecode(aws_secretsmanager_secret_version.app_database_secret_version.secret_string).password
  sslmode         = "disable"
  connect_timeout = 5
}

terraform {
  required_providers {
    aws = {
      source = "hashicorp/aws"
    }
    postgresql = {
      source = "cyrilgdn/postgresql"
      version = "1.25.0"
    }
  }
}
