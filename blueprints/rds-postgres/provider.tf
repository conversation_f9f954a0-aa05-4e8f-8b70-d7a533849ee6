provider "aws" {
  default_tags {
    tags = {
      Name          =   "app"
      Ambiente      =   var.environment_name
      Managed-by    =   "Terraform"
    }
  }

  region  = var.region
  profile = var.profile

}

provider "postgresql" {
  alias           = "admin" 
  host            = var.db_instance_addr
  port            = 5432
  database        = "postgres"
  username        = "k<PERSON><PERSON>"
  password        = var.master_credential_password
  sslmode         = "disable"
  connect_timeout = 5
  superuser       = false
}

provider "postgresql" {
  alias           = "app" 
  host            = var.db_instance_addr
  port            = 5432
  database        = aws_db_instance.app.name
  username        = var.application_name
  password        = jsondecode(aws_secretsmanager_secret_version.app_database_secret_version.secret_string).password
  sslmode         = "disable"
  connect_timeout = 5
}

terraform {
  required_providers {
    aws = {
      source = "hashicorp/aws"
    }
    postgresql = {
      source = "cyrilgdn/postgresql"
      version = "1.25.0"
    }
  }
}
