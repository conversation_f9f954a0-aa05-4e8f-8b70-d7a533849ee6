provider "aws" {
  default_tags {
    tags = {
      Name          =   "app"
      Ambiente      =   var.environment_name[0]
      Managed-by    =   "Terraform"
    }
  }

  region  = var.region
  profile = var.profile

}

provider "postgresql" {
  alias           = "admin" 
  host            = "${aws_db_instance.app.address}"
  port            = 5432
  database        = "postgres"
  username        = "k<PERSON><PERSON>"
  password        = jsondecode(aws_secretsmanager_secret_version.db_admin_password_version.secret_string).password
  sslmode         = "disable"
  connect_timeout = 5
  superuser       = false
}

provider "postgresql" {
  alias           = "app" 
  host            = "${aws_db_instance.app.address}"
  port            = 5432
  database        = aws_db_instance.app.name
  username        = var.application_name
  password        = jsondecode(aws_secretsmanager_secret_version.app_database_secret_version.secret_string).password
  sslmode         = "disable"
  connect_timeout = 5
}

terraform {
  required_providers {
    aws = {
      source = "hashicorp/aws"
    }
    postgresql = {
      source = "cyrilgdn/postgresql"
      version = "1.25.0"
    }
  }
}
