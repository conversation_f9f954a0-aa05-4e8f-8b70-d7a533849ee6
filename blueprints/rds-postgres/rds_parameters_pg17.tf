resource "aws_db_parameter_group" "postgresql17" {
  name        = "${var.environment_name[0]}-${var.application_name}-pg17-params"
  family      = "postgres17"
  description = "Custom PostgreSQL 17 parameter group for ${var.environment_name[0]}"

  parameter {
    name  = "log_min_duration_statement"
    value = "5000"
    apply_method = "pending-reboot"
  }

  parameter {
    name  = "log_connections"
    value = "1"
    apply_method = "immediate"
  }

  parameter {
    name  = "log_disconnections"
    value = "1"
    apply_method = "immediate"
  }

  parameter {
    name  = "idle_in_transaction_session_timeout"
    value = "60000"
    apply_method = "pending-reboot"
  }

  parameter {
    name         = "rds.force_ssl"
    value        = "0"
    apply_method = "pending-reboot"
  }

  tags = {
    Environment   = var.environment_name[0]
    Name          = var.application_name
  }
}
