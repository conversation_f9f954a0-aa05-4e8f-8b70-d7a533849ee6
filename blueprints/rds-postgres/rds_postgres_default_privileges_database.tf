#Default Privileges database/role RW
resource "postgresql_default_privileges" "db_tables_rw" {
  provider          = postgresql.admin
  role              = postgresql_role.rw.name
  database          = postgresql_database.app.name

  owner             = postgresql_role.app.name
  object_type       = "table"
  privileges        = [
          "DELETE",
          "INSERT",
          "REFERENCES",
          "SELECT",
          "TRIGGER",
          "TRUNCATE",
          "UPDATE" 
          ]
}

resource "postgresql_default_privileges" "db_sequences_rw" {
  provider          = postgresql.admin
  role              = postgresql_role.rw.name
  database          = postgresql_database.app.name

  owner             = postgresql_role.app.name
  object_type       = "sequence"
  privileges        = ["ALL"]
}

resource "postgresql_default_privileges" "db_functions_rw" {
  provider          = postgresql.admin
  role              = postgresql_role.rw.name
  database          = postgresql_database.app.name

  owner             = postgresql_role.app.name
  object_type       = "function"
  privileges        = ["ALL"]
}

resource "postgresql_default_privileges" "db_type_rw" {
  provider          = postgresql.admin
  role              = postgresql_role.rw.name
  database          = postgresql_database.app.name

  owner             = postgresql_role.app.name
  object_type       = "type"
  privileges        = ["ALL"]
}

resource "postgresql_default_privileges" "db_chemas_rw" {
  provider          = postgresql.admin
  role              = postgresql_role.rw.name
  database          = postgresql_database.app.name

  owner             = postgresql_role.app.name
  object_type       = "schema"
  privileges        = ["ALL"]
}

#Default Privileges database/role RO
resource "postgresql_default_privileges" "db_tables_ro" {
  provider          = postgresql.admin
  role              = postgresql_role.ro.name
  database          = postgresql_database.app.name
  schema            = postgresql_schema.app.name

  owner             = postgresql_role.app.name
  object_type       = "table"
  privileges        = ["SELECT"]
}

resource "postgresql_default_privileges" "db_sequences_ro" {
  provider          = postgresql.admin
  role              = postgresql_role.ro.name
  database          = postgresql_database.app.name

  owner             = postgresql_role.app.name
  object_type       = "sequence"
  privileges        = ["USAGE"]
}


resource "postgresql_default_privileges" "db_type_ro" {
  provider          = postgresql.admin
  role              = postgresql_role.ro.name
  database          = postgresql_database.app.name

  owner             = postgresql_role.app.name
  object_type       = "type"
  privileges        = ["USAGE"]
}

resource "postgresql_default_privileges" "db_schemas_ro" {
  provider          = postgresql.admin
  role              = postgresql_role.ro.name
  database          = postgresql_database.app.name

  owner             = postgresql_role.app.name
  object_type       = "schema"
  privileges        = ["USAGE"]
}
