################################################
##    Rotas from HML account to datadev account
################################################
resource "aws_route" "routes-ecs-developer-to-datadev" {
  provider                      = aws.hml
  route_table_id                = each.value
  for_each                      = var.rtbs-ecs-developer
  destination_cidr_block        = var.destinations-datalake-dev-vpc
  transit_gateway_id            = aws_ec2_transit_gateway.viajor-tgw.id

}

resource "aws_route" "routes-ecs-staging-to-datadev" {
  provider                      = aws.hml
  route_table_id                = each.value
  for_each                      = var.rtbs-ecs-staging
  destination_cidr_block        = var.destinations-datalake-dev-vpc
  transit_gateway_id            = aws_ec2_transit_gateway.viajor-tgw.id

}

resource "aws_route" "routes-eks-developer-to-datadev" {
  provider                      = aws.hml
  route_table_id                = aws_route_table.eks-dev-10-20.id
  destination_cidr_block        = var.destinations-datalake-dev-vpc
  transit_gateway_id            = aws_ec2_transit_gateway.viajor-tgw.id

}
