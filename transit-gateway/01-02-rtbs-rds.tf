######################################
##                                  ##
##          RDS ROUTE TABLE         ##
##                                  ## 
######################################
resource "aws_route_table" "rds" {
    provider    = aws.prod
    vpc_id      = aws_vpc.eks-prod-vpc.id

    tags             = {
            managed-by                                  = "terraform",
            env                                         = "prod",
            Name                                        = "rds"
    }

}

resource "aws_route_table_association" "rds-us-east-1a" {
    provider       = aws.prod
    subnet_id      = aws_subnet.rds-us-east-1a.id
    route_table_id = aws_route_table.rds.id
}

resource "aws_route_table_association" "rds-us-east-1b" {
    provider       = aws.prod
    subnet_id      = aws_subnet.rds-us-east-1b.id
    route_table_id = aws_route_table.rds.id
}

resource "aws_route_table_association" "rds-us-east-1c" {
    provider       = aws.prod
    subnet_id      = aws_subnet.rds-us-east-1c.id
    route_table_id = aws_route_table.rds.id
}

