##############################
##                          ##
##  SUBNETS PRIVADAS - RDS  ##
##                          ##
##############################
resource "aws_subnet" "rds-us-east-1a" {
  vpc_id                                         = aws_vpc.eks-prod-vpc.id
  cidr_block                                     = "***********/24"
  availability_zone                              = "us-east-1a"
  map_public_ip_on_launch                        = false

  tags = {
    env                     = "prod",
    managed-by              = "terraform"
    Name                    = "rds"
  }

}

resource "aws_subnet" "rds-us-east-1b" {
  vpc_id                                         = aws_vpc.eks-prod-vpc.id
  cidr_block                                     = "***********/24"
  availability_zone                              = "us-east-1b"
  map_public_ip_on_launch                        = false

  tags = {
    env                     = "prod",
    managed-by              = "terraform"
    Name                    = "rds"
  }

}

resource "aws_subnet" "rds-us-east-1c" {
  vpc_id                                         = aws_vpc.eks-prod-vpc.id
  cidr_block                                     = "***********/24"
  availability_zone                              = "us-east-1c"
  map_public_ip_on_launch                        = false

  tags = {
    env                     = "prod",
    managed-by              = "terraform"
    Name                    = "rds"
  }

}
