#transit gateway vpn destinations route
resource "aws_ec2_transit_gateway_route" "vpn-eksdev" {
  provider                        = aws.prod
  for_each                        = var.destinations-vpn
  destination_cidr_block          = each.value
  transit_gateway_attachment_id   = aws_ec2_transit_gateway_vpc_attachment.viajor-vpc-attach.id
  transit_gateway_route_table_id  = aws_ec2_transit_gateway.viajor-tgw.association_default_route_table_id
}



#route transit gateway traffic destinated to VPN for private nat
resource "aws_route" "vpn-routes" {
  provider                  = aws.prod
  route_table_id            = aws_route_table.tgw.id
  for_each                  = var.destinations-vpn
  destination_cidr_block    = each.value
  nat_gateway_id            = aws_nat_gateway.viajor-private-ngw.id
 
  depends_on = [
    aws_ec2_transit_gateway.viajor-tgw,
    aws_nat_gateway.viajor-private-ngw,
    aws_route_table.tgw,
    aws_subnet.transit-gateway
  ]
}

resource "aws_route" "from-tgw-subnet-to-eks-prod-subnets" {
  provider                  = aws.prod
  route_table_id            = aws_route_table.tgw.id
  for_each                  = var.destinations-eks-prod-tools-vpc
  destination_cidr_block    = each.value
  transit_gateway_id        = aws_ec2_transit_gateway.viajor-tgw.id
 
  depends_on = [
    aws_ec2_transit_gateway.viajor-tgw,
    aws_nat_gateway.viajor-private-ngw,
    aws_route_table.tgw,
    aws_subnet.transit-gateway
  ]
}


resource "aws_route" "from-tgw-subnet-to-eks-dev-subnets" {
  provider                  = aws.prod
  route_table_id            = aws_route_table.tgw.id
  for_each                  = var.destinations-eks-dev-vpc
  destination_cidr_block    = each.value
  transit_gateway_id        = aws_ec2_transit_gateway.viajor-tgw.id
 
  depends_on = [
    aws_ec2_transit_gateway.viajor-tgw,
    aws_nat_gateway.viajor-private-ngw,
    aws_route_table.tgw,
    aws_subnet.transit-gateway
  ]
}

#Route every other traffic trough 34.233.189.84
resource "aws_route" "nat-saida" {
  provider                    = aws.prod
  route_table_id              = aws_route_table.tgw.id
  nat_gateway_id              = "nat-02ffb009ffc85a638"
  destination_cidr_block      = "0.0.0.0/0"
  
  depends_on = [
    aws_ec2_transit_gateway.viajor-tgw,
    aws_nat_gateway.viajor-private-ngw
  ]
}


#eks prod - new vpc/cidr 172.20.0.0/18
resource "aws_route" "prod-tools-vpn-routes" {
  provider                  = aws.prod
  route_table_id            = aws_route_table.eks-prod-tools-172-20.id
  for_each                  = var.destinations-vpn
  destination_cidr_block    = each.value
  transit_gateway_id        = aws_ec2_transit_gateway.viajor-tgw.id

  depends_on = [
    aws_ec2_transit_gateway.viajor-tgw,
    aws_nat_gateway.viajor-private-ngw,
  ]
}


#transit gateway openvpn
resource "aws_ec2_transit_gateway_route" "openvpn-viajor" {
  provider                        = aws.prod
  for_each                        = var.destinations-viajor-vpc
  destination_cidr_block          = each.value
  transit_gateway_attachment_id   = aws_ec2_transit_gateway_vpc_attachment.viajor-vpc-attach.id
  transit_gateway_route_table_id  = aws_ec2_transit_gateway.viajor-tgw.association_default_route_table_id
}
