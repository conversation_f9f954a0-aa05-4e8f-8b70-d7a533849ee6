resource "aws_ram_resource_share" "tgw-hml" {
  provider                  = aws.prod
  name                      = "tgw-hml"
  allow_external_principals = "false"

  tags = {
    env         = "prod",
    managed-by  = "terraform"
  }
}

resource "aws_ram_resource_association" "tgw-hml" {
  provider           = aws.prod
  resource_arn       = aws_ec2_transit_gateway.viajor-tgw.arn
  resource_share_arn = aws_ram_resource_share.tgw-hml.id

}

resource "aws_ram_principal_association" "tgw-hml" {
  provider           = aws.prod
  principal          = "560211763190"
  resource_share_arn = aws_ram_resource_share.tgw-hml.id

  depends_on = [
    aws_ec2_transit_gateway.viajor-tgw
  ]
}

##############
#datalake dev#
##############
resource "aws_ram_resource_share" "tgw-datadev" {
  provider                  = aws.prod
  name                      = "tgw-datadev"
  allow_external_principals = "false"

  tags = {
    env         = "prod",
    managed-by  = "terraform"
  }
}


resource "aws_ram_resource_association" "tgw-datadev" {
  provider           = aws.prod
  resource_arn       = aws_ec2_transit_gateway.viajor-tgw.arn
  resource_share_arn = aws_ram_resource_share.tgw-datadev.id

}


resource "aws_ram_principal_association" "tgw-datadev" {
  provider           = aws.prod
  principal          = "122610517084"
  resource_share_arn = aws_ram_resource_share.tgw-datadev.id

}

