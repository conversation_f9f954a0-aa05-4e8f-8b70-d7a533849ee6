#transit gateway ecs staging
resource "aws_ec2_transit_gateway_route" "ecs-staging" {
  provider                        = aws.prod
  for_each                        = var.destinations-ecs-hml-vpc
  destination_cidr_block          = each.value
  transit_gateway_attachment_id   = aws_ec2_transit_gateway_vpc_attachment.ecs-staging-subnets-attach.id
  transit_gateway_route_table_id  = aws_ec2_transit_gateway.viajor-tgw.association_default_route_table_id
}

#transit gateway ecs staging
resource "aws_ec2_transit_gateway_route" "ecs-developer" {
  provider                        = aws.prod
  for_each                        = var.destinations-ecs-dev-vpc
  destination_cidr_block          = each.value
  transit_gateway_attachment_id   = aws_ec2_transit_gateway_vpc_attachment.ecs-developer-subnets-attach.id
  transit_gateway_route_table_id  = aws_ec2_transit_gateway.viajor-tgw.association_default_route_table_id
}