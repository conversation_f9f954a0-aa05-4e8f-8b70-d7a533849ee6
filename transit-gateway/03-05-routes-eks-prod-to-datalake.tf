###############################################
##    Rotas EKS Produção -> Datalake         ##
###############################################
resource "aws_route" "eks-to-datalake" {
  provider                      = aws.prod
  route_table_id                = aws_route_table.eks-prod-tools-172-20.id
  for_each                      = var.destinations-datalake-vpc
  destination_cidr_block        = each.value
  transit_gateway_id            = aws_ec2_transit_gateway.viajor-tgw.id

  depends_on = [
    aws_route_table.eks-prod-tools-172-20
  ]
}
