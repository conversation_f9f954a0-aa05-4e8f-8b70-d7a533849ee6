####################
###################
##################
## Needs ONE attachment by zone
##################
###################
####################


#transit gateway subnet attach viajor vpc, somente vpc do nat gateway para controlar roteamento
resource "aws_ec2_transit_gateway_vpc_attachment" "viajor-vpc-attach" {
  provider                        = aws.prod
  subnet_ids                      = [ 
                                      aws_subnet.transit-gateway.id,
                                      aws_subnet.nat-gateway-b.id,
                                      aws_subnet.nat-gateway-c.id,
                                      aws_subnet.nat-gateway-d.id,
                                      aws_subnet.nat-gateway-e.id,
                                      aws_subnet.nat-gateway-f.id
                                     ]
  transit_gateway_id              = aws_ec2_transit_gateway.viajor-tgw.id
  vpc_id                          = aws_vpc.viajor-vpc.id
  depends_on = [
    aws_ec2_transit_gateway.viajor-tgw,
    aws_subnet.transit-gateway,

  ]

  tags = {
    Name = "viajor-vpc"
  }
  
}




#############################
##                         ##
##   NOVA VPC EKS PROD     ##
##                         ##
#############################
resource "aws_ec2_transit_gateway_vpc_attachment" "eks-prod-subnets-attach" {
  provider                        = aws.prod
  subnet_ids                      = [ 
                                      aws_subnet.private-0-us-east-a.id,
                                      aws_subnet.private-4-us-east-b.id,
                                      aws_subnet.private-8-us-east-c.id
                                    ]
  transit_gateway_id              = aws_ec2_transit_gateway.viajor-tgw.id
  vpc_id                          = aws_vpc.eks-prod-vpc.id
  depends_on = [
    aws_ec2_transit_gateway.viajor-tgw
  ]

  tags = {
    Name = "eks-prod"
  }

}


#############################
##                         ##
##   NOVA VPC EKS DEV     ##
##                         ##
#############################
resource "aws_ec2_transit_gateway_vpc_attachment" "eks-developer-subnets-attach" {
  provider                        = aws.hml
  subnet_ids                      = [ 
                                      aws_subnet.dev-private0-us-east-a.id,
                                      aws_subnet.dev-private4-us-east-b.id,
                                      aws_subnet.dev-private8-us-east-c.id
                                    ]
  transit_gateway_id              = aws_ec2_transit_gateway.viajor-tgw.id
  vpc_id                          = aws_vpc.eks-developer-vpc.id
  depends_on = [
    aws_ec2_transit_gateway.viajor-tgw
  ]

  tags = {
    Name = "eks-developer"
  }

}


#############################
##                         ##
##   ECS    DEVELOPER      ##
##                         ##
#############################
resource "aws_ec2_transit_gateway_vpc_attachment" "ecs-developer-subnets-attach" {
  provider                        = aws.hml
  subnet_ids                      = [ 
                                      aws_subnet.developer-us-east-1a-1.id,
                                      aws_subnet.developer-us-east-1b-1.id
                                    ]
  transit_gateway_id              = aws_ec2_transit_gateway.viajor-tgw.id
  vpc_id                          = aws_vpc.ecs-developer.id
  depends_on = [
    aws_ec2_transit_gateway.viajor-tgw
  ]

  tags = {
    Name = "ecs-developer"
  }

}

#############################
##                         ##
##   ECS    STAGING        ##
##                         ##
#############################
resource "aws_ec2_transit_gateway_vpc_attachment" "ecs-staging-subnets-attach" {
  provider                        = aws.hml
  subnet_ids                      = [ 
                                      aws_subnet.staging-us-east-1a-1.id,
                                      aws_subnet.staging-us-east-1b-1.id
                                    ]
  transit_gateway_id              = aws_ec2_transit_gateway.viajor-tgw.id
  vpc_id                          = aws_vpc.ecs-staging.id
  depends_on = [
    aws_ec2_transit_gateway.viajor-tgw
  ]

  tags = {
    Name = "ecs-staging"
  }

}

#############################
##                         ##
##     SYNC HOMOLOG        ##
##                         ##
#############################
resource "aws_ec2_transit_gateway_vpc_attachment" "sync_hml_vpc_attachment" {
  provider            = aws.hml
  transit_gateway_id  = aws_ec2_transit_gateway.viajor-tgw.id
  vpc_id              = var.vpc-sync-hml
  subnet_ids          = var.subnets-sync-hml 

  tags = {
    Name = "sync-hml"
  }
}

#############################
##                         ##
##        DATADEV          ##
##                         ##
#############################
resource "aws_ec2_transit_gateway_vpc_attachment" "datadev" {
  provider            = aws.datadev
  transit_gateway_id  = aws_ec2_transit_gateway.viajor-tgw.id
  vpc_id              = aws_vpc.datadev.id
  subnet_ids          = [ aws_subnet.datadev-private-1-a.id, aws_subnet.datadev-private-1-b.id, aws_subnet.datadev-private-1-c.id ]

  depends_on = [ aws_ram_resource_association.tgw-datadev, aws_ram_principal_association.tgw-datadev ]

  tags = {
    Name = "datadev"
  }
}
