#rotas criadas em "nested loop"
resource "aws_route" "destinations-sync-homolog-to-viajor-vpc" {
  provider = aws.hml

  route_table_id         = var.rtb-sync-hml
  destination_cidr_block = aws_vpc.viajor-vpc.cidr_block
  transit_gateway_id     = aws_ec2_transit_gateway.viajor-tgw.id

  depends_on = [
    aws_ec2_transit_gateway.viajor-tgw
  ]
}

resource "aws_route" "routes-viajor-to-sync-homolog" {
  provider = aws.prod

  route_table_id          = data.aws_route_table.rtb_nat_com_vpn.id
  for_each                = var.destinations-sync-hml
  destination_cidr_block  = each.value
  transit_gateway_id      = aws_ec2_transit_gateway.viajor-tgw.id

  depends_on = [
    aws_ec2_transit_gateway.viajor-tgw
  ]
}

data "aws_route_table" "rtb_nat_com_vpn" {
  route_table_id = "rtb-06a12272ccc0871ad"
}
