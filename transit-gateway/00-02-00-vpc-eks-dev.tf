resource "aws_cloudwatch_log_group" "eks-dev" {
  provider         = aws.hml
  name             = "eks-dev"
  depends_on       = [
    aws_iam_role.eks-dev-flow-logs
  ]
}

data "aws_iam_policy_document" "eks-dev" {
  statement {
    effect = "Allow"

    principals {
      type        = "Service"
      identifiers = ["vpc-flow-logs.amazonaws.com"]
    }

    actions = ["sts:AssumeRole"]
  }
}

resource "aws_iam_role" "eks-dev-flow-logs" {
  provider           = aws.hml
  name               = "FlowLogsRole"
  assume_role_policy = data.aws_iam_policy_document.eks-dev.json
  
}

data "aws_iam_policy_document" "eks-dev-flow-logs-policy" {
  statement {
    effect = "Allow"

    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents",
      "logs:DescribeLogGroups",
      "logs:DescribeLogStreams",
    ]

    resources = ["*"]
  }
}

resource "aws_iam_role_policy" "eks-dev-flow-logs-policy" {
  provider         = aws.hml
  name             = "FlowLogsPolicy"
  role             = aws_iam_role.eks-dev-flow-logs.id
  policy           = data.aws_iam_policy_document.eks-dev-flow-logs-policy.json

  depends_on       = [
          aws_iam_role.eks-dev-flow-logs
  ]

}

#############################
##                         ##
##        VPC NOVA         ##
##                         ##
#############################

resource "aws_vpc" "eks-developer-vpc" {
  provider              = aws.hml
  cidr_block            = "*********/16"
  enable_dns_hostnames  = "true"
  tags                                 = {
    "name"                                        = "eks"
    "alpha.eksctl.io/cluster-name"                = "dev-tools",
    "alpha.eksctl.io/cluster-oidc-enabled"        = "true",
    "eksctl.cluster.k8s.io/v1alpha1/cluster-name" = "dev-tools",
    "managed-by"                                  = "terraform",
    "env"                                         = "dev"
    "kubernetes.io/role/internal-elb"             = "1"
  }
}

resource "aws_flow_log" "eks-developer-vpc" {
  provider        = aws.hml
  iam_role_arn    = aws_iam_role.eks-dev-flow-logs.arn
  log_destination = aws_cloudwatch_log_group.eks-developer-logs.arn
  traffic_type    = "ALL"
  vpc_id          = aws_vpc.eks-developer-vpc.id

  depends_on      = [
    aws_cloudwatch_log_group.eks-developer-logs
  ]
}

resource "aws_cloudwatch_log_group" "eks-developer-logs" {
  provider         = aws.hml
  name             = "eks-developer-vpc-logs"
}


##############################
##                          ##
##  SUBNETS PRIVADAS        ##
##                          ##
##############################

##################
###   us-east-1a
##################
resource "aws_subnet" "dev-private0-us-east-a" {
  provider                                        = aws.hml
  vpc_id                                          = aws_vpc.eks-developer-vpc.id
  cidr_block                                      = "*********/23"
  availability_zone                               = "us-east-1a"
  map_public_ip_on_launch                         = false

  tags = {
    env                               = "dev",
    managed-by                        = "terraform"
    "kubernetes.io/role/internal-elb"   = "1"
    Name                              = "private"
  }

}

resource "aws_subnet" "dev-private2-us-east-a" {
  provider                                        = aws.hml
  vpc_id                                          = aws_vpc.eks-developer-vpc.id
  cidr_block                                      = "*********/23"
  availability_zone                               = "us-east-1a"
  map_public_ip_on_launch                         = false

  tags = {
    env                               = "dev",
    managed-by                        = "terraform"
    "kubernetes.io/role/internal-elb"   = "1"
    Name                              = "private"
  }

}

##################
###   us-east-1b
##################
resource "aws_subnet" "dev-private4-us-east-b" {
  provider                                        = aws.hml
  vpc_id                                          = aws_vpc.eks-developer-vpc.id
  cidr_block                                      = "*********/23"
  availability_zone                               = "us-east-1b"
  map_public_ip_on_launch                         = false

  tags = {
    env                               = "dev",
    managed-by                        = "terraform"
    "kubernetes.io/role/internal-elb"   = "1"
    Name                              = "private"
  }
  
}

resource "aws_subnet" "dev-private6-us-east-b" {
  provider                                        = aws.hml
  vpc_id                                          = aws_vpc.eks-developer-vpc.id
  cidr_block                                      = "*********/23"
  availability_zone                               = "us-east-1b"
  map_public_ip_on_launch                         = false

  tags = {
    env                               = "dev",
    managed-by                        = "terraform"
    "kubernetes.io/role/internal-elb"   = "1"
    Name                              = "private"
  }
  
}

##################
##  us-east-1c
##################
resource "aws_subnet" "dev-private8-us-east-c" {
  provider                                        = aws.hml
  vpc_id                                          = aws_vpc.eks-developer-vpc.id
  cidr_block                                      = "*********/23"
  availability_zone                               = "us-east-1c"
  map_public_ip_on_launch                         = false

  tags = {
    env                               = "dev",
    managed-by                        = "terraform"
    "kubernetes.io/role/internal-elb"   = "1"
    Name                              = "private"
  }

}

resource "aws_subnet" "dev-private10-us-east-c" {
  provider                                        = aws.hml
  vpc_id                                          = aws_vpc.eks-developer-vpc.id
  cidr_block                                      = "**********/23"
  availability_zone                               = "us-east-1c"
  map_public_ip_on_launch                         = false

  tags = {
    env                               = "dev",
    managed-by                        = "terraform"
    "kubernetes.io/role/internal-elb"   = "1"
    Name                              = "private"
  }

}


#############################
##                         ##
##        NAT GATEWAY      ##
##                         ##
#############################

resource "aws_nat_gateway" "eks-developer-nat-gateway" {
  provider          = aws.hml
  subnet_id         = aws_subnet.dev-public-12-us-east-b.id
  allocation_id     = aws_eip.nat-gateway-eks-developer.id

  tags = {
    Name        = "eks",
    env         = "dev",
    managed-by  = "terraform"
    Name        = "public"
  }

  depends_on = [
    aws_subnet.dev-private2-us-east-a,
    aws_eip.nat-gateway-eks-developer
  ]
}

resource "aws_eip" "nat-gateway-eks-developer" {
  provider          = aws.hml
}


#############################
##                         ##
##   INTERNET GATEWAY      ##
##                         ##
#############################
resource "aws_internet_gateway" "igw-eks-developer" {
  provider          = aws.hml
  vpc_id            = aws_vpc.eks-developer-vpc.id

  tags = {
    Name        = "eks",
    env         = "dev",
    managed-by  = "terraform"
    Name        = "public"
  }
}


##################################
##                              ##
##  PUBLIC SUBNETS NAT GATEWAY  ##
##                              ##
##################################

resource "aws_subnet" "dev-public-12-us-east-b" {
  provider                                        = aws.hml
  vpc_id                                          = aws_vpc.eks-developer-vpc.id
  cidr_block                                      = "**********/24"
  availability_zone                               = "us-east-1b"
  map_public_ip_on_launch                         = true

  tags = {
    env                       = "dev",
    managed-by                = "terraform"
   "kubernetes.io/role/elb"   = "1"
   Name                       = "public"
  }

}

resource "aws_subnet" "dev-public-13-us-east-c" {
  provider                                        = aws.hml
  vpc_id                                          = aws_vpc.eks-developer-vpc.id
  cidr_block                                      = "**********/24"
  availability_zone                               = "us-east-1c"
  map_public_ip_on_launch                         = true

  tags = {
    env                       = "dev",
    managed-by                = "terraform"
    "kubernetes.io/role/elb"  = "1"
    Name                      = "public"
  }

}

resource "aws_subnet" "dev-public-14-us-east-a" {
  provider                                        = aws.hml
  vpc_id                                          = aws_vpc.eks-developer-vpc.id
  cidr_block                                      = "**********/24"
  availability_zone                               = "us-east-1a"
  map_public_ip_on_launch                         = true

  tags = {
    env                       = "dev",
    managed-by                = "terraform",
    "kubernetes.io/role/elb"  = "1"
    Name                      = "public"
  }

}
