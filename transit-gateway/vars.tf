#Rotas por vpc
variable "destinations-viajor-vpc" {
  type = set(string)
  default = [ 
    "172.29.0.0/16",
    "192.168.224.0/20"
  ]
}

variable "destinations-prod-vpc" {
  type = set(string)
  default = [ 
    "172.17.0.0/16",
  ]
}

variable "destinations-eks-dev-vpc" {
  type = set(string)
  default = [
    "10.20.0.0/16"
  ]
}

variable "destinations-eks-prod-tools-vpc" {
  type = set(string)
  default = [ 
    "172.20.0.0/16"
  ]
}

variable "destinations-ecs-hml-vpc" {
  type = set(string)
  default = [ 
    "172.16.0.0/16"
  ]
}

variable "destinations-ecs-dev-vpc" {
  type = set(string)
  default = [ 
    "172.18.0.0/16"
  ]
}

####################################################
####          ROTAS VPN                         ####
####################################################
variable "destinations-vpn" {
  type = set(string)
  default = [ 
    "172.24.52.101/32",
    "172.26.16.37/32",
    "10.0.0.81/32",
    "***********/32",
    "*************/32",
    "*************/32",
    "************/32",
    "************/24",
    "***********/24",
    "************/24",
    "************/24",
    "*********/24",
    "***********/24",
    "**********/24",
    "***********/24",
    "***********/24",
    "***********/21",
    "********/16",
    "*********/16",
    "*********/16",
    "**********/16",
    "***********/32",
    "***********/32"
  ]
}


####################################################
####    Saída pelo IP *************             ####
####################################################
variable "destinations-nat-externo" {
  type = set(string)
  default = [ 
    "**************/32",
    "*************/32"
  ]
}

#Troubleshoot
#while true
#do
#nc -w3 -zv ************** 389
#nc -w3 -zv ************* 389
#done


####################################################
####          Subnets                           ####
####################################################

variable "viajor-vpc-subnets" {
  type = set(string)
  default = [
    "subnet-06e109f65da43dbcb",
    "subnet-0c04dabb847ecf9e7",
    "subnet-0568ff5da18cb13ca",
    "subnet-03521e7ee635eae01",
    "subnet-0a1d1ac0e4282e7ee",
    "subnet-e88c0eb3",
    "subnet-090e8cdd95d8566ab",
    "subnet-695ba30d",
    "subnet-64a6ea48",
    "subnet-0aa2593463cb0a9ea",
    "subnet-079895c600572cce7",
    "subnet-0506932d77f69a681",
    "subnet-0d1c465a0ff2ec225"
  ]
}   

variable "eks-prod-tools-subnets" {
  type = set(string)
  default = [ 
    "subnet-05f19c00c7c107280",
    "subnet-0822aea5cf219fe2d",
    "subnet-06bc8d8e4dca34da4",
    "subnet-0efe0d893cf30b8d9",
    "subnet-07f109a3aabd6cd13",
    "subnet-0c0b570c347bc7f68",
    "subnet-02313cddbd7eca21e"
  ]
}

variable "ecs-hml-subnets" {
  type = set(string)
  default = [ 
    "subnet-0fa129cb994c92b6c",
    "subnet-0e6362f0706d80a5c",
    "subnet-0024a943d419cd468",
    "subnet-00333267a8a816ec4"
  ]
}

variable "ecs-dev-subnets" {
  type = set(string)
  default = [
    "subnet-0bf314f0d7a306a52",
    "subnet-095ee1e9c84e8bb36",
    "subnet-0ac8ff29104112eae",
    "subnet-05d35dba38c9e50aa"
  ]
}   

variable "prod-vpc-subnets" {
  type = set(string)
  default = [
    "subnet-018591db22b6e2c5a",
    "subnet-01fcd00867458a8c0",
    "subnet-00da2ba575179f5e2",
    "subnet-0e46da0352a394c39"
  ]
}

#######################
#                     #
#     DATALAKE        #
#                     #
#######################

#prod
variable "destinations-datalake-vpc" {
  type = set(string)
  default = [ 
    "10.123.120.0/22"
  ]
}
#dev
variable "destinations-datalake-dev-vpc" {
  type = string
  default = "10.123.124.0/22"
}

#######################
#                     #
#     SYNC HML        #
#                     #
#######################

variable "destinations-sync-hml" {
  type = set(string)
  default = [ 
    "172.28.0.0/24"
  ]
}
variable "rtb-sync-hml" {
  type = string
  default = "rtb-09f0c249f349b2518"
}
variable "vpc-sync-hml" {
  type = string
  default = "vpc-00e0161a5fb168c1c"
}

variable "subnets-sync-hml" {
  type = set(string)
  default = [ 
    "subnet-0903fa830c64ca5a1",
    "subnet-0da072225fde8cbc2",
    "subnet-07a51af6615a802df"
  ]
}

#######################
#                     #
#     RTBS ECS        #
#                     #
#######################

variable "rtbs-ecs-developer" {
  type = set(string)
  default = [ 
    "rtb-0506c98e8d3d6a27a",
    "rtb-020f4c5db63191fe2",
    "rtb-0b169f6f942e48392",
    "rtb-0c9f0e27c8e725f44",
  ]
}

variable "rtbs-ecs-staging" {
  type = set(string)
  default = [ 
    "rtb-08625dd0784c61976",
    "rtb-0c8a9be35b442701a",
    "rtb-01cf5a3c64fc38d7e",
    "rtb-05611dd84f8fcf046",
  ]
}
