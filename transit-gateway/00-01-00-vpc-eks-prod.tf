#############################
##                         ##
##        VPC NOVA         ##
##                         ##
#############################

resource "aws_vpc" "eks-prod-vpc" {
  provider              = aws.prod
  cidr_block            = "**********/16"
  enable_dns_hostnames  = "true"
  tags                                 = {
    "Name"                                        = "eks"
    "alpha.eksctl.io/cluster-name"                = "prod-tools",
    "alpha.eksctl.io/cluster-oidc-enabled"        = "true",
    "eksctl.cluster.k8s.io/v1alpha1/cluster-name" = "prod-tools",
    "managed-by"                                  = "terraform",
    "env"                                         = "prod"
  }
}

resource "aws_flow_log" "eks-prod-vpc" {
  provider        = aws.prod
  iam_role_arn    = "arn:aws:iam::393346304284:role/flowlogsRole"
  log_destination = aws_cloudwatch_log_group.eks-prod-logs.arn
  traffic_type    = "ALL"
  vpc_id          = aws_vpc.eks-prod-vpc.id

  depends_on      = [
    aws_cloudwatch_log_group.eks-prod-logs
  ]
}

resource "aws_cloudwatch_log_group" "eks-prod-logs" {
  provider         = aws.prod
  name             = "eks-prod-vpc-logs"
}


##############################
##                          ##
##  SUBNETS PRIVADAS        ##
##                          ##
##############################

##################
###   us-east-1a
##################
resource "aws_subnet" "private-0-us-east-a" {
  vpc_id                                          = aws_vpc.eks-prod-vpc.id
  cidr_block                                      = "**********/23"
  availability_zone                               = "us-east-1a"
  map_public_ip_on_launch                         = false

  tags = {
    env                     = "prod",
    managed-by              = "terraform"
    "kubernetes.io/role/internal-elb" = "1"
    Name        = "private"
  }

}

resource "aws_subnet" "private-2-us-east-a" {
  vpc_id                                          = aws_vpc.eks-prod-vpc.id
  cidr_block                                      = "**********/23"
  availability_zone                               = "us-east-1a"
  map_public_ip_on_launch                         = false

  tags = {
    env                     = "prod",
    managed-by              = "terraform"
    "kubernetes.io/role/internal-elb" = "1"
    Name        = "private"
  }

}

##################
###   us-east-1b
##################
resource "aws_subnet" "private-4-us-east-b" {
  vpc_id                                          = aws_vpc.eks-prod-vpc.id
  cidr_block                                      = "**********/23"
  availability_zone                               = "us-east-1b"
  map_public_ip_on_launch                         = false

  tags = {
    env                     = "prod",
    managed-by              = "terraform"
    "kubernetes.io/role/internal-elb" = "1"
    Name        = "private"
  }
  
}

resource "aws_subnet" "private-6-us-east-b" {
  vpc_id                                          = aws_vpc.eks-prod-vpc.id
  cidr_block                                      = "**********/23"
  availability_zone                               = "us-east-1b"
  map_public_ip_on_launch                         = false

  tags = {
    env                     = "prod",
    managed-by              = "terraform"
    "kubernetes.io/role/internal-elb" = "1"
    Name        = "private"
  }
  
}

##################
##  us-east-1c
##################
resource "aws_subnet" "private-8-us-east-c" {
  vpc_id                                          = aws_vpc.eks-prod-vpc.id
  cidr_block                                      = "**********/23"
  availability_zone                               = "us-east-1c"
  map_public_ip_on_launch                         = false

  tags = {
    env                     = "prod",
    managed-by              = "terraform"
    "kubernetes.io/role/internal-elb" = "1"
    Name        = "private"
  }

}

resource "aws_subnet" "private-10-us-east-c" {
  vpc_id                                          = aws_vpc.eks-prod-vpc.id
  cidr_block                                      = "***********/23"
  availability_zone                               = "us-east-1c"
  map_public_ip_on_launch                         = false

  tags = {
    env                     = "prod",
    managed-by              = "terraform"
    "kubernetes.io/role/internal-elb" = "1"
    Name        = "private"
  }

}


#############################
##                         ##
##        NAT GATEWAY      ##
##                         ##
#############################

resource "aws_nat_gateway" "eks-nat-gateway" {
  provider          = aws.prod
  subnet_id         = aws_subnet.public-12-us-east-b.id
  allocation_id     = aws_eip.nat-gateway-eks-prod.id

  tags = {
    Name        = "eks",
    env         = "prod",
    managed-by  = "terraform"
  }

  depends_on = [
    aws_subnet.private-2-us-east-a,
    aws_eip.nat-gateway-eks-prod
  ]
}

resource "aws_eip" "nat-gateway-eks-prod" {
  provider          = aws.prod
}


#############################
##                         ##
##   INTERNET GATEWAY      ##
##                         ##
#############################
resource "aws_internet_gateway" "igw-eks-prod" {
  provider          = aws.prod
  vpc_id            = aws_vpc.eks-prod-vpc.id

  tags = {
    Name        = "eks",
    env         = "prod",
    managed-by  = "terraform"
  }
}


##################################
##                              ##
##  PUBLIC SUBNETS NAT GATEWAY  ##
##                              ##
##################################

resource "aws_subnet" "public-12-us-east-b" {
  vpc_id                                          = aws_vpc.eks-prod-vpc.id
  cidr_block                                      = "***********/24"
  availability_zone                               = "us-east-1b"
  map_public_ip_on_launch                         = true

  tags = {
    env         = "prod",
    managed-by  = "terraform",
    "kubernetes.io/role/elb" = "1"
    Name        = "public"
  }

}

resource "aws_subnet" "public-13-us-east-a" {
  vpc_id                                          = aws_vpc.eks-prod-vpc.id
  cidr_block                                      = "***********/24"
  availability_zone                               = "us-east-1a"
  map_public_ip_on_launch                         = true

  tags = {
    env         = "prod",
    managed-by  = "terraform",
    "kubernetes.io/role/elb" = "1",
    Name        = "public"
  }

}


resource "aws_subnet" "public-14-us-east-c" {
  vpc_id                                          = aws_vpc.eks-prod-vpc.id
  cidr_block                                      = "***********/24"
  availability_zone                               = "us-east-1c"
  map_public_ip_on_launch                         = true

  tags = {
    env         = "prod",
    managed-by  = "terraform",
    "kubernetes.io/role/elb" = "1"
  }

}
