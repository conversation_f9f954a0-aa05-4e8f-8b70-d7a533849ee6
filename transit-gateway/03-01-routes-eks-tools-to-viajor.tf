##################################
##    Rotas nova CIDR 172.20.0.0/18
##################################
resource "aws_route" "routes-172-20-viajor-vpc" {
  provider                      = aws.prod
  route_table_id                = aws_route_table.eks-prod-tools-172-20.id
  for_each                      = var.destinations-viajor-vpc
  destination_cidr_block        = each.value
  transit_gateway_id            = aws_ec2_transit_gateway.viajor-tgw.id

  depends_on = [
    aws_route_table.eks-prod-tools-172-20
  ]
}

resource "aws_route" "internet-172-20-nat-gateway" {
  provider                      = aws.prod
  route_table_id                = aws_route_table.eks-prod-tools-172-20.id
  destination_cidr_block        = "0.0.0.0/0"
  nat_gateway_id                = aws_nat_gateway.eks-nat-gateway.id
  
  depends_on = [
    aws_nat_gateway.eks-nat-gateway
  ]
}


###########################
## Public subnets route
###########################
resource "aws_route" "public-subnet-internet-gateway" {
  provider                      = aws.prod
  route_table_id                = aws_route_table.eks-prod-tools-172-20-public.id
  destination_cidr_block        = "0.0.0.0/0"
  gateway_id                    = aws_internet_gateway.igw-eks-prod.id
  
  depends_on = [
    aws_nat_gateway.eks-nat-gateway
  ]
}


##################################
##    Rotas EKS DEV CIDR 10.20.0.0/16
##################################
resource "aws_route" "routes-10-20-eks-dev-vpc" {
  provider                      = aws.prod
  route_table_id                = aws_route_table.eks-prod-tools-172-20.id
  for_each                      = var.destinations-eks-dev-vpc
  destination_cidr_block        = each.value
  transit_gateway_id            = aws_ec2_transit_gateway.viajor-tgw.id

  depends_on = [
    aws_route_table.eks-prod-tools-172-20
  ]
}
