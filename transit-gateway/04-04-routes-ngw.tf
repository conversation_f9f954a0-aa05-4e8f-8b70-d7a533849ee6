resource "aws_route" "ngw-vpn" {
  provider                    = aws.prod
  route_table_id              = aws_route_table.rtb-ngw-subnet.id
  network_interface_id        = "eni-00431538b2703101d"
  for_each                    = var.destinations-vpn
  destination_cidr_block      = each.value
  depends_on = [
    aws_route_table_association.ngw-rtb-association,
    aws_nat_gateway.viajor-private-ngw
  ]
}

resource "aws_route" "eks-dev-ngw-subnet" {
  provider                    = aws.prod
  route_table_id              = aws_route_table.rtb-ngw-subnet.id
  transit_gateway_id          = aws_ec2_transit_gateway.viajor-tgw.id
  for_each                    = var.destinations-eks-dev-vpc
  destination_cidr_block      = each.value

  depends_on = [
    aws_route_table.rtb-ngw-subnet
  ]
}
