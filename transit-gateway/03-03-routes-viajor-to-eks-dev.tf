#rotas criadas em "nested loop"
resource "aws_route" "destinations-eks-dev-vpc" {
  provider = aws.prod
  for_each = { for combo in local.viajor-routes-to-eks-dev : "${combo.rtb}_${combo.destination}" => combo }
  route_table_id            = each.value.rtb
  destination_cidr_block    = each.value.destination
  #vpc_peering_connection_id = aws_vpc_peering_connection_accepter.peer-viajor-vpc-to-eks-dev.id
  transit_gateway_id        = aws_ec2_transit_gateway.viajor-tgw.id

  depends_on = [
    aws_ec2_transit_gateway.viajor-tgw
  ]
}

locals {
  viajor-routes-to-eks-dev = flatten([
    for rtb in var.rtbs-viajor-vpc : [
      for destination in var.destinations-eks-dev-vpc : {
        rtb         = rtb
        destination = destination
      }
    ]
  ])
}
