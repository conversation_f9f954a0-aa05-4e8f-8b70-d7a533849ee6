

import {
  to        = aws_vpc.viajor-vpc
  id        = "vpc-289f564e"
}

resource "aws_vpc" "viajor-vpc" {
  provider          = aws.prod
      tags                                 = {
        "Name"            = "VIAJOR_VPC",
        env               = "prod",
        "managed-by"      = "terraform"
        }
}


resource "aws_subnet" "nat-gateway" {
  vpc_id            = aws_vpc.viajor-vpc.id
  cidr_block        = "172.29.155.0/24"
  availability_zone = "us-east-1a"

  tags = {
    env         = "prod",
    managed-by  = "terraform"
  }
}

resource "aws_subnet" "transit-gateway" {
  vpc_id            = aws_vpc.viajor-vpc.id
  cidr_block        = "172.29.154.0/24"
  availability_zone = "us-east-1a"

  tags = {
    env         = "prod",
    managed-by  = "terraform"
  }
}

resource "aws_subnet" "nat-gateway-b" {
  vpc_id            = aws_vpc.viajor-vpc.id
  cidr_block        = "172.29.156.0/28"
  availability_zone = "us-east-1b"

  tags = {
    env         = "prod",
    managed-by  = "terraform"
  }
}

resource "aws_subnet" "nat-gateway-c" {
  vpc_id            = aws_vpc.viajor-vpc.id
  cidr_block        = "172.29.156.16/28"
  availability_zone = "us-east-1c"

  tags = {
    env         = "prod",
    managed-by  = "terraform"
  }
}

resource "aws_subnet" "nat-gateway-d" {
  vpc_id            = aws_vpc.viajor-vpc.id
  cidr_block        = "172.29.156.32/28"
  availability_zone = "us-east-1d"

  tags = {
    env         = "prod",
    managed-by  = "terraform"
  }
}

resource "aws_subnet" "nat-gateway-e" {
  vpc_id            = aws_vpc.viajor-vpc.id
  cidr_block        = "172.29.156.48/28"
  availability_zone = "us-east-1e"

  tags = {
    env         = "prod",
    managed-by  = "terraform"
  }
}

resource "aws_subnet" "nat-gateway-f" {
  vpc_id            = aws_vpc.viajor-vpc.id
  cidr_block        = "172.29.156.64/28"
  availability_zone = "us-east-1f"

  tags = {
    env         = "prod",
    managed-by  = "terraform"
  }
}