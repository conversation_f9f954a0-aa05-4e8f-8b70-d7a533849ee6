resource "aws_route" "sync_vpc_tgw_route" {
  provider                = aws.prod
  route_table_id          = aws_route_table.tgw.id
  for_each                = var.destinations-sync-hml
  destination_cidr_block  = each.value
  transit_gateway_id      = aws_ec2_transit_gateway.viajor-tgw.id
}
resource "aws_ec2_transit_gateway_route" "route_to_sync_vpc" {
  provider                        = aws.prod
  for_each                        = var.destinations-sync-hml
  destination_cidr_block          = each.value
  transit_gateway_attachment_id   = aws_ec2_transit_gateway_vpc_attachment.sync_hml_vpc_attachment.id
  transit_gateway_route_table_id  = aws_ec2_transit_gateway.viajor-tgw.association_default_route_table_id
}