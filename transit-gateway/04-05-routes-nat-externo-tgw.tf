
resource "aws_ec2_transit_gateway_route" "nat-saida" {
  provider                        = aws.prod
  for_each                        = var.destinations-nat-externo
  destination_cidr_block          = each.value
  transit_gateway_attachment_id   = aws_ec2_transit_gateway_vpc_attachment.viajor-vpc-attach.id
  transit_gateway_route_table_id  = aws_ec2_transit_gateway.viajor-tgw.association_default_route_table_id
}

#novo cluster
resource "aws_route" "prod-us-east-1-routes-nat-rtb-private" {
  provider                  = aws.prod
  route_table_id            = aws_route_table.eks-prod-tools-172-20.id
  for_each                  = var.destinations-nat-externo
  destination_cidr_block    = each.value
  transit_gateway_id        = aws_ec2_transit_gateway.viajor-tgw.id

}

resource "aws_route" "prod-us-east-1-routes-nat-rtb-public" {
  provider                  = aws.prod
  route_table_id            = aws_route_table.eks-prod-tools-172-20-public.id
  for_each                  = var.destinations-nat-externo
  destination_cidr_block    = each.value
  transit_gateway_id        = aws_ec2_transit_gateway.viajor-tgw.id

}

