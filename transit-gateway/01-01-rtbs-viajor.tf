#Tabelas de roteamento
variable "rtbs-viajor-vpc" {
  type = set(string)
  default = [ "rtb-0a96bcc5191a32697",
              "rtb-02e3ec59f3d63d794",
              "rtb-087eb08831d708e26",
              "rtb-0a49e5d92d75fdade",
              "rtb-57c5fe31",
              "rtb-99b982ff",
              "rtb-06a12272ccc0871ad",
              "rtb-07f6d77dee1f8d7d6",
              "rtb-08e14595f0d98cb68",
              "rtb-0762909f9cc52649a",
            ]
}

##import
import {
    for_each    = var.rtbs-viajor-vpc
    to          = aws_route_table.viajor[each.key]
    id          = each.value
}

resource "aws_route_table" "viajor" {
    provider    = aws.prod
    for_each    = var.rtbs-viajor-vpc
    vpc_id      = aws_vpc.viajor-vpc.id

    tags             = {
          "managed-by"      = "terraform"
          "env"             = "prod"
    }
}
##end import

#route table nat gateway
resource "aws_route_table" "rtb-ngw-subnet" {
    provider    = aws.prod
    vpc_id      = aws_vpc.viajor-vpc.id

    tags             = {
            "managed-by"        = "terraform",
            "env"               = "prod"
    }
}

resource "aws_route_table_association" "ngw-rtb-association" {
    provider       = aws.prod
    subnet_id      = aws_subnet.nat-gateway.id
    route_table_id = aws_route_table.rtb-ngw-subnet.id
}

#route table transit gateway subnet
resource "aws_route_table" "tgw" {
    provider    = aws.prod
    vpc_id      = aws_vpc.viajor-vpc.id

    tags             = {
            "managed-by"        = "terraform",
            "env"               = "prod"
    }
}

resource "aws_route_table_association" "tgw" {
    provider       = aws.prod
    subnet_id      = aws_subnet.transit-gateway.id
    route_table_id = aws_route_table.tgw.id
}

resource "aws_route_table_association" "tgw-b" {
    provider       = aws.prod
    subnet_id      = aws_subnet.nat-gateway-b.id
    route_table_id = aws_route_table.tgw.id
}

resource "aws_route_table_association" "tgw-c" {
    provider       = aws.prod
    subnet_id      = aws_subnet.nat-gateway-c.id
    route_table_id = aws_route_table.tgw.id
}

resource "aws_route_table_association" "tgw-d" {
    provider       = aws.prod
    subnet_id      = aws_subnet.nat-gateway-d.id
    route_table_id = aws_route_table.tgw.id
}

resource "aws_route_table_association" "tgw-e" {
    provider       = aws.prod
    subnet_id      = aws_subnet.nat-gateway-e.id
    route_table_id = aws_route_table.tgw.id
}

resource "aws_route_table_association" "tgw-f" {
    provider       = aws.prod
    subnet_id      = aws_subnet.nat-gateway-f.id
    route_table_id = aws_route_table.tgw.id
}

