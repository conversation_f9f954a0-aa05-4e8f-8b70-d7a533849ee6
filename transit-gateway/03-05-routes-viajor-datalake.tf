#rotas criadas em "nested loop"
resource "aws_route" "destinations-datalake" {
  provider                    = aws.prod
  for_each = { for combo in local.viajor-routes-to-datalake : "${combo.rtb}_${combo.destination}" => combo }
  route_table_id              = each.value.rtb
  destination_cidr_block      = each.value.destination
  transit_gateway_id          = aws_ec2_transit_gateway.viajor-tgw.id

  depends_on = [
    aws_ec2_transit_gateway.viajor-tgw
  ]
}

locals {
  viajor-routes-to-datalake = flatten([
    for rtb in var.rtbs-viajor-vpc : [
      for destination in var.destinations-datalake-vpc : {
        rtb         = rtb
        destination = destination
      }
    ]
  ])
}
