resource "aws_vpc_endpoint" "vpc_endpoint_rds_dev" {
  provider          = aws.hml

  vpc_id            = aws_vpc.ecs-developer.id
  service_name      = "com.amazonaws.us-east-1.rds"
  vpc_endpoint_type = "Interface"

  security_group_ids = [aws_security_group.vpc_endpoint_rds_dev_sg.id]
  private_dns_enabled = true

  tags = {
    env         = "developer",
    managed-by  = "terraform"
    Ambiente    = "developer"
    Name        = "vpc_endpoint_rds_dev"
  }
}

resource "aws_security_group" "vpc_endpoint_rds_dev_sg" {
  provider          = aws.hml

  name        = "vpc-endpoint-rds-dev-sg"
  description = "Security group for VPC endpoint RDS"
  vpc_id      = aws_vpc.ecs-developer.id

  ingress {
    from_port   = 3306
    to_port     = 3306
    protocol    = "tcp"
    cidr_blocks = [aws_vpc.ecs-developer.cidr_block]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
  tags = {
    env         = "developer",
    managed-by  = "terraform"
    Ambiente    = "developer"
    Name        = "vpc_endpoint_rds_dev-sg"
  }
}

resource "aws_vpc_endpoint" "vpc_endpoint_eks_dev_rds" {
  provider          = aws.hml

  vpc_id            = aws_vpc.eks-developer-vpc.id
  service_name      = "com.amazonaws.us-east-1.rds"
  vpc_endpoint_type = "Interface"

  security_group_ids = [aws_security_group.vpc_endpoint_eks_dev_rds_sg.id]
  private_dns_enabled = true

  tags = {
    env         = "developer",
    managed-by  = "terraform"
    Ambiente    = "developer"
    Name        = "vpc_endpoint_eks_dev_rds"
  }
}

resource "aws_security_group" "vpc_endpoint_eks_dev_rds_sg" {
  provider          = aws.hml

  name        = "vpc-endpoint-eks-dev-rds-sg"
  description = "Security group for VPC endpoint RDS"
  vpc_id      = aws_vpc.eks-developer-vpc.id

  ingress {
    from_port   = 3306
    to_port     = 3306
    protocol    = "tcp"
    cidr_blocks = [aws_vpc.eks-developer-vpc.cidr_block]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
  tags = {
    env         = "developer",
    managed-by  = "terraform"
    Ambiente    = "developer"
    Name        = "vpc_endpoint_eks_dev_rds_sg"
  }
}
