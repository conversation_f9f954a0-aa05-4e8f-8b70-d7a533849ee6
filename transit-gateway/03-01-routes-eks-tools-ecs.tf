################################################
##    Rotas ECS PROJETO hml                   ##
################################################
resource "aws_route" "routes-172-20-ecs-dev-vpc" {
  provider                      = aws.prod
  route_table_id                = aws_route_table.eks-prod-tools-172-20.id
  for_each                      = var.destinations-ecs-dev-vpc
  destination_cidr_block        = each.value
  transit_gateway_id            = aws_ec2_transit_gateway.viajor-tgw.id

  depends_on = [
    aws_route_table.eks-prod-tools-172-20
  ]
}

resource "aws_route" "routes-172-20-ecs-hml-vpc" {
  provider                      = aws.prod
  route_table_id                = aws_route_table.eks-prod-tools-172-20.id
  for_each                      = var.destinations-ecs-hml-vpc
  destination_cidr_block        = each.value
  transit_gateway_id            = aws_ec2_transit_gateway.viajor-tgw.id

  depends_on = [
    aws_route_table.eks-prod-tools-172-20
  ]
}