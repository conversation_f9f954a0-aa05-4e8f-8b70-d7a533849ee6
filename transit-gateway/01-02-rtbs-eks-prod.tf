######################################
##                                  ##
##                                  ##
##      NOVA VPC EKS                ##
##                                  ##
##                                  ##
######################################


resource "aws_route_table" "eks-prod-tools-172-20" {
    provider    = aws.prod
    vpc_id      = aws_vpc.eks-prod-vpc.id

    tags             = {
            "managed-by"                                  = "terraform",
            "env"                                         = "prod",
            "alpha.eksctl.io/cluster-name"                = "prod-tools",
            "alpha.eksctl.io/cluster-oidc-enabled"        = "true",
            "eksctl.cluster.k8s.io/v1alpha1/cluster-name" = "prod-tools"

    }

}

resource "aws_route_table_association" "private-0-172-20" {
    provider       = aws.prod
    subnet_id      = aws_subnet.private-0-us-east-a.id
    route_table_id = aws_route_table.eks-prod-tools-172-20.id
}

resource "aws_route_table_association" "private-2-172-20" {
    provider       = aws.prod
    subnet_id      = aws_subnet.private-2-us-east-a.id
    route_table_id = aws_route_table.eks-prod-tools-172-20.id
}

resource "aws_route_table_association" "private-4-172-20" {
    provider       = aws.prod
    subnet_id      = aws_subnet.private-4-us-east-b.id
    route_table_id = aws_route_table.eks-prod-tools-172-20.id
}


resource "aws_route_table_association" "private-6-172-20" {
    provider       = aws.prod
    subnet_id      = aws_subnet.private-6-us-east-b.id
    route_table_id = aws_route_table.eks-prod-tools-172-20.id
}


resource "aws_route_table_association" "private-8-172-20" {
    provider       = aws.prod
    subnet_id      = aws_subnet.private-8-us-east-c.id
    route_table_id = aws_route_table.eks-prod-tools-172-20.id
}


resource "aws_route_table_association" "private-10-172-20" {
    provider       = aws.prod
    subnet_id      = aws_subnet.private-10-us-east-c.id
    route_table_id = aws_route_table.eks-prod-tools-172-20.id
}

####################################
##                                ##
##                                ##
##      PUBLIC ROUTE TABLE        ##
##                                ## 
####################################

resource "aws_route_table" "eks-prod-tools-172-20-public" {
    provider    = aws.prod
    vpc_id      = aws_vpc.eks-prod-vpc.id

    tags             = {
            "managed-by"                                  = "terraform",
            "env"                                         = "prod",
            "alpha.eksctl.io/cluster-name"                = "prod-tools",
            "alpha.eksctl.io/cluster-oidc-enabled"        = "true",
            "eksctl.cluster.k8s.io/v1alpha1/cluster-name" = "prod-tools"

    }

}

resource "aws_route_table_association" "public-12-172-20" {
    provider       = aws.prod
    subnet_id      = aws_subnet.public-12-us-east-b.id
    route_table_id = aws_route_table.eks-prod-tools-172-20-public.id
}

resource "aws_route_table_association" "public-13-172-20" {
    provider       = aws.prod
    subnet_id      = aws_subnet.public-13-us-east-a.id
    route_table_id = aws_route_table.eks-prod-tools-172-20-public.id
}

resource "aws_route_table_association" "public-14-172-20" {
    provider       = aws.prod
    subnet_id      = aws_subnet.public-14-us-east-c.id
    route_table_id = aws_route_table.eks-prod-tools-172-20-public.id
}
