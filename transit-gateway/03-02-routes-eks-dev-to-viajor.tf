##################################
##    Rotas nova CIDR 10.20.0.0/16
##################################
resource "aws_route" "routes-10-20-viajor-vpc" {
  provider                      = aws.hml
  route_table_id                = aws_route_table.eks-dev-10-20.id
  for_each                      = var.destinations-viajor-vpc
  destination_cidr_block        = each.value
  transit_gateway_id            = aws_ec2_transit_gateway.viajor-tgw.id

  depends_on = [
    aws_route_table.eks-dev-10-20
  ]
}

resource "aws_route" "internet-10-20-nat-gateway" {
  provider                      = aws.hml
  route_table_id                = aws_route_table.eks-dev-10-20.id
  destination_cidr_block        = "0.0.0.0/0"
  nat_gateway_id                = aws_nat_gateway.eks-developer-nat-gateway.id
  
  depends_on = [
    aws_nat_gateway.eks-developer-nat-gateway
  ]
}


###########################
## Public subnets route
###########################
resource "aws_route" "eks-developer-internet-gateway" {
  provider                      = aws.hml
  route_table_id                = aws_route_table.eks-developer-10-20-public.id
  destination_cidr_block        = "0.0.0.0/0"
  gateway_id                    = aws_internet_gateway.igw-eks-developer.id
  
  depends_on = [
    aws_nat_gateway.eks-developer-nat-gateway
  ]
}

##################################
##    Rotas EKS TOOLS CIDR 172.20.0.0/16
##################################
resource "aws_route" "routes-172-20-eks-dev-vpc" {
  provider                      = aws.hml
  route_table_id                = aws_route_table.eks-dev-10-20.id
  for_each                      = var.destinations-eks-prod-tools-vpc
  destination_cidr_block        = each.value
  transit_gateway_id            = aws_ec2_transit_gateway.viajor-tgw.id

  depends_on = [
    aws_route_table.eks-dev-10-20
  ]
}

##################################
##    Rotas EKS TOOLS CIDR 172.20.0.0/16
##################################
resource "aws_route" "routes-eks-dev-integracoes-vpn" {
  provider                      = aws.hml
  route_table_id                = aws_route_table.eks-dev-10-20.id
  for_each                      = var.destinations-vpn
  destination_cidr_block        = each.value
  transit_gateway_id            = aws_ec2_transit_gateway.viajor-tgw.id

  depends_on = [
    aws_route_table.eks-dev-10-20
  ]
}

