import {
  to        = aws_vpc.ecs-developer
  id        = "vpc-00648d5a38c32ba60"
}

resource "aws_vpc" "ecs-developer" {
  provider          = aws.hml
  tags = {
    env         = "developer",
    managed-by  = "terraform"
    Ambiente    = "developer"
    Name        = "developer"
  }
}


import {
  to        = aws_subnet.developer-us-east-1a-1
  id        = "subnet-0bf314f0d7a306a52"
  provider  = aws.hml
}

import {
  to        = aws_subnet.developer-us-east-1a-2
  id        = "subnet-0ac8ff29104112eae"
}

import {
  to        = aws_subnet.developer-us-east-1b-1
  id        = "subnet-095ee1e9c84e8bb36"
}

import {
  to        = aws_subnet.developer-us-east-1b-2
  id        = "subnet-05d35dba38c9e50aa"
}


resource "aws_subnet" "developer-us-east-1a-1" {
  vpc_id                      = aws_vpc.ecs-developer.id
  cidr_block                  = "**********/19"
  map_public_ip_on_launch     = true
  
  provider          = aws.hml

  tags = {
    env         = "developer",
    managed-by  = "terraform"
    Ambiente    = "developer"
    Name        = "developer"
  }
}

resource "aws_subnet" "developer-us-east-1a-2" {
  vpc_id                      = aws_vpc.ecs-developer.id
  cidr_block                  = "***********/19"
  provider          = aws.hml

  tags = {
    env         = "developer",
    managed-by  = "terraform"
    Ambiente    = "developer"
    Name        = "developer"
  }
}

resource "aws_subnet" "developer-us-east-1b-1" {
  vpc_id                      = aws_vpc.ecs-developer.id
  cidr_block                  = "***********/19"
  provider          = aws.hml

  tags = {
    env         = "developer",
    managed-by  = "terraform"
    Ambiente    = "developer"
    Name        = "developer"
  }
}

resource "aws_subnet" "developer-us-east-1b-2" {
  vpc_id                      = aws_vpc.ecs-developer.id
  cidr_block                  = "***********/19"
  map_public_ip_on_launch     = true
  provider          = aws.hml

  tags = {
    env         = "developer"
    managed-by  = "terraform"
    Ambiente    = "developer"
    Name        = "developer"

  }
}