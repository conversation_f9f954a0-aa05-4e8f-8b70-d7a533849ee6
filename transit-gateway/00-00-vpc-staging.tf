import {
  to        = aws_vpc.ecs-staging
  id        = "vpc-097093d2681a97b1d"
}

resource "aws_vpc" "ecs-staging" {
  provider          = aws.hml
  tags = {
    env         = "staging",
    managed-by  = "terraform"
    Ambiente    = "staging"
    Name        = "staging"
  }
}


import {
  to        = aws_subnet.staging-us-east-1a-1
  id        = "subnet-0e6362f0706d80a5c"
}

import {
  to        = aws_subnet.staging-us-east-1a-2
  id        = "subnet-0fa129cb994c92b6c"
}

import {
  to        = aws_subnet.staging-us-east-1b-1
  id        = "subnet-00333267a8a816ec4"
}

import {
  to        = aws_subnet.staging-us-east-1b-2
  id        = "subnet-0024a943d419cd468"
}


resource "aws_subnet" "staging-us-east-1a-1" {
  vpc_id            = aws_vpc.ecs-staging.id
  cidr_block        = "***********/19"
  provider          = aws.hml
  
  tags = {
    env         = "staging",
    managed-by  = "terraform"
    Ambiente    = "staging"
    Name        = "staging"
  }
}

resource "aws_subnet" "staging-us-east-1a-2" {
  vpc_id                      = aws_vpc.ecs-staging.id
  cidr_block                  = "**********/19"
  map_public_ip_on_launch     = true
  provider          = aws.hml

  tags = {
    env         = "staging",
    managed-by  = "terraform"
    Ambiente    = "staging"
    Name        = "staging"
  }
}

resource "aws_subnet" "staging-us-east-1b-1" {
  vpc_id                      = aws_vpc.ecs-staging.id
  cidr_block                  = "***********/19"
  map_public_ip_on_launch     = true
  provider          = aws.hml

  tags = {
    env         = "staging",
    managed-by  = "terraform"
    Ambiente    = "staging"
    Name        = "staging"
  }
}

resource "aws_subnet" "staging-us-east-1b-2" {
  vpc_id            = aws_vpc.ecs-staging.id
  cidr_block        = "***********/19"
  provider          = aws.hml

  tags = {
    env         = "staging",
    managed-by  = "terraform"
    Ambiente    = "staging"
    Name        = "staging"
  }
}