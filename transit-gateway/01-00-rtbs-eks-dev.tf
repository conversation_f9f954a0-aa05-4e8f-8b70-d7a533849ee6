######################################
##                                  ##
##                                  ##
##      NOVA VPC EKS                ##
##                                  ##
##                                  ##
######################################


resource "aws_route_table" "eks-dev-10-20" {
    provider    = aws.hml
    vpc_id      = aws_vpc.eks-developer-vpc.id

    tags             = {
            "managed-by"                                  = "terraform",
            "env"                                         = "prod",
            "alpha.eksctl.io/cluster-name"                = "prod-tools",
            "alpha.eksctl.io/cluster-oidc-enabled"        = "true",
            "eksctl.cluster.k8s.io/v1alpha1/cluster-name" = "prod-tools"

    }

}

resource "aws_route_table_association" "dev-private0-10-20" {
    provider       = aws.hml
    subnet_id      = aws_subnet.dev-private0-us-east-a.id
    route_table_id = aws_route_table.eks-dev-10-20.id
}

resource "aws_route_table_association" "dev-private2-10-20" {
    provider       = aws.hml
    subnet_id      = aws_subnet.dev-private2-us-east-a.id
    route_table_id = aws_route_table.eks-dev-10-20.id
}

resource "aws_route_table_association" "dev-private4-10-20" {
    provider       = aws.hml
    subnet_id      = aws_subnet.dev-private4-us-east-b.id
    route_table_id = aws_route_table.eks-dev-10-20.id
}


resource "aws_route_table_association" "dev-private6-10-20" {
    provider       = aws.hml
    subnet_id      = aws_subnet.dev-private6-us-east-b.id
    route_table_id = aws_route_table.eks-dev-10-20.id
}


resource "aws_route_table_association" "dev-private8-10-20" {
    provider       = aws.hml
    subnet_id      = aws_subnet.dev-private8-us-east-c.id
    route_table_id = aws_route_table.eks-dev-10-20.id
}


resource "aws_route_table_association" "dev-private10-10-20" {
    provider       = aws.hml
    subnet_id      = aws_subnet.dev-private10-us-east-c.id
    route_table_id = aws_route_table.eks-dev-10-20.id
}


####################################
##                                ##
##                                ##
##      PUBLIC ROUTE TABLE        ##
##                                ## 
####################################

resource "aws_route_table" "eks-developer-10-20-public" {
    provider    = aws.hml
    vpc_id      = aws_vpc.eks-developer-vpc.id

    tags             = {
            "managed-by"                                  = "terraform",
            "env"                                         = "prod",
            "alpha.eksctl.io/cluster-name"                = "prod-tools",
            "alpha.eksctl.io/cluster-oidc-enabled"        = "true",
            "eksctl.cluster.k8s.io/v1alpha1/cluster-name" = "prod-tools"

    }

}

resource "aws_route_table_association" "public-12-10-20" {
    provider       = aws.hml
    subnet_id      = aws_subnet.dev-public-12-us-east-b.id
    route_table_id = aws_route_table.eks-developer-10-20-public.id
}

resource "aws_route_table_association" "public-13-10-20" {
    provider       = aws.hml
    subnet_id      = aws_subnet.dev-public-13-us-east-c.id
    route_table_id = aws_route_table.eks-developer-10-20-public.id
}

resource "aws_route_table_association" "public-14-10-20" {
    provider       = aws.hml
    subnet_id      = aws_subnet.dev-public-14-us-east-a.id
    route_table_id = aws_route_table.eks-developer-10-20-public.id
}

