resource "aws_nat_gateway" "viajor-private-ngw" {
  provider          = aws.prod
  connectivity_type = "private"
  subnet_id         = aws_subnet.nat-gateway.id

  tags = {
    env         = "prod",
    managed-by  = "terraform"
  }
}

resource "aws_ec2_transit_gateway" "viajor-tgw" {
  description                     = "Cross VPCs traffic centralization"
  provider                        = aws.prod
  default_route_table_propagation = "enable"
  dns_support                     = "enable"
  transit_gateway_cidr_blocks     = [ "************/24" ]
  multicast_support               = "disable"
  auto_accept_shared_attachments  = "enable" 

  tags = {
    env         = "prod",
    managed-by  = "terraform"
  }
}

#Flow logs transit gateway
resource "aws_flow_log" "transit-gateway-prod" {
  provider                  = aws.prod
  iam_role_arn              = "arn:aws:iam::393346304284:role/flowlogsRole"
  log_destination           = aws_cloudwatch_log_group.transit-gateway-prod.arn
  traffic_type              = "ALL"
  transit_gateway_id        = aws_ec2_transit_gateway.viajor-tgw.id
  max_aggregation_interval  = 60

  depends_on      = [
    aws_cloudwatch_log_group.transit-gateway-prod
  ]
}

resource "aws_cloudwatch_log_group" "transit-gateway-prod" {
  provider         = aws.prod
  name             = "transit-gateway"
}