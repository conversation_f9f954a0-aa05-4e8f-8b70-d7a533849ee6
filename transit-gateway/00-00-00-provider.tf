terraform {
  backend "s3" {
    bucket  = "terraform-iac-v2"
    key     = "paytrack/eks/network/terraform.tfstate"
    region  = "us-east-1"
    profile = "main_account"
  }

  required_providers {
    aws = {
      source  = "hashicorp/aws"
    }
  }
}

provider "aws" {
  alias     = "hml"
  region    = "us-east-1"
  profile   = "hml"
}

provider "aws" {
  alias     = "prod"
  region    = "us-east-1"
  profile   = "prod"
}

provider "aws" {
  alias     = "datadev"
  region    = "us-east-1"
  profile   = "datadev"
}