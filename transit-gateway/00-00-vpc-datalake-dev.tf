import {
  to        = aws_vpc.datadev
  id        = "vpc-0eb7f29d117644175"
}

resource "aws_vpc" "datadev" {
  provider          = aws.datadev
  tags = {
    env         = "datadev",
    managed-by  = "sadir"
    Ambiente    = "datadev"
    Name        = "datadev"
  }
}

import {
  to        = aws_subnet.datadev-private-1-a
  id        = "subnet-075e3268a747797e7"
  provider  = aws.datadev
}

import {
  to        = aws_subnet.datadev-private-1-b
  id        = "subnet-029f28ad6aeb2b578"
  provider  = aws.datadev
}

import {
  to        = aws_subnet.datadev-private-1-c
  id        = "subnet-0385355ed299befc2"
  provider  = aws.datadev
}


resource "aws_subnet" "datadev-private-1-a" {
  vpc_id                      = aws_vpc.datadev.id
  cidr_block                  = "10.123.125.0/24"
  provider                    = aws.datadev

  tags = {
    env                               = "datadev"
    managed-by                        = "sadir"
    Ambiente                          = "datadev"
    Name                              = "private-1a"
    "kubernetes.io/role/internal-elb" = "1"
  }
}

resource "aws_subnet" "datadev-private-1-b" {
  vpc_id                      = aws_vpc.datadev.id
  cidr_block                  = "10.123.126.0/24"
  provider                    = aws.datadev

  tags = {
    env                               = "datadev"
    managed-by                        = "sadir"
    Ambiente                          = "datadev"
    Name                              = "private-1b"
    "kubernetes.io/role/internal-elb" = "1"
  }
}

resource "aws_subnet" "datadev-private-1-c" {
  vpc_id                      = aws_vpc.datadev.id
  cidr_block                  = "10.123.127.0/24"
  provider                    = aws.datadev

  tags = {
    env                               = "datadev"
    managed-by                        = "sadir"
    Ambiente                          = "datadev"
    Name                              = "private-1c"
    "kubernetes.io/role/internal-elb" = "1"
  }
}
