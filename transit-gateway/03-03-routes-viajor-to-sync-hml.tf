resource "aws_route" "destinations-sync-hml" {
  provider                  = aws.prod
  for_each                  = { for combo in local.viajor-to-sync-hml : "${combo.rtb}_${combo.destination}" => combo }
  route_table_id            = each.value.rtb
  destination_cidr_block    = each.value.destination
  transit_gateway_id        = aws_ec2_transit_gateway.viajor-tgw.id
}


locals {
  viajor-to-sync-hml = flatten([
    for rtb in var.rtbs-viajor-vpc : [
      for destination in var.destinations-sync-hml : {
        rtb         = rtb
        destination = destination
      }
    ]
  ])
}
