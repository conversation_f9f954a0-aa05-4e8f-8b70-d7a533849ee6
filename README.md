# Configuração de ambiente
Para rodar o terraform é preciso atualizar as credenciais em ~/.aws/credentials </br>
É possível localizar elas acessando o portal de contas da AWS https://paytrack-sso.awsapps.com/start/#/?tab=accounts e clicando em Access keys

Dentro deste arquivo vamos ter uma estrutura a onde as credenciais ficarão
````
[DEFAULT]
Credenciais aqui...

[MAIN_ACCOUNT]
Credenciais aqui...
````


## Ambiente prod
Para rodar o terraform em ambiente [prod] as duas chaves de credenciais precisam estar apontadas pra prod

[DEFAULT] -> Credencial prod </br>
[MAIN_ACCOUNT] -> Credencial prod

## Ambiente de desenvolvimento
Para rodar em ambiente [developer] será preciso tanto a credencial de prod quanto da developer. 
Isso se deve ao fato da conta de prod compartilhar certos recursos com o ambiente developer (Como o S3 por exemplo)

[DEFAULT] -> Credencial da conta developer <br>
[MAIN_ACCOUNT] -> Credencial da conta de prod


## Ambiente homolog, EKS e relacionados

Estes projetos estão usando profiles de SSO para funcionar corretamente.
Configurar utilizando aws cli, conforme orientações disponveis em https://d-**********.awsapps.com/start/#/?tab=accounts


```
[profile prod]
sso_session = sso
sso_account_id = ************
sso_role_name = AWSAdministratorAccess
region = us-east-1
output = text

[profile hml]
sso_session = sso
sso_account_id = ************
sso_role_name = AWSAdministratorAccess
region = us-east-1
output = text
```
