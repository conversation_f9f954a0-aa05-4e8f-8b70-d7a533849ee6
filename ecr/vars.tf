#Incluir novos aqui
variable "new_repos" {
  type = set(string)
  default = [ 
"paytrack/paytrack-notify",
"paytrack/paytrack-autenticador",
"paytrack/paytrack-sync-management",
"paytrack/paytrack-sync-executor",
"paytrack/paytrack-web",
"paytrack/onboarding-backend",
"paytrack/processador-pagamentos",
"tools",
"paytrack/solicitacoes",
"paytrack/paytrack-relatorios",
"paytrack/backstage",
"paytrack/war-room",
"paytrack/notify",
"paytrack/integrador-cangooroo-v2",
"paytrack/aereo-ia",
"paytrack/paytrack-tradutor",
"paytrack/opensearch-index-pattern-creator",
"paytrack/logstash-datadog",
]
}


















#Importados, já existentes antes de haver a centralização
variable "repoistories" {
  type = set(string)
  default = [ 
"paytrack/diretorio-hoteis",
"paytrack/integrador-reservafacil",
"paytrack/integrador-aereo-precificador",
"paytrack/fluxo-financeiro",
"paytrack/acesso",
"paytrack/lambda-get-frontend-version",
"runner",
"paytrack/paytrack-data-provider",
"paytrack-local/paytrack-login-frontend",
"paytrack-local/paytrack-agency-frontend",
"payscan",
"paytrack/clientes-backend",
"paytrack/integrador-movida",
"paytrack/pesquisa-servico",
"paytrack/integrador-queropassagem",
"paytrack/integrador-omnibees",
"paytrack/integrador-bee2pay",
"paytrack/integrador-azul",
"paytrack/integrador-b2b",
"paytrack/convite",
"paytrack-local/paytrack-agency-backend",
"paytrack-local/paytrack-tradutor",
"paytrack-local/paytrack-autenticador",
"clientes",
"paytrack/integrador-arbi",
"paytrack/integrador-sabre",
"paytrack/cartao-backend",
"paytrack/integrador-hrs",
"paytrack/integrador-clickbus",
"paytrack/conciliacao-backend",
"paytrack/integrador-expedia",
"paytrack/integrador-omie",
"paytrack/integrador-ehtl",
"paytrack/ocr-api",
"paytrack/configuracoes",
"paytrack-local/paytrack-backend",
"paytrack-local/paytrack-frontend",
"paytrack-local/paytrack-relatorios",
"cadastros-gerais",
"paytrack/integrador-localiza",
"paytrack/integrador-swap",
"paystore",
"paytrack/integrador-cangooroo",
"paytrack/etl",
"integracoes",
"paytrack/onboarding-backend",
"paytrack/reserva-emissao"
]
}
