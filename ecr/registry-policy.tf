data "aws_iam_policy_document" "allowpushpull" {
  statement {
    sid    = "AllowPushPull"
    effect = "Allow"

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::560211763190:root"]
    }

    actions = [
        "ecr:GetDownloadUrlForLayer",
        "ecr:BatchGetImage",
        "ecr:BatchCheckLayerAvailability"
    ]
  }

  statement {
    sid    = "AllowLambdaPull"
    effect = "Allow"

    principals {
      type        = "Service"
      identifiers = ["lambda.amazonaws.com"]
    }

    actions = [
        "ecr:BatchGetImage",
        "ecr:GetDownloadUrlForLayer",
        "ecr:GetRepositoryPolicy",
    ]

    condition {
      test     = "StringLike"
      variable = "aws:sourceArn"

      values = [
        "arn:aws:lambda:us-east-1:393346304284:function:*"
      ]
    }
  }

    statement {
    sid    = "AllowPagamentosPull"
    effect = "Allow"

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::393346304284:role/aws-reserved/sso.amazonaws.com/AWSReservedSSO_ECRPullPagamentos_a62709f0c901c66e"]
    }

    actions = [
        "ecr:BatchGetImage",
        "ecr:GetDownloadUrlForLayer"
    ]

    condition {
      test     = "StringLike"
      variable = "aws:sourceArn"

      values = [
        "arn:aws:lambda:us-east-1:393346304284:function:*"
      ]
    }
  }


}

resource "aws_ecr_repository_policy" "allow-eks-dev-push-pull" {
  for_each = var.repoistories
  repository = each.value
  policy     = data.aws_iam_policy_document.allowpushpull.json
}

resource "aws_ecr_repository_policy" "new-repos-allow-push-pull-eks" {
  for_each = var.new_repos
  repository = each.value
  policy     = data.aws_iam_policy_document.allowpushpull.json

      depends_on = [
        aws_ecr_repository.ecr_repo
    ]
}