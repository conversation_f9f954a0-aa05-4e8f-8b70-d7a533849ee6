resource "aws_ecr_repository" "ecr_repo" {
  for_each = var.new_repos
  name                 = each.value
  image_tag_mutability = "MUTABLE"
  force_delete         = true
  
  image_scanning_configuration {
            scan_on_push = true
  }
}

import {
  for_each = var.repoistories
  to = aws_ecr_repository.this[each.key]
  id = each.value
}

resource "aws_ecr_repository" "this" {
  for_each = var.repoistories
  name = each.value
  force_delete         = true

  image_scanning_configuration {
            scan_on_push = true
  }
}