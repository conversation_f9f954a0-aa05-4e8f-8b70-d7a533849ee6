module "alerta_image_builder_hml" {
  source               = "./modules/alerta_image_builder"
  aws_region           = var.aws_region
  aws_profile          = "paytrack_hml"
  sns_topic_name       = "image-builder-alerts"
  notification_email   = var.notification_email
  lambda_function_name = "image_builder_failure_formatter"

  default_tags = {
    Name        = "image_builder_paytrack_sync_scheduler_prd"
    Owner       = var.client
    Created_By  = var.created_by
    Creator     = var.creator
    Dati-ticket = var.ticket_number
    Terraform   = "true"
  }
}

module "alerta_image_builder_prd" {
  source               = "./modules/alerta_image_builder"
  aws_region           = var.aws_region
  aws_profile          = "paytrack_prd"
  sns_topic_name       = "image-builder-alerts"
  notification_email   = var.notification_email
  lambda_function_name = "image_builder_failure_formatter"

  default_tags = {
    Name        = "image_builder_paytrack_sync_scheduler_prd"
    Owner       = var.client
    Created_By  = var.created_by
    Creator     = var.creator
    Dati-ticket = var.ticket_number
    Terraform   = "true"
  }
}