variable "project_name" {
  description = "Nome do projeto ou serviço para identificar os recursos"
  type        = string
  default     = "default"
}

variable "client" {
  description = "Nome do client"
  type        = string
}

variable "vpc_id" {
  description = "ID do VPC"
  type        = string
}

variable "subnet_id" {
  description = "ID da subnet para o EC2 Image Builder"
  type        = string
}

variable "vpc_id_hml" {
  description = "ID do VPC"
  type        = string
}

variable "subnet_id_hml" {
  description = "ID da subnet para o EC2 Image Builder"
  type        = string
}

variable "kms_key_id" {
  description = "ID da chave KMS para encriptar o EBS volume, se fornecido"
  type        = string
  default     = null
}

variable "aws_region" {
  description = "AWS region where the infrastructure will be provisioned."
  type        = string
}

# Environment Creator Information
variable "created_by" {
  description = "Name of the person who created the environment."
  type        = string
}

variable "creator" {
  description = "Creator's name."
  type        = string
}

# Ticket Information
variable "ticket_number" {
  description = "Number of the ticket related to the creation of the environment."
  type        = string
}

variable "notification_email" {
  description = "Email address for notification."
  type        = list(string)
}