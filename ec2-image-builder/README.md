# Paytrack IAC Terraform Documentation

## Overview

This repository contains Terraform configurations for managing AWS EC2 Image Builder infrastructure for Paytrack. EC2 Image Builder is a fully managed AWS service that simplifies the automation of creating, managing, and deploying customized, secure, and up-to-date server images.

## Project Structure

```bash
paytrack-iac-terraform/
.
├── alerta_image_builder.tf
├── main.tf
├── modules
│   ├── alerta_image_builder
│   ├── ec2-image-builder
│   ├── ec2-image-builder-14
│   └── ec2-image-builder-17
├── README.md
├── terraform.tfvars
└── variables.tf
```

## AWS Profile Configuration

Before using this infrastructure, ensure your AWS credentials are properly configured with the following profiles:

- `paytrack_prd`
- `paytrack_hml`

## Prerequisites

- AWS Account
- Terraform installed (recommended version: latest)
- Appropriate AWS credentials configured

## Usage

### Initial Setup

1. Initialize Terraform:

   ```bash
   terraform init
   ```

2. Review planned changes:

   ```bash
   terraform plan -var-file=terraform.tfvars
   ```

3. Apply changes:

   ```bash
   terraform apply -var-file=terraform.tfvars
   ```

### Alerts

In case of a pipeline failure, an email notification will be sent to the designated recipients. `<EMAIL>`

## Module Documentation

The following modules are available:

- [`/modules/alerta_image_builder/README.md`](./modules/alerta_image_builder/README.md)
- [`/modules/ec2-image-builder/README.md`](./modules/ec2-image-builder/README.md)
- [`/modules/ec2-image-builder-14/README.md`](./modules/ec2-image-builder-14/README.md)
- [`/modules/ec2-image-builder-17/README.md`](./modules/ec2-image-builder-17/README.md)

For detailed configuration and usage, refer to each module's `README.md`.

## Infrastructure Components

The project manages EC2 Image Builder resources, which include:

- Image pipeline configurations
- Image recipes
- Infrastructure configuration
- Component configurations
- Distribution settings
