variable "project_name" {
  description = "Nome do projeto ou serviço para identificar os recursos"
  type        = string
  default     = "default"
}

variable "vpc_id" {
  description = "ID do VPC"
  type        = string
}

variable "subnet_id" {
  description = "ID da subnet para o EC2 Image Builder"
  type        = string
}

variable "kms_key_id" {
  description = "ID da chave KMS para encriptar o EBS volume, se fornecido"
  type        = string
  default     = null
}

variable "tags" {
  description = "Tags a serem aplicadas aos recursos"
  type        = map(string)
  default     = {}
}

variable "default_tags" {
  description = "Tags padrão a serem aplicadas a todos os recursos"
  type        = map(string)
  default = {
    Terraform   = "true"
    Environment = "dev"
  }
}

variable "aws_region" {
  description = "AWS region where the infrastructure will be provisioned."
  type        = string
}

variable "launch_template_ids" {
  description = "Lista de ARNs de Launch Templates a serem atualizados com a nova AMI"
  type        = list(string)
  default     = []
}

variable "volume_size" {
  description = "volume size"
  type = number
  default = "20"
}

variable "license_configuration_arns" {
  type    = list(string)
  default = null
}

variable "aws_profile" {
  description = "AWS profile name"
  type        = string
  default     = null
}

variable "image_builder_schedule" {
  description = "Define se a pipeline será executada manualmente ou via cron. Use um formato cron válido ou 'manual'."
  type        = string
  default     = "manual"
}

variable "image_scanning_enabled" {
  description = "(Optional) Whether image scans are enabled. Defaults to false."
  type = bool
  default = false
}

variable "ami" {
  type        = string
  default     = ""
  description = "AMI ID a ser utilizada para a criação da imagem. Se não for especificado, será usada a AMI padrão 'ami-0ecaf74d8c068eb49'."
}