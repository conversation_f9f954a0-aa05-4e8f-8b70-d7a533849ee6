## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.3.0 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | >= 5.0.0 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | >= 5.0.0 |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [aws_iam_instance_profile.image_builder_profile](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_instance_profile) | resource |
| [aws_iam_role.image_builder_role](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role) | resource |
| [aws_iam_role_policy_attachment.image_builder_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.ssm_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_imagebuilder_component.install_java](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/imagebuilder_component) | resource |
| [aws_imagebuilder_distribution_configuration.dist_config](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/imagebuilder_distribution_configuration) | resource |
| [aws_imagebuilder_image_pipeline.java_image_pipeline](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/imagebuilder_image_pipeline) | resource |
| [aws_imagebuilder_image_recipe.java_image_recipe](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/imagebuilder_image_recipe) | resource |
| [aws_imagebuilder_infrastructure_configuration.infra_config](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/imagebuilder_infrastructure_configuration) | resource |
| [aws_security_group.image_builder_sg](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group) | resource |
| [aws_ami.amazon_linux_2023](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/ami) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_ami"></a> [ami](#input\_ami) | AMI ID a ser utilizada para a criação da imagem. Se não for especificado, será usada a AMI padrão 'ami-0ecaf74d8c068eb49'. | `string` | `""` | no |
| <a name="input_aws_profile"></a> [aws\_profile](#input\_aws\_profile) | AWS profile name | `string` | `null` | no |
| <a name="input_aws_region"></a> [aws\_region](#input\_aws\_region) | AWS region where the infrastructure will be provisioned. | `string` | n/a | yes |
| <a name="input_default_tags"></a> [default\_tags](#input\_default\_tags) | Tags padrão a serem aplicadas a todos os recursos | `map(string)` | <pre>{<br>  "Environment": "dev",<br>  "Terraform": "true"<br>}</pre> | no |
| <a name="input_image_builder_schedule"></a> [image\_builder\_schedule](#input\_image\_builder\_schedule) | Define se a pipeline será executada manualmente ou via cron. Use um formato cron válido ou 'manual'. | `string` | `"manual"` | no |
| <a name="input_image_scanning_enabled"></a> [image\_scanning\_enabled](#input\_image\_scanning\_enabled) | (Optional) Whether image scans are enabled. Defaults to false. | `bool` | `false` | no |
| <a name="input_kms_key_id"></a> [kms\_key\_id](#input\_kms\_key\_id) | ID da chave KMS para encriptar o EBS volume, se fornecido | `string` | `null` | no |
| <a name="input_launch_template_ids"></a> [launch\_template\_ids](#input\_launch\_template\_ids) | Lista de ARNs de Launch Templates a serem atualizados com a nova AMI | `list(string)` | `[]` | no |
| <a name="input_license_configuration_arns"></a> [license\_configuration\_arns](#input\_license\_configuration\_arns) | n/a | `list(string)` | `null` | no |
| <a name="input_project_name"></a> [project\_name](#input\_project\_name) | Nome do projeto ou serviço para identificar os recursos | `string` | `"default"` | no |
| <a name="input_subnet_id"></a> [subnet\_id](#input\_subnet\_id) | ID da subnet para o EC2 Image Builder | `string` | n/a | yes |
| <a name="input_tags"></a> [tags](#input\_tags) | Tags a serem aplicadas aos recursos | `map(string)` | `{}` | no |
| <a name="input_volume_size"></a> [volume\_size](#input\_volume\_size) | volume size | `number` | `"20"` | no |
| <a name="input_vpc_id"></a> [vpc\_id](#input\_vpc\_id) | ID do VPC | `string` | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_component_arn"></a> [component\_arn](#output\_component\_arn) | ARN do componente |
| <a name="output_image_pipeline_arn"></a> [image\_pipeline\_arn](#output\_image\_pipeline\_arn) | ARN da pipeline do Image Builder |
| <a name="output_image_recipe_arn"></a> [image\_recipe\_arn](#output\_image\_recipe\_arn) | ARN da receita da imagem |
| <a name="output_infrastructure_config_arn"></a> [infrastructure\_config\_arn](#output\_infrastructure\_config\_arn) | ARN da configuração da infraestrutura |
