variable "aws_region" {
  description = "Região AWS"
  type        = string
}

variable "aws_profile" {
  description = "Perfil AWS CLI para a conta"
  type        = string
}

variable "sns_topic_name" {
  description = "Nome do tópico SNS"
  type        = string
}

variable "notification_email" {
  description = "E-mail para receber notificações"
  type    = list(string)
}

variable "lambda_function_name" {
  description = "Nome da função Lambda"
  type        = string
}

variable "default_tags" {
  description = "Tags padrão para todos os recursos"
  type        = map(string)
  default     = {}
}