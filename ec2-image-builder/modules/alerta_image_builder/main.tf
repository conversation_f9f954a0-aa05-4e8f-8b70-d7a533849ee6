provider "aws" {
  region  = var.aws_region
  profile = var.aws_profile
  default_tags {
    tags = var.default_tags
  }
}

resource "aws_sns_topic" "image_builder_failure_topic" {
  name = var.sns_topic_name
}

resource "aws_sns_topic_subscription" "email_subscriptions" {
  for_each = toset(var.notification_email)  # Converte a lista em um conjunto para iterar

  topic_arn = aws_sns_topic.image_builder_failure_topic.arn
  protocol  = "email"
  endpoint  = each.value  # Cada email na lista
}




resource "aws_cloudwatch_event_rule" "image_builder_failure_rule" {
  name        = "image-builder-failure"
  description = "Dispara quando uma pipeline do EC2 Image Builder falha"

  event_pattern = <<EOF
{
  "source": ["aws.imagebuilder"],
  "detail-type": ["EC2 Image Builder Image State Change"],
  "detail": {
    "state": {
      "status": ["FAILED"]
    }
  }
}
EOF
}

resource "aws_iam_role" "lambda_role" {
  name = "lambda_image_builder_alerts"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
EOF
}

resource "aws_iam_policy" "lambda_sns_policy" {
  name        = "lambda_sns_publish_policy"
  description = "Permite que a Lambda publique mensagens no SNS"

  policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": "sns:Publish",
      "Resource": "${aws_sns_topic.image_builder_failure_topic.arn}"
    }
  ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "lambda_sns_attach" {
  role       = aws_iam_role.lambda_role.name
  policy_arn = aws_iam_policy.lambda_sns_policy.arn
}

resource "aws_iam_role_policy_attachment" "lambda_basic_execution" {
  role       = aws_iam_role.lambda_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}

data "archive_file" "lambda_zip" {
  type        = "zip"
  output_path = "lambda_function_payload.zip"

  source {
    content  = <<EOF
import json
import boto3
import os

sns_client = boto3.client("sns")
SNS_TOPIC_ARN = os.environ["SNS_TOPIC_ARN"]

def lambda_handler(event, context):
    detail = event.get("detail", {})
    resources = event.get("resources", [])

    # Definir valores padrão
    pipeline_name = "Desconhecido"
    image_arn = "Desconhecido"

    # Identificar a imagem corretamente
    if resources:
        image_arn = resources[0]  # Assume que a primeira ARN é da imagem

        # Extraindo corretamente o nome da pipeline do ARN da imagem
        image_parts = image_arn.split("/")  # Divide a ARN em partes
        if len(image_parts) >= 3:
            pipeline_name = image_parts[-3]  # Pegamos o nome antes da versão

    # Capturar estado da execução
    state = detail.get("state", {}).get("status", "Desconhecido")
    failure_reason = detail.get("failureMessage", "Não especificado")
    event_time = event.get("time", "Desconhecido")

    # Criar a mensagem formatada
    message = f"""
    🚨 [ALERTA] EC2 Image Builder Pipeline Falhou 🚨

    🔹 Pipeline: {pipeline_name}
    🔹 Imagem ARN: {image_arn}
    🔹 Estado: {state}
    🔹 Hora: {event_time}

    ❌ Erro: {failure_reason}

    🔍 Verifique o console do AWS Image Builder:
    https://console.aws.amazon.com/imagebuilder/home
    """

    # Enviar alerta para o SNS
    sns_client.publish(
        TopicArn=SNS_TOPIC_ARN,
        Message=message,
        Subject=f"[ALERTA] Falha na Pipeline {pipeline_name} do EC2 Image Builder"
    )

    return {"statusCode": 200, "body": json.dumps("Mensagem enviada!")}
EOF
    filename = "lambda_function.py"
  }
}

resource "aws_lambda_function" "sns_formatter" {
  function_name = var.lambda_function_name
  runtime       = "python3.13"
  handler       = "lambda_function.lambda_handler"
  role          = aws_iam_role.lambda_role.arn
  architectures = ["arm64"]

  filename         = data.archive_file.lambda_zip.output_path
  source_code_hash = data.archive_file.lambda_zip.output_base64sha256

  environment {
    variables = {
      SNS_TOPIC_ARN = aws_sns_topic.image_builder_failure_topic.arn
    }
  }
}

resource "aws_cloudwatch_event_target" "image_builder_failure_target" {
  rule      = aws_cloudwatch_event_rule.image_builder_failure_rule.name
  target_id = "send-to-lambda"
  arn       = aws_lambda_function.sns_formatter.arn
}

resource "aws_lambda_permission" "allow_cloudwatch" {
  statement_id  = "AllowExecutionFromCloudWatch"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.sns_formatter.function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.image_builder_failure_rule.arn
}
