provider "aws" {
  region = var.aws_region
  profile = var.aws_profile
}

data "aws_ami" "amazon_linux_2023" {
  owners      = ["amazon"]
  most_recent = true
  filter {
    name   = "name"
    values = ["al2023-ami-2023*"]
  }
  filter {
    name   = "architecture"
    values = ["x86_64"]
  }
  filter {
    name   = "state"
    values = ["available"]
  }
}

resource "aws_security_group" "image_builder_sg" {
  name        = "${var.project_name}_image_builder_sg"
  description = "Security group for EC2 Image Builder for ${var.project_name}"
  vpc_id      = var.vpc_id

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
    tags = merge(var.default_tags, var.tags)
}

resource "aws_iam_role" "image_builder_role" {
  name = "${var.project_name}_image_builder_role"
  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action = "sts:AssumeRole",
        Effect = "Allow",
        Principal = {
          Service = "ec2.amazonaws.com"
        }
      }
    ]
  })
   tags = merge(var.default_tags, var.tags)
}

resource "aws_iam_role_policy_attachment" "image_builder_policy" {
  policy_arn = "arn:aws:iam::aws:policy/EC2InstanceProfileForImageBuilder"
  role       = aws_iam_role.image_builder_role.name
}

resource "aws_iam_role_policy_attachment" "ssm_policy" {
  policy_arn = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
  role       = aws_iam_role.image_builder_role.name
}


resource "aws_iam_instance_profile" "image_builder_profile" {
  name = "${var.project_name}_image_builder_profile"
  role = aws_iam_role.image_builder_role.name
  tags = merge(var.default_tags, var.tags)
}

resource "aws_imagebuilder_infrastructure_configuration" "infra_config" {
  name                  = "${var.project_name}_java_image_infra"
  instance_profile_name = aws_iam_instance_profile.image_builder_profile.name
  instance_types        = ["t3.small"]
  subnet_id             = var.subnet_id
  security_group_ids    = [aws_security_group.image_builder_sg.id]

  terminate_instance_on_failure = true
    tags = merge(var.default_tags, var.tags)
}


resource "aws_imagebuilder_component" "install_java" {
  name     = "${var.project_name}_install_java_17"
  platform = "Linux"
  version  = "1.0.0"
  data     = <<EOF
name: Install Java 17
description: Installs Java 17 using Amazon Linux package manager.
schemaVersion: 1.0

phases:
  - name: build
    steps:
      - name: InstallJava
        action: ExecuteBash
        inputs:
          commands:
            - sudo yum update -y
            - sudo dnf check-update
            - sudo yum install java-17-amazon-corretto-headless amazon-cloudwatch-agent logrotate -y
            - sudo timedatectl set-timezone America/Sao_Paulo
            - sudo systemctl enable amazon-cloudwatch-agent
            - sudo systemctl start amazon-cloudwatch-agent
EOF
     tags = merge(var.default_tags, var.tags)
}

resource "aws_imagebuilder_image_recipe" "java_image_recipe" {
  name         = "${var.project_name}_java_17_amazonlinux_2023"
  parent_image = data.aws_ami.amazon_linux_2023.id
  version      = "1.0.0"

  block_device_mapping {
    device_name = "/dev/xvda"
    ebs {
      delete_on_termination = true
      volume_size           = var.volume_size
      volume_type           = "gp3"
      encrypted             = var.kms_key_id != null ? true : false
      kms_key_id            = var.kms_key_id
    }
  }

  component {
    component_arn = aws_imagebuilder_component.install_java.arn
  }
    tags = merge(var.default_tags, var.tags)
  lifecycle {
    ignore_changes = [parent_image]
  }
}

resource "aws_imagebuilder_distribution_configuration" "dist_config" {
  name        = "${var.project_name}_dist_config"
  description = "Distribution configuration for updating launch templates with new AMI for ${var.project_name}"

  distribution {
    region = var.aws_region

    ami_distribution_configuration {
    
    }

   dynamic "launch_template_configuration" {
      for_each = var.launch_template_ids
      content {
        launch_template_id = launch_template_configuration.value
      }
    }
  }
  tags = merge(var.default_tags, var.tags)
  
  lifecycle {
    ignore_changes = [distribution]
  }
}

resource "aws_imagebuilder_image_pipeline" "java_image_pipeline" {
  name                             = "${var.project_name}_java_17_amazonlinux_2023_pipeline"
  image_recipe_arn                 = aws_imagebuilder_image_recipe.java_image_recipe.arn
  infrastructure_configuration_arn  = aws_imagebuilder_infrastructure_configuration.infra_config.arn
  distribution_configuration_arn    = aws_imagebuilder_distribution_configuration.dist_config.arn

  dynamic "image_scanning_configuration" {
    for_each = var.image_scanning_enabled ? [1] : []
    content {
      image_scanning_enabled = var.image_scanning_enabled
    }
  }

  dynamic "schedule" {
    for_each = var.image_builder_schedule != "manual" ? [1] : []
    content {
      schedule_expression = "cron(${var.image_builder_schedule})"
      pipeline_execution_start_condition = "EXPRESSION_MATCH_ONLY"
    }
  }

  image_tests_configuration {
    image_tests_enabled = var.image_scanning_enabled
  }

  tags = merge(var.default_tags, var.tags)

}