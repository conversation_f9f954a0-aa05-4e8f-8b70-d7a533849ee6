output "image_pipeline_arn" {
  value       = aws_imagebuilder_image_pipeline.java_image_pipeline.arn
  description = "ARN da pipeline do Image Builder"
}

output "image_recipe_arn" {
  value       = aws_imagebuilder_image_recipe.java_image_recipe.arn
  description = "ARN da receita da imagem"
}

output "infrastructure_config_arn" {
  value       = aws_imagebuilder_infrastructure_configuration.infra_config.arn
  description = "ARN da configuração da infraestrutura"
}

output "component_arn" {
  value       = aws_imagebuilder_component.install_java.arn
  description = "ARN do componente"
}