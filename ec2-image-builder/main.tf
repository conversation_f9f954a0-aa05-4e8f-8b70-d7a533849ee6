provider "aws" {
  region = var.aws_region
  default_tags {
    tags = {
      Owner       = var.client
      Created_By  = var.created_by
      Creator     = var.creator
      Dati-ticket = var.ticket_number
    }
  }
}

terraform {
  backend "s3" {
    bucket  = "terraform-iac-v2"
    key     = "paytrack/ec2-image-builder/terraform.tfstate"
    region  = "us-east-1"
    profile = "paytrack_prd"
  }
}

module "paytrack_tradutor_hml_image_builder" {
  source      = "./modules/ec2-image-builder-17"
  aws_profile = "paytrack_prd"

  aws_region             = var.aws_region
  project_name           = "paytrack_tradutor_hml"
  vpc_id                 = var.vpc_id
  subnet_id              = var.subnet_id
  launch_template_ids    = ["lt-0153d83898c34c02e", "lt-0a420f10aca7dcb77", "lt-068d376932979b1ff"]
  volume_size            = 8
  image_builder_schedule = "0 12 ? * TUE *"

  default_tags = {
    Name        = "image_builder_paytrack_tradutor_hml"
    Owner       = var.client
    Created_By  = var.created_by
    Creator     = var.creator
    Dati-ticket = var.ticket_number
    Terraform   = "true"
  }
}

module "paytrack_tradutor_prd_image_builder" {
  source      = "./modules/ec2-image-builder-17"
  aws_profile = "paytrack_prd"

  aws_region             = var.aws_region
  project_name           = "paytrack_tradutor_prd"
  vpc_id                 = var.vpc_id
  subnet_id              = var.subnet_id
  launch_template_ids    = ["lt-0f2f2291fa4f3c0a1"]
  image_scanning_enabled = true
  volume_size            = 8

  default_tags = {
    Name        = "image_builder_paytrack_tradutor_prd"
    Owner       = var.client
    Created_By  = var.created_by
    Creator     = var.creator
    Dati-ticket = var.ticket_number
    Terraform   = "true"
  }
}

module "paytrack_agencias_dev_image_builder" {
  source      = "./modules/ec2-image-builder-17"
  aws_profile = "paytrack_prd"

  aws_region   = var.aws_region
  project_name = "paytrack_agencias_dev"
  vpc_id       = var.vpc_id
  subnet_id    = var.subnet_id
  launch_template_ids = [
    "lt-078b9a1c1b53d1bb9",
    "lt-09028d1ce92484841",
    "lt-0934a47d98ceac10e",
    "lt-0298f51bc51743fa4",
    "lt-0be1789777b305369",
    "lt-0777ae561c973596a",
    "lt-0392947ecd0efd081",
    "lt-059d68f279c7c8e33",
    "lt-0414f829f7713615c"
  ]
  volume_size            = 8
  image_builder_schedule = "0 12 ? * TUE *"

  default_tags = {
    Name        = "image_builder_paytrack_agencias_dev"
    Owner       = var.client
    Created_By  = var.created_by
    Creator     = var.creator
    Dati-ticket = var.ticket_number
    Terraform   = "true"
  }
}

module "paytrack_agencias_hml_image_builder" {
  source      = "./modules/ec2-image-builder-17"
  aws_profile = "paytrack_prd"

  aws_region             = var.aws_region
  project_name           = "paytrack_agencias_hml"
  vpc_id                 = var.vpc_id
  subnet_id              = var.subnet_id
  launch_template_ids    = ["lt-0c3e97ed1941040b2"]
  volume_size            = 8
  image_builder_schedule = "0 12 ? * TUE *"

  default_tags = {
    Name        = "image_builder_paytrack_agencias_hml"
    Owner       = var.client
    Created_By  = var.created_by
    Creator     = var.creator
    Dati-ticket = var.ticket_number
    Terraform   = "true"
  }
}

module "paytrack_agencias_prd_image_builder" {
  source      = "./modules/ec2-image-builder-17"
  aws_profile = "paytrack_prd"

  aws_region             = var.aws_region
  project_name           = "paytrack_agencias_prd"
  vpc_id                 = var.vpc_id
  subnet_id              = var.subnet_id
  launch_template_ids    = ["lt-007e792302f81d1ac"]
  volume_size            = 8
  image_scanning_enabled = true
  image_builder_schedule = "0 12 ? * TUE *"

  default_tags = {
    Name        = "image_builder_paytrack_agencias_prd"
    Owner       = var.client
    Created_By  = var.created_by
    Creator     = var.creator
    Dati-ticket = var.ticket_number
    Terraform   = "true"
  }
}

module "paytrack_sync_hml_image_builder" {
  source      = "./modules/ec2-image-builder-17"
  aws_profile = "paytrack_hml"

  aws_region             = var.aws_region
  project_name           = "paytrack_sync_hml"
  vpc_id                 = var.vpc_id_hml
  subnet_id              = var.subnet_id_hml
  launch_template_ids    = ["lt-0dc25afa26da87913", "lt-0242e1875106ab838"]
  volume_size            = 10
  image_builder_schedule = "0 12 ? * TUE *"

  default_tags = {
    Name        = "image_builder_paytrack_sync_hml"
    Owner       = var.client
    Created_By  = var.created_by
    Creator     = var.creator
    Dati-ticket = var.ticket_number
    Terraform   = "true"
  }
}

module "paytrack_sync_scheduler_hml_image_builder" {
  source      = "./modules/ec2-image-builder"
  aws_profile = "paytrack_hml"

  aws_region             = var.aws_region
  project_name           = "paytrack_sync_scheduler_hml"
  vpc_id                 = var.vpc_id_hml
  subnet_id              = var.subnet_id_hml
  launch_template_ids    = ["lt-099d889eca731d7f7"]
  volume_size            = 8
  image_builder_schedule = "0 12 ? * TUE *"

  default_tags = {
    Name        = "image_builder_paytrack_sync_scheduler_hml"
    Owner       = var.client
    Created_By  = var.created_by
    Creator     = var.creator
    Dati-ticket = var.ticket_number
    Terraform   = "true"
  }
}

module "paytrack_sync_executor_prd_image_builder" {
  source      = "./modules/ec2-image-builder-17"
  aws_profile = "paytrack_prd"

  aws_region   = var.aws_region
  project_name = "paytrack_sync_executor_prd"
  vpc_id       = var.vpc_id
  subnet_id    = var.subnet_id

  launch_template_ids    = ["lt-0597d2c09a3f29f5d"]
  volume_size            = 150
  image_scanning_enabled = true
  image_builder_schedule = "0 12 ? * TUE *"

  default_tags = {
    Name        = "image_builder_paytrack_sync_executor_prd"
    Owner       = var.client
    Created_By  = var.created_by
    Creator     = var.creator
    Dati-ticket = var.ticket_number
    Terraform   = "true"
  }
}

module "paytrack_sync_management_prd_image_builder" {
  source      = "./modules/ec2-image-builder-17"
  aws_profile = "paytrack_prd"

  aws_region             = var.aws_region
  project_name           = "paytrack_sync_management_prd"
  vpc_id                 = var.vpc_id
  subnet_id              = var.subnet_id
  launch_template_ids    = ["lt-0174e64939c428348"]
  volume_size            = 15
  image_scanning_enabled = true
  image_builder_schedule = "0 12 ? * TUE *"

  default_tags = {
    Name        = "image_builder_paytrack_sync_management_prd"
    Owner       = var.client
    Created_By  = var.created_by
    Creator     = var.creator
    Dati-ticket = var.ticket_number
    Terraform   = "true"
  }
}

module "paytrack_sync_scheduler_prd_image_builder" {
  source      = "./modules/ec2-image-builder"
  aws_profile = "paytrack_prd"

  aws_region             = var.aws_region
  project_name           = "paytrack_sync_scheduler_prd"
  vpc_id                 = var.vpc_id
  subnet_id              = var.subnet_id
  launch_template_ids    = ["lt-0ea4224f345bd223d"]
  volume_size            = 8
  image_scanning_enabled = true
  image_builder_schedule = "0 12 ? * TUE *"

  default_tags = {
    Name        = "image_builder_paytrack_sync_scheduler_prd"
    Owner       = var.client
    Created_By  = var.created_by
    Creator     = var.creator
    Dati-ticket = var.ticket_number
    Terraform   = "true"
  }
}

module "paytrack_web_hml_image_builder" {
  source      = "./modules/ec2-image-builder"
  aws_profile = "paytrack_prd"

  ami                    = "ami-015ceed9e4d21cd24"
  aws_region             = var.aws_region
  project_name           = "paytrack_web_hml"
  vpc_id                 = var.vpc_id
  subnet_id              = var.subnet_id
  launch_template_ids    = ["lt-0f380c961f50f8585", "lt-0ab6e54bf248a7897"]
  volume_size            = 8
  image_scanning_enabled = false
  image_builder_schedule = "0 12 ? * TUE *"

  default_tags = {
    Name        = "image_builder_paytrack_web_hml"
    Owner       = var.client
    Created_By  = var.created_by
    Creator     = var.creator
    Dati-ticket = var.ticket_number
    Terraform   = "true"
  }
}

module "paytrack_web_prd_image_builder" {
  source      = "./modules/ec2-image-builder"
  aws_profile = "paytrack_prd"

  ami                    = "ami-015ceed9e4d21cd24"
  aws_region             = var.aws_region
  project_name           = "paytrack_web_prd"
  vpc_id                 = var.vpc_id
  subnet_id              = var.subnet_id
  launch_template_ids    = ["lt-04550bd7c7f29bf77"]
  volume_size            = 50
  image_scanning_enabled = true
  image_builder_schedule = "0 12 ? * TUE *"

  default_tags = {
    Name        = "image_builder_paytrack_web_prd"
    Owner       = var.client
    Created_By  = var.created_by
    Creator     = var.creator
    Dati-ticket = var.ticket_number
    Terraform   = "true"
  }
}

module "paytrack_relatorios_hml_image_builder" {
  source      = "./modules/ec2-image-builder-17"
  aws_profile = "paytrack_prd"

  aws_region             = var.aws_region
  project_name           = "paytrack_relatorios_hml"
  vpc_id                 = var.vpc_id
  subnet_id              = var.subnet_id
  launch_template_ids    = ["lt-0f72643e138d0a085", "lt-0edcbaaead40c1c45"]
  volume_size            = 8
  image_scanning_enabled = false
  image_builder_schedule = "0 12 ? * TUE *"

  default_tags = {
    Name        = "image_builder_paytrack_relatorios_hml"
    Owner       = var.client
    Created_By  = var.created_by
    Creator     = var.creator
    Dati-ticket = var.ticket_number
    Terraform   = "true"
  }
}

module "paytrack_relatorios_prd_image_builder" {
  source      = "./modules/ec2-image-builder-17"
  aws_profile = "paytrack_prd"

  aws_region             = var.aws_region
  project_name           = "paytrack_relatorios_prd"
  vpc_id                 = var.vpc_id
  subnet_id              = var.subnet_id
  launch_template_ids    = ["lt-076d9498e8974204c"]
  volume_size            = 15
  image_scanning_enabled = true
  image_builder_schedule = "0 12 ? * TUE *"

  default_tags = {
    Name        = "image_builder_paytrack_relatorios_prd"
    Owner       = var.client
    Created_By  = var.created_by
    Creator     = var.creator
    Dati-ticket = var.ticket_number
    Terraform   = "true"
  }
}
