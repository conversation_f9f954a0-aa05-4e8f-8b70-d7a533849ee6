# Must have bucket versioning enabled
resource "aws_s3_bucket_replication_configuration" "replication" {
  provider  = aws.prod
  role      = aws_iam_role.replication.arn
  bucket    = aws_s3_bucket.viajor-app-vault.id

  rule {
      id = "drp-replication"
      status = "Enabled"
      filter {
        prefix = ""
      }
      destination {
        bucket        = aws_s3_bucket.drp.arn
        account       = "************"
        storage_class = "STANDARD"
        replication_time {
          status = "Enabled"
            time {
              minutes = 15
            }
        }
        metrics {
          event_threshold {
            minutes = 15
          }
          status = "Enabled"
        }
      }
      source_selection_criteria {
        replica_modifications {
          status = "Disabled" #Não quero sincronizar o destino com a origem
        }
      }
      delete_marker_replication {
        status = "Enabled"
      }
  }

  depends_on    = [
              aws_iam_role.replication,
              aws_s3_bucket_versioning.drp-versioning
  ]
}

#Comando para checar status da replicação de determinado objeto:
#aws s3api head-object --bucket viajor-test-vault-drp --key file-name-here