#bucket de destino da replicação
resource "aws_s3_bucket" "drp" {
  provider      = aws.drp
  bucket        = "viajor-app-vault-drp"

  lifecycle {
    ignore_changes = [
      grant
    ]
  }

  tags = {
    env        = "drp"
    managed-by = "terraform"
  }
}

resource "aws_s3_bucket_ownership_controls" "drp" {
  provider      = aws.drp
  bucket        = aws_s3_bucket.drp.id

  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

#Both buckets must have versioning enabled
resource "aws_s3_bucket_versioning" "drp-versioning" {
  provider      = aws.drp
  bucket        = aws_s3_bucket.drp.id
  versioning_configuration {
    status = "Enabled"
  }
}

#Destination Bucket policy
resource "aws_s3_bucket_policy" "allow-replication-from-source" {
  provider    = aws.drp
  bucket      = aws_s3_bucket.drp.id
  policy      = data.aws_iam_policy_document.allow_access_from_another_account.json
}

data "aws_iam_policy_document" "allow_access_from_another_account" {
  statement {   
    sid     = "replication"
    effect  = "Allow"
    actions = [
        "s3:ReplicateDelete",
        "s3:ReplicateObject",
        "s3:ObjectOwnerOverrideToBucketOwner",
        "s3:GetBucketVersioning",
        "s3:PutBucketVersioning",
        "s3:PutObject",
        "s3:PutObjectAcl",
        "s3:PutObjectVersionAcl",
        "s3:PutObjectTagging",
        "s3:PutObjectVersionTagging",
        "s3:GetBucketObjectLockConfiguration",
        "s3:PutObjectRetention",
        "s3:BypassGovernanceRetention"
    ]
    principals {
      type        = "AWS"
      identifiers = [aws_iam_role.replication.arn]
    }
    resources = [
        "${aws_s3_bucket.drp.arn}/*",
        aws_s3_bucket.drp.arn,
    ]
  }
}

#Inventory policy#Bucket policy
resource "aws_s3_bucket_policy" "allow-inventory" {
  provider    = aws.prod
  bucket      = aws_s3_bucket.inventory.id
  policy      = data.aws_iam_policy_document.allow-inventory.json
}

data "aws_iam_policy_document" "allow-inventory" {
  statement {   
    sid     = "inventory"
    effect  = "Allow"
    actions = [
        "s3:PutObject",
        "s3:GetObject",
        "s3:GetObjectVersion"
    ]
    principals {
      type        = "Service"
      identifiers = ["s3.amazonaws.com"]
    }
    resources = [
        "${aws_s3_bucket.inventory.arn}/*",
        aws_s3_bucket.inventory.arn,
    ]
    condition {
      test     = "ArnLike"
      variable = "aws:SourceArn"
      values = [aws_s3_bucket.viajor-app-vault.arn]
    }
     condition {
      test     = "StringEquals"
      variable = "aws:SourceAccount"
      values = ["************"]
    }
     condition {
      test     = "StringEquals"
      variable = "s3:x-amz-acl"
      values = ["bucket-owner-full-control"]
    }
  }
}
#https://docs.aws.amazon.com/AmazonS3/latest/userguide/example-bucket-policies.html#example-bucket-policies-s3-inventory-1