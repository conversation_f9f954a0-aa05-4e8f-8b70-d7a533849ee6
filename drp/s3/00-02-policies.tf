#https://repost.aws/knowledge-center/s3-cross-account-replication-object-lock
data "aws_iam_policy_document" "assume_role" {
  statement {
    effect = "Allow"

    principals {
      type        = "Service"
      identifiers = ["s3.amazonaws.com","batchoperations.s3.amazonaws.com"]
    }
    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::************:role/aws-reserved/sso.amazonaws.com/AWSReservedSSO_AWSAdministratorAccess_aaaf7cef3957f90a"]
    }

    actions = ["sts:AssumeRole"]
  }
}

resource "aws_iam_role" "replication" {
  provider           = aws.prod
  name               = "ReplicationDRPRole"
  assume_role_policy = data.aws_iam_policy_document.assume_role.json
  depends_on = [
    aws_iam_policy.replication
  ]
}

data "aws_iam_policy_document" "replication" {
  statement {
    effect = "Allow"

    actions = [
        "s3:GetObjectRetention",
        "s3:GetObjectVersionTagging",
        "s3:GetObjectVersionAcl",
        "s3:GetBucketLocation",
        "s3:GetBucketAcl",
        "s3:GetObjectVersionForReplication",
        "s3:GetObjectLegalHold",
        "s3:GetReplicationConfiguration",
        "s3:PutInventoryConfiguration",
        "s3:GetObject",
        "s3:GetObjectAcl",
        "s3:GetObjectTagging",
        "s3:ListBucket",
        "s3:InitiateReplication"

    ]

    resources = [ 
                  "${aws_s3_bucket.viajor-app-vault.arn}/*",
                  aws_s3_bucket.viajor-app-vault.arn
                  ]
  }

  statement {
    effect = "Allow"

    actions = [
        "s3:ReplicateObject",
        "s3:ObjectOwnerOverrideToBucketOwner",
        "s3:GetObjectVersionTagging",
        "s3:ReplicateTags",
        "s3:ReplicateDelete",
        "s3:PutObject",
        "s3:PutObjectAcl",
        "s3:PutObjectTagging",
        "s3:PutObjectVersionTagging"
    ]

    resources = [
                  "${aws_s3_bucket.drp.arn}/*",
                  aws_s3_bucket.drp.arn,
                  ]
  }

  statement {
    effect = "Allow"

    actions = [
        "s3:*"
    ]

    resources = [
                  "${aws_s3_bucket.inventory.arn}/*",
                  aws_s3_bucket.inventory.arn,
                  ]
  }
}

resource "aws_iam_policy" "replication" {
  provider  = aws.prod
  name      = "ReplicationDRPPolicy"
  policy    = data.aws_iam_policy_document.replication.json
}

resource "aws_iam_role_policy_attachment" "replication" {
  provider   = aws.prod
  role       = aws_iam_role.replication.name
  policy_arn = aws_iam_policy.replication.arn

  depends_on = [
    aws_iam_policy.replication,
    aws_iam_role.replication
  ]
}