resource "aws_s3_bucket" "inventory" {
  provider                  = aws.prod
  bucket                    = "paytrack-inventory"
}


resource "aws_s3_bucket_inventory" "rtc-inventory" {
  provider                  = aws.prod
  bucket                    = aws_s3_bucket.viajor-app-vault.id
  name                      = "ReplicationWeekly"
  enabled                   = "true"
  included_object_versions  = "Current"

  schedule {
    frequency = "Daily"
  }
  optional_fields = ["ReplicationStatus"]

  destination {
    bucket {
      format     = "CSV"
      bucket_arn = aws_s3_bucket.inventory.arn
    }
  }
}