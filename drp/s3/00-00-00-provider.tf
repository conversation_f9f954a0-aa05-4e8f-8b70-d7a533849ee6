terraform {
  backend "s3" {
    bucket  = "terraform-iac-v2"
    key     = "paytrack/drp/terraform.tfstate"
    region  = "us-east-1"
    profile = "main_account"
  }

  required_providers {
    aws = {
      source  = "hashicorp/aws"
    }
  }
}

provider "aws" {
  alias     = "drp"
  region    = "us-west-2"
  profile   = "drp"
}


provider "aws" {
  alias     = "prod"
  region    = "us-east-1"
  profile   = "prod"
}
