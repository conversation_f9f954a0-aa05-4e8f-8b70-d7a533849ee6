data "aws_iam_policy_document" "topic" {
  statement {
    effect = "Allow"

    principals {
      type        = "Service"
      identifiers = ["s3.amazonaws.com"]
    }

    actions   = ["SNS:Publish"]
    resources = [ "arn:aws:sns:*:*:s3-replication-events" ]

    condition {
      test     = "ArnLike"
      variable = "aws:SourceArn"
      values   = [ aws_s3_bucket.viajor-app-vault.arn ]
    }
  }
}
resource "aws_sns_topic" "s3-replication-topic" {
  provider  = aws.prod
  name      = "s3-replication-events"
  policy    = data.aws_iam_policy_document.topic.json
}

resource "aws_s3_bucket_notification" "bucket_notification" {
  provider  = aws.prod
  bucket    = aws_s3_bucket.viajor-app-vault.id
  topic {
    topic_arn     = aws_sns_topic.s3-replication-topic.arn
    events        = ["s3:Replication:OperationFailedReplication"]
  }
}