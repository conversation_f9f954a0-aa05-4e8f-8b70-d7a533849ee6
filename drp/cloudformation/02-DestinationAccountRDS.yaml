AWSTemplateFormatVersion: '2010-09-09'
Transform: 'AWS::Serverless-2016-10-31'
Description: Yaml template for snapshot copy across accounts
Parameters:
#O tópico de erros precisa ser inserido manualmente dentro do step workflow
# General params 
#arn:aws:sns:us-east-1:************:cross-account-rds-CrossAccountSnapshotNotificationSNSTopic
  KMSKey:
    Description: Arn of KMS Key to encrypt your snapshot in destination account. Check Pre-requisite 4.
    Type: String 
    Default: 'arn:aws:kms:us-west-2:************:key/mrk-658ab758fb7645b196042fef00aa9e5b'
Resources:
#SNS Topic for step function failure detection
  NotifyCrossRegionCopy:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: !Sub ${AWS::StackName}-NotifyCrossRegionCopy
#DynamoDB table to store rds-instances
  DrpInstancesTable:
    Type: 'AWS::DynamoDB::Table'
    Properties:
      TableName: 'drp-instances'
      AttributeDefinitions:
        - AttributeName: 'id'
          AttributeType: 'N'
      KeySchema:
        - AttributeName: 'id'
          KeyType: 'HASH'
      ProvisionedThroughput:
        ReadCapacityUnits: 5
        WriteCapacityUnits: 5
# Lambda execution role config   
  LambdaTargetAccountRole: 
    Type: AWS::IAM::Role 
    Properties: 
      RoleName: !Sub ${AWS::StackName}-LambdaTargetAccountRole
      Path: / 
      AssumeRolePolicyDocument:               
          Version: '2012-10-17' 
          Statement: 
          - Sid: 'LambdaSSMAssume' 
            Effect: Allow 
            Principal: 
              Service: 
              - lambda.amazonaws.com   
            Action: sts:AssumeRole

            
  DBReplicationPolicyTargetAccount: 
    Type: AWS::IAM::Policy
    Properties: 
      PolicyName: DBReplicationPolicyTargetAccount 
      PolicyDocument: 
        Statement: 
        - Action: ["logs:DescribeLogStreams", "logs:CreateLogStream", "logs:PutLogEvents", "logs:CreateLogGroup"] 
          Resource: "*"  
          Effect: Allow         
        - Action: ["rds:RestoreDBInstanceFromDBSnapshot","rds:CreateDBInstance","rds:DescribeDBInstances","rds:ModifyDBInstance","rds:DeleteDBInstance","rds:DeleteDBSnapshot","rds:DescribeDBSnapshots","rds:CopyDBSnapshot","rds:AddTagsToResource"]
          Resource: "*" 
          Effect: Allow
        - Action: ["KMS:Decrypt","KMS:DescribeKey","KMS:Encrypt", "KMS:GenerateDataKey","KMS:ReEncryptFrom","KMS:ReEncryptTo","KMS:CreateGrant"]
          Resource: "*" 
          Effect: Allow  
        - Action: ["secretsmanager:GetResourcePolicy","secretsmanager:GetSecretValue","secretsmanager:DescribeSecret","secretsmanager:ListSecretVersionIds","secretsmanager:UpdateSecret"]
          Resource: "*" 
          Effect: Allow 
        - Action: ["tag:GetResources"]
          Resource: "*" 
          Effect: Allow
        - Action: ["dynamodb:BatchGetItem", "dynamodb:DescribeTable", "dynamodb:GetItem", "dynamodb:GetRecords", "dynamodb:Query", "dynamodb:Scan"]
          Resource: "*" 
          Effect: Allow
      Roles: [!Ref LambdaTargetAccountRole]
# Lambda execution role config   
  InvokeStepFunctionLambdaRole: 
    Type: AWS::IAM::Role 
    Properties: 
      RoleName: !Sub ${AWS::StackName}-InvokeStepFunctionLambdaRole
      Path: / 
      AssumeRolePolicyDocument:               
          Version: '2012-10-17' 
          Statement: 
          - Sid: 'LambdaSSMAssume' 
            Effect: Allow 
            Principal: 
              Service: 
              - lambda.amazonaws.com   
            Action: sts:AssumeRole
            
  InvokeStepFunctionLambdaPolicy: 
    Type: AWS::IAM::Policy
    Properties: 
      PolicyName: InvokeStepFunctionLambdaPolicy 
      PolicyDocument: 
        Statement: 
        - Action: ["states:*"] 
          Resource: "*"  
          Effect: Allow 
        - Action: ["logs:DescribeLogStreams", "logs:CreateLogStream", "logs:PutLogEvents", "logs:CreateLogGroup"] 
          Resource: "*"  
          Effect: Allow                     
      Roles: [!Ref InvokeStepFunctionLambdaRole]
    
# Step 1:  InvokeStepFunction        
# This Lambda function will invoke Step Function workflow.
  InvokeStepFunction:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub ${AWS::StackName}-InvokeStepFunction
      Handler: index.lambda_handler
      Runtime: python3.9
      Code:
        ZipFile: |
          import boto3
          import os
          client = boto3.client('stepfunctions')
          stepfunctionArn = os.environ.get("stepfunctionArn")       
          def lambda_handler(event, context):
              try:                  
                  print('FetchCrossAccount Event: {}'.format(event))                  
                  sns_data = event['Records'][0]['Sns']['Message']          
                  print('Input : ', sns_data)
                  
                  response = client.start_execution(
                      stateMachineArn=stepfunctionArn,    
                      input=sns_data ,
                      traceHeader='string'
                      )
                  
                  print("Response >> ",response)                  
              except Exception as e:
                  print("error: {0}".format(e))
      Description: 'This Lambda function will invoke Step Function workflow.'
      MemorySize: 128
      Timeout: 60
      Role: !GetAtt InvokeStepFunctionLambdaRole.Arn 
      Environment: 
        Variables: 
          stepfunctionArn : !Ref TargetAccountTargetRegionRDSRestoreWorkflow 
# Step 2:  Clean old snapshots
  DeleteReplicatedSnapshot:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub ${AWS::StackName}-DeleteReplicatedSnapshot
      Handler: index.lambda_handler
      Runtime: python3.9
      Code:
        ZipFile: |
            import json
            import boto3
            from boto3.dynamodb.conditions import Key, Attr
            import os
            from operator import itemgetter
            rds = boto3.client('rds')
            snapshot_prefix = "drp-"
            kms_key=os.environ.get("kms_key")
            # Get the service resource.
            dynamodb = boto3.resource('dynamodb')
            # Set the table
            table = dynamodb.Table('drp-instances')
            rds = boto3.client('rds')

            def get_instance_list():
                response = table.query(
                        KeyConditionExpression=Key('id').eq(1)
                    )
                    
                items = response['Items']
                # Access each item in the items list
                for item in items:
                    # Access the 'rds-instances' set
                    rds_instances = item.get('rds-instances', set())
                    return(rds_instances)  

            def lambda_handler(event, context):
                for instance in get_instance_list():
                  target_db_snapshot_identifier = 'drp-' + instance + '-replicated'
                  try:
                    print('Deleting Existing Snapshot ',target_db_snapshot_identifier)
                    response = rds.delete_db_snapshot(
                        DBSnapshotIdentifier=target_db_snapshot_identifier
                    )
                    print("Response >> ",response)
                  except Exception as e:
                    print(e)
                    break
                return  {
                        'targetDBSnapshot': {
                            'TargetDBSnapshotIdentifier': target_db_snapshot_identifier,
                            'status':'Available',
                            'accountid': event['Input']['targetDBSnapshot']['accountid']
                          }
                        }
      Description: 'Clean last snapshots'
      MemorySize: 128
      Timeout: 60
      Role: !GetAtt LambdaTargetAccountRole.Arn
# Step 3:  CheckSourceSnapshotStatus        
#To make a copy of the shared cross account snapshot the snapshot status and pass the response to next stage.
  CheckSourceSnapshotStatus:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub ${AWS::StackName}-CheckSourceSnapshotStatus
      Handler: index.lambda_handler
      Runtime: python3.9
      Code:
        ZipFile: |
            import json
            import boto3
            from boto3.dynamodb.conditions import Key, Attr
            import os
            from operator import itemgetter
            rds = boto3.client('rds')
            snapshot_prefix = "drp-"
            kms_key=os.environ.get("kms_key")
            # Get the service resource.
            dynamodb = boto3.resource('dynamodb')
            # Set the table
            table = dynamodb.Table('drp-instances')
            rds = boto3.client('rds')
            
            def get_instance_list():
                response = table.query(
                        KeyConditionExpression=Key('id').eq(1)
                    )
                    
                items = response['Items']
                # Access each item in the items list
                for item in items:
                    # Access the 'rds-instances' set
                    rds_instances = item.get('rds-instances', set())
                    return(rds_instances)  

            def lambda_handler(event, context):
                for db_identifier in get_instance_list():
                    snapshot_identifier = 'drp-' + db_identifier + '-replicated'
                    try:
                        response = rds.describe_db_snapshots(
                            DBSnapshotIdentifier=snapshot_identifier
                        )
                        print("Response >> ",response)
                        if not response['DBSnapshots']:
                            return  {
                                'targetDBSnapshot': {
                                    'targetDBSnapshot': snapshot_identifier,
                                    'status':'NotAvailable',
                                    'accountid': event['Input']['targetDBSnapshot']['accountid']
                                    }
                                }
                            break
                        if len(response['DBSnapshots'])>1:
                            sorted_keys = sorted(response['DBSnapshots'], key=itemgetter('SnapshotCreateTime'), reverse=True)
                            status = sorted_keys[0]['Status']
                        else:
                            status = response['DBSnapshots'][0]['Status']
                        print('status:', status)
                        if status != 'available':
                            return  {
                                'targetDBSnapshot': {
                                    'targetDBSnapshot': snapshot_identifier,
                                    'status':'NotAvailable',
                                    'accountid': event['Input']['targetDBSnapshot']['accountid']
                                    }
                                }
                            break
                    except Exception as e:
                        print("error: {0}".format(e))
                        return {'status':'NotAvailable'}
                        break
                return  {
                    'targetDBSnapshot': {
                        'targetDBSnapshot': snapshot_identifier,
                        'status':'Available',
                        'accountid': event['Input']['targetDBSnapshot']['accountid']
                        }
                    }
      Description: 'To make a copy of the shared cross account snapshot the snapshot status and pass the response to next stage.'
      MemorySize: 128
      Timeout: 60
      Role: !GetAtt LambdaTargetAccountRole.Arn 
# Step 4:  CopySharedSnapshot        
#To make a copy of the shared cross account snapshot 
  CopySharedSnapshot:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub ${AWS::StackName}-CopySharedSnapshot
      Handler: index.lambda_handler
      Runtime: python3.9
      Code:
        ZipFile: |
          import json
          import boto3
          from boto3.dynamodb.conditions import Key, Attr

          import os
          from operator import itemgetter
          rds = boto3.client('rds')
          snapshot_prefix = "drp-"
          kms_key=os.environ.get("kms_key")
          # Get the service resource.
          dynamodb = boto3.resource('dynamodb')
          # Set the table
          table = dynamodb.Table('drp-instances')
          rds = boto3.client('rds')
          def get_instance_list():
              response = table.query(
                      KeyConditionExpression=Key('id').eq(1)
                  )
                  
              items = response['Items']
              # Access each item in the items list
              for item in items:
                  # Access the 'rds-instances' set
                  rds_instances = item.get('rds-instances', set())
                  return(rds_instances)  
                  
          def lambda_handler(event, context):
              for instance in get_instance_list():
                accountid = event['targetDBSnapshot']['accountid']
                sourceDBSnapshotIdentifier = 'arn:aws:rds:us-east-1:' + accountid + ':snapshot:drp-' + instance + '-replicated'
                targetDBSnapshotIdentifier = 'drp-' + instance + '-replicated'
                print("Target: ", targetDBSnapshotIdentifier)
                print("Source: ", sourceDBSnapshotIdentifier)
                try:
                  response = rds.copy_db_snapshot(
                          SourceDBSnapshotIdentifier=sourceDBSnapshotIdentifier,
                          TargetDBSnapshotIdentifier=targetDBSnapshotIdentifier,
                          KmsKeyId=kms_key
                        )
                except Exception as e:
                  print(e)
              return event
      Description: 'To make a copy of the shared cross account snapshot '
      MemorySize: 128
      Timeout: 60
      Role: !GetAtt LambdaTargetAccountRole.Arn 
      Environment: 
        Variables:          
          kms_key: !Ref KMSKey 
# Step 5:  CheckCopiedSnapshotStatus  
# This option fetches the copy snapshot status and passes the response to IsCopySharedRDSSnapshotAvailable method
  CheckCopiedSnapshotStatus:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub ${AWS::StackName}-CheckCopiedSnapshotStatus
      Handler: index.lambda_handler
      Runtime: python3.9
      Code:
        ZipFile: |
          import json
          import boto3
          from boto3.dynamodb.conditions import Key, Attr
          import os
          from operator import itemgetter
          rds = boto3.client('rds')

          kms_key=os.environ.get("kms_key")
          # Get the service resource.
          dynamodb = boto3.resource('dynamodb')
          # Set the table
          table = dynamodb.Table('drp-instances')
          rds = boto3.client('rds')
          def get_instance_list():
              response = table.query(
                      KeyConditionExpression=Key('id').eq(1)
                  )
                  
              items = response['Items']
              # Access each item in the items list
              for item in items:
                  # Access the 'rds-instances' set
                  rds_instances = item.get('rds-instances', set())
                  return(rds_instances) 

          def lambda_handler(event, context):
            for instance in get_instance_list():
              targetDBSnapshotIdentifier = 'drp-' + instance + '-replicated'
              try:
                  print('CheckCopiedSnapshotStatus function Event: {}'.format(event))
                  print('targetdbSnapshotIdentifier::', targetDBSnapshotIdentifier)
                  output =     {
                      'targetDBSnapshot': {
                          'TargetDBSnapshotIdentifier': targetDBSnapshotIdentifier,
                          'status':'NotAvailable',
                          'accountid': event['targetDBSnapshot']['accountid']
                        }
                      }
                  
                  response = rds.describe_db_snapshots(
                      DBSnapshotIdentifier=targetDBSnapshotIdentifier
                  )
                  print("Response >> ",response)
                  if not response['DBSnapshots']:
                      return output
                      
                  if len(response['DBSnapshots'])>1:
                      sorted_keys = sorted(response['DBSnapshots'], key=itemgetter('SnapshotCreateTime'), reverse=True)
                      status = sorted_keys[0]['Status'] 
                  else:
                      status = response['DBSnapshots'][0]['Status'] 
                  if status != 'available':
                      return  {
                          'targetDBSnapshot': {
                              'TargetDBSnapshotIdentifier': targetDBSnapshotIdentifier,
                              'status':'NotAvailable',
                              'accountid': event['targetDBSnapshot']['accountid']
                              }
                          }
              except Exception as e:
                  print("error: {0}".format(e))
                  return  {
                          'targetDBSnapshot': {
                              'TargetDBSnapshotIdentifier': targetDBSnapshotIdentifier,
                              'status':'NotAvailable',
                              'accountid': event['targetDBSnapshot']['accountid']
                              }
                          }
            return  {
                'targetDBSnapshot': {
                    'TargetDBSnapshotIdentifier': targetDBSnapshotIdentifier,
                    'status':'Available'
                    }
                }
      Description: 'This option fetches the copy snapshot status and passes the response to IsRDSCopySnapshotAvailable method'
      MemorySize: 128
      Timeout: 100
      Role: !GetAtt LambdaTargetAccountRole.Arn
  
  StepFunctionLambdaExecutinRole:
    Type: "AWS::IAM::Role"
    Properties:
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service: states.amazonaws.com
            Action: "sts:AssumeRole"
      Policies:
        - PolicyName: AllowSNSandInvokeLambda
          PolicyDocument:
            Statement:
              - Effect: Allow
                Action:
                  - "lambda:InvokeFunction"
                Resource:
                  - "*"
              - Effect: Allow
                Action:
                  - 'sns:Publish'
                Resource: !Ref NotifyCrossRegionCopy
  TargetAccountTargetRegionRDSRestoreWorkflow:
    Type: AWS::StepFunctions::StateMachine
    Properties:
        StateMachineName: !Sub ${AWS::StackName}-CrossAccount-RDSSync-Target_Account
        DefinitionString:
          !Sub
            - |-
                {
                  "StartAt": "CleanOldSnapshots",
                  "States": {
                    "CleanOldSnapshots": {
                      "Type": "Task",
                      "Resource": "${DeleteReplicatedSnapshot.Arn}",
                      "Next": "CopySharedSnapshot",
                      "Catch": [
                        {
                          "ErrorEquals": [
                            "States.ALL"
                          ],
                          "Next": "ErrorPublish"
                        }
                      ]
                    },
                    "ErrorPublish": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::sns:publish",
                      "Parameters": {
                        "TopicArn": "arn:aws:sns:us-east-1:************:DRP-RDS-Snapshots-DRPFailureTopic",
                        "Message.$": "$"
                      },
                     "Next": "Fail"
                   },
                   "Fail": {
                     "Type": "Fail"
                   },
                    "CopySharedSnapshot": {
                      "Type": "Task",
                      "Resource": "${CopySharedSnapshot.Arn}",
                      "Next": "Wait",
                      "Catch": [
                        {
                          "ErrorEquals": [
                            "States.ALL"
                          ],
                          "Next": "ErrorPublish"
                        }
                      ]
                    },
                    "Wait": {
                      "Type": "Wait",
                      "Seconds": 120,
                      "Next": "CheckCopiedSnapshotStatus"
                    },
                    "CheckCopiedSnapshotStatus": {
                      "Type": "Task",
                      "Resource": "${CheckCopiedSnapshotStatus.Arn}",
                      "Next": "Choice",
                      "Catch": [
                        {
                          "ErrorEquals": [
                            "States.ALL"
                          ],
                          "Next": "ErrorPublish"
                        }
                      ]
                    },
                    "Choice": {
                      "Type": "Choice",
                      "Choices": [
                        {
                          "Variable": "$.targetDBSnapshot.status",
                          "StringEquals": "Available",
                          "Next": "NotifyCrossRegionCopy"
                        },
                        {
                          "Variable": "$.targetDBSnapshot.status",
                          "StringEquals": "NotAvailable",
                          "Next": "CopySharedSnapshot"
                        }
                      ]
                    },
                    "NotifyCrossRegionCopy": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::sns:publish",
                      "Parameters": {
                        "TopicArn": "${NotifyCrossRegionCopy}",
                        "Message.$": "$"
                      },
                      "Next": "Success"
                    },
                    "Success": {
                      "Type": "Succeed"
                    }
                  }
                }
            - {CopySharedSnapshotArn: !GetAtt [ CopySharedSnapshot, Arn ]}
        RoleArn: !GetAtt StepFunctionLambdaExecutinRole.Arn

