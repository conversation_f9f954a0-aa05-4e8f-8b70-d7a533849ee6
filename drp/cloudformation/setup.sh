#https://docs.aws.amazon.com/prescriptive-guidance/latest/patterns/automate-the-replication-of-amazon-rds-instances-across-aws-accounts.html
#https://github.com/aws-samples/aws-rds-crossaccount-replication
#Source account command
#Topico de snapshots
aws --profile prod sns add-permission \
--label drp-account-subscribe --aws-account-id ************ \
--topic-arn arn:aws:sns:us-east-1:************:DRP-RDS-Snapshots-CrossAccountSnapshotNotificationSNSTopic \
--action-name Subscribe Publish ListSubscriptionsByTopic
#Topico de erros
aws --profile prod sns add-permission \
--label drp-notifier-account-subscribe --aws-account-id ************ \
--topic-arn arn:aws:sns:us-east-1:************:DRP-RDS-Snapshots-DRPFailureTopic  \
--action-name Subscribe Publish ListSubscriptionsByTopic
#Clean snapshot topic
aws --profile prod sns add-permission \
--label clean-snapshots-subscribe --aws-account-id ************ \
--topic-arn arn:aws:sns:us-east-1:************:DRPRDSCleanSnapshots \
--action-name Subscribe Publish ListSubscriptionsByTopic

#Allow subscription to clean source account snapshots
aws --profile drp --region us-west-2 sns add-permission --label source-account-subscribe \
--aws-account-id ************ \
--topic-arn arn:aws:sns:us-west-2:************:CleanSourceSnapshots \
--action-name Subscribe Publish ListSubscriptionsByTopic

#Rodar após criar stack na conta destino
#Pemite invoke a partir do sns origem para conta destino
aws --profile drp --region us-east-1 lambda add-permission \
--function-name arn:aws:lambda:us-east-1:************:function:copy-snapshot-form-source-InvokeStepFunction \
--statement-id sns_invoke_permission \
--action lambda:InvokeFunction \
--principal sns.amazonaws.com

#Discord allow publish from drp account
aws --profile prod sns add-permission \
--label publish-error-message-from-drp-account --aws-account-id ************ \
--topic-arn arn:aws:sns:us-east-1:************:DRP-RDS-Snapshots-DRPFailureTopic \
--action-name Subscribe Publish ListSubscriptionsByTopic

aws --profile prod --region us-west-2 sns add-permission \
--label publish-error-message-from-drp-account --aws-account-id ************ \
--topic-arn arn:aws:sns:us-west-2:************:DRP-RDS-Snapshots-DRPFailureTopic \
--action-name Subscribe Publish ListSubscriptionsByTopic


#Rodar após criar stack na conta destino
#Incrição no tópico a partir da conta destino dos snapshots
aws --profile drp sns subscribe \
--region us-east-1 \
--topic-arn arn:aws:sns:us-east-1:************:DRP-RDS-Snapshots-CrossAccountSnapshotNotificationSNSTopic  \
--protocol "lambda" \
--notification-endpoint "arn:aws:lambda:us-east-1:************:function:copy-snapshot-form-source-InvokeStepFunction"


#Adiciona permissões para sns e invoke
aws --profile prod sns add-permission \
--label drp-account-subscribe --aws-account-id ************ \
--topic-arn arn:aws:sns:us-east-1:************:DRP-RDS-Snapshots-DRPFailureTopic \
--action-name Subscribe Publish ListSubscriptionsByTopic

aws --profile prod --region us-east-1 lambda add-permission \
--function-name DRP-RDS-Snapshots-DiscordNotify \
--statement-id sns_invoke_permission \
--action lambda:InvokeFunction \
--principal sns.amazonaws.com

#Subscribe to the sns failure topic
aws --profile prod sns subscribe \
--region us-east-1 \
--topic-arn arn:aws:sns:us-east-1:************:DRP-RDS-Snapshots-DRPFailureTopic  \
--protocol "lambda" \
--notification-endpoint "arn:aws:lambda:us-east-1:************:function:DRP-RDS-Snapshots-DiscordNotify"

aws --profile prod sns subscribe \
--region us-west-2 \
--topic-arn arn:aws:sns:us-west-2:************:DRP-RDS-Snapshots-DRPFailureTopic  \
--protocol "lambda" \
--notification-endpoint "arn:aws:lambda:us-east-1:************:function:DRP-RDS-Snapshots-DiscordNotify"

aws --profile drp --region us-east-1 lambda add-permission \
--function-name DRP-RDS-Snapshots-DiscordNotify \
--statement-id sns_invoke_permission \
--action lambda:InvokeFunction \
--principal sns.amazonaws.com

#Subscripbe copy lambda function to copy notify topic
aws --profile drp sns subscribe \
--region us-east-1 \
--topic-arn arn:aws:sns:us-east-1:************:NotifyCrossRegionCopy  \
--protocol "lambda" \
--notification-endpoint "arn:aws:lambda:us-west-2:************:function:DRP-copyDBSnapshot"


#Discord notify topic
arn:aws:sns:us-east-1:************:DRP-RDS-Snapshots-DRPFailureTopic


#Invoke the final step function
aws --profile drp --region us-east-1 lambda add-permission \
--function-name arn:aws:lambda:us-west-2:************:function:copy-to-oregon-InvokeStepFunction \
--statement-id sns_invoke_permission \
--action lambda:InvokeFunction \
--principal sns.amazonaws.com


aws --profile drp sns subscribe \
--region us-east-1 \
--topic-arn arn:aws:sns:us-east-1:************:NotifyCrossRegionCopy  \
--protocol "lambda" \
--notification-endpoint arn:aws:lambda:us-west-2:************:function:copy-to-oregon-InvokeStepFunction



#Função para limpar os snapshots copiados para us-east-1
aws --profile drp sns subscribe --region us-west-2 \
--topic-arn arn:aws:sns:us-west-2:************:CleanSourceSnapshots --protocol "lambda" \
--notification-endpoint "arn:aws:lambda:us-east-1:************:function:CleanSourceSnapshots"

aws --profile drp sns add-permission --region us-west-2 \
--label clean-snapshots-subscribe --aws-account-id ************ \
--topic-arn arn:aws:sns:us-west-2:************:CleanSourceSnapshots \
--action-name Subscribe Publish ListSubscriptionsByTopic

# Put items on dynamo table as the following JSON sample:
{
  "id": {
    "N": "1"
  },
  "rds-instances": {
    "SS": [
      "paytrack-cartao",
      "postgree-db",
      "dbprod2",
      "paytrack-agencia",
      "paytrack-backoffice",
      "paytrack-modulo-agencia",
      "paytrack-prod-instance"
    ]
  }
}