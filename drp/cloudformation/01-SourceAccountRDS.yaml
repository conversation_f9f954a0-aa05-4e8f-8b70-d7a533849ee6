AWSTemplateFormatVersion: '2010-09-09'
Transform: 'AWS::Serverless-2016-10-31'
Description: Yaml template for snapshot copy across accounts
Parameters:
#general params 
# Criar tabela no dynamodb com nome: drp-instances
# id numérico como partition key
# string set como rds-instances
# TO DO: mover isso para YAML
# Essa chave precisa ser copiada para região destino, atentar na hora de criação para permitir isso
  KMSKey:
    Description: Arn of Cross Account KMS Key that is shared with destination account
    Type: String 
    Default: 'arn:aws:kms:us-east-1:************:key/mrk-658ab758fb7645b196042fef00aa9e5b'
  
  DestinationAccountNumber: 
    Description: Destination Account Number 
    Type: String
    Default: ************

  DiscordChannel: 
    Description: Discord webhook
    Type: String
    Default: 'https://discord.com/api/webhooks/1270015856121876521/SraHG0tGDKQZwmCFh6C5xJsOYX43Rzz2nlJgLYOQARfToLLKr4xjVGrdLPvjVu6UONk8'
  
  ScheduleExpression:
    Type: String 
    Description: Schedule expressions using cron
    Default: cron(0 22 * * ? *)
Resources: 
#SNS Topic for step function failure detection
  DRPFailureTopic:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: !Sub ${AWS::StackName}-DRPFailureTopic
#DynamoDB table to store rds-instances
  DrpInstancesTable:
    Type: 'AWS::DynamoDB::Table'
    Properties:
      TableName: 'drp-instances'
      AttributeDefinitions:
        - AttributeName: 'id'
          AttributeType: 'N'
      KeySchema:
        - AttributeName: 'id'
          KeyType: 'HASH'
      ProvisionedThroughput:
        ReadCapacityUnits: 5
        WriteCapacityUnits: 5
#Role that lambda will assume in source account  
  LambdaSourceAccountRole: 
    Type: AWS::IAM::Role 
    Properties: 
      RoleName: !Sub ${AWS::StackName}-LambdaSourceAccountRole
      Path: / 
      AssumeRolePolicyDocument:               
          Version: '2012-10-17' 
          Statement: 
          - Sid: 'LambdaSSMAssume' 
            Effect: Allow 
            Principal: 
              Service: 
              - lambda.amazonaws.com   
            Action: sts:AssumeRole
            
  RDSReplicationPolicySourceAccount: 
    Type: AWS::IAM::Policy
    Properties: 
      PolicyName: !Sub ${AWS::StackName}-RDSReplicationPolicySourceAccount
      PolicyDocument: 
        Statement: 
        - Action: ["logs:DescribeLogStreams", "logs:CreateLogStream", "logs:PutLogEvents", "logs:CreateLogGroup"] 
          Resource: "*"  
          Effect: Allow         
        - Action: ["rds:CreateDBSnapshot","rds:DescribeDBInstances","rds:CopyDBSnapshot","rds:DeleteDBSnapshot","rds:ModifyDBSnapshotAttribute","rds:DescribeDBSnapshots","rds:AddTagsToResource","rds:RemoveTagsFromResource","rds:ListTagsForResource"]
          Resource: "*" 
          Effect: Allow
        - Action: ["KMS:Decrypt","KMS:DescribeKey","KMS:Encrypt","KMS:GenerateDataKey","KMS:ReEncryptFrom","KMS:ReEncryptTo","KMS:CreateGrant"]
          Resource: "*" 
          Effect: Allow
        - Action: ["dynamodb:BatchGetItem", "dynamodb:DescribeTable", "dynamodb:GetItem", "dynamodb:GetRecords", "dynamodb:Query", "dynamodb:Scan"]
          Resource: "*" 
          Effect: Allow
      Roles: [!Ref LambdaSourceAccountRole]

# Step 1:  Snapshot creation in source account. Remember that this function only work with Aurora DB clusters.
  CreateLatestSnapshot:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub ${AWS::StackName}-CreateLatestSnapshot
      Handler: index.lambda_handler
      Runtime: python3.9
      Code:
        ZipFile: | 
          import boto3
          import os
          from boto3.dynamodb.conditions import Key, Attr
          rds = boto3.client('rds')
          # Get the service resource.
          dynamodb = boto3.resource('dynamodb')
          # Set the table
          table = dynamodb.Table('drp-instances')
          def get_instance_list():
              response = table.query(
                      KeyConditionExpression=Key('id').eq(1)
                  )
                  
              items = response['Items']
              # Access each item in the items list
              for item in items:
                  # Access the 'rds-instances' set
                  rds_instances = item.get('rds-instances', set())
                  return(rds_instances) 
          def delete_latest_snapshot(db_identifier):
              print('Removing snapshot of',db_identifier)
              snapshot_identifier =  "drp-" + db_identifier
              print('Deleting existing snapshot...')
              try:
                  response = rds.delete_db_snapshot(
                        DBSnapshotIdentifier=snapshot_identifier
                  )
                  print('response >> ',response)
              except Exception as e:
                  print(e)    
                
          def create_latest_snapshot(db_identifier):
              print('Creating Snapshot for', db_identifier)
              snapshot_identifier = "drp-" + db_identifier
              print(snapshot_identifier)
              try:
                  response = rds.create_db_snapshot(
                      DBSnapshotIdentifier=snapshot_identifier,
                      DBInstanceIdentifier=db_identifier)
                                              
                  print('response >> ',response)
                  print('SourceDBSnapshotArn >> ',response['DBSnapshot']['DBSnapshotArn'])
                  
                  return {
                  'SourceDBSnapshot': {
                      'SourceDBSnapshotArn': response['DBSnapshot']['DBSnapshotArn']
                      }
                  }
                  rds.client.describe_db_snapshot_attributes
              except Exception as e:
                  print(e)
          def lambda_handler(event, context):
              db_list = get_instance_list()
              #To Create a latest snapshot
              for db_identifier in db_list:
                  delete_latest_snapshot(db_identifier)
                  response = create_latest_snapshot(db_identifier)
                  print('final response >>><<<', response)
              return response
      Description: 'To Create a Snapshot in source account'
      MemorySize: 128
      Timeout: 60
      Role: !GetAtt LambdaSourceAccountRole.Arn
      Tags: 
            - 
              Key: "ProjectName"
              Value: "SyncInstanceCrossAccount"  
#Cloud Watch config       
  CreateLatestSnapshotLogGroup:
      Type: "AWS::Logs::LogGroup"
      Properties:
        RetentionInDays: 120
        LogGroupName: !Join ["", ["/aws/lambda/", !Ref CreateLatestSnapshot]]
# Step 2:  CheckSnapshotStatus       
# To check the snapshot status.
  CheckSnapshotStatus:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub ${AWS::StackName}-CheckSnapshotStatus
      Handler: index.lambda_handler
      Runtime: python3.9
      Code:
        ZipFile: |
          import json
          import boto3
          from boto3.dynamodb.conditions import Key, Attr
          import os
          from operator import itemgetter
          rds = boto3.client('rds')
          # Get the service resource.
          dynamodb = boto3.resource('dynamodb')
          # Set the table
          table = dynamodb.Table('drp-instances')
          def get_instance_list():
              response = table.query(
                      KeyConditionExpression=Key('id').eq(1)
                  )
                  
              items = response['Items']
              # Access each item in the items list
              for item in items:
                  # Access the 'rds-instances' set
                  rds_instances = item.get('rds-instances', set())
                  return(rds_instances) 
          def lambda_handler(event, context):
              db_list = get_instance_list()
              for db_identifier in db_list:
                  snapshot_identifier =  "drp-" + db_identifier
                  try:
                      response = rds.describe_db_snapshots(
                          DBSnapshotIdentifier=snapshot_identifier
                      )
                      print("Response >> ",response)
                      if not response['DBSnapshots']:
                          return  {
                              'SourceDBSnapshot': {
                                  'SourceDBSnapshotArn': snapshot_identifier,
                                  'status':'NotAvailable'
                                  }
                              }
                          break
                      if len(response['DBSnapshots'])>1:
                          sorted_keys = sorted(response['DBSnapshots'], key=itemgetter('SnapshotCreateTime'), reverse=True)
                          status = sorted_keys[0]['Status']
                      else:
                          status = response['DBSnapshots'][0]['Status']
                      print('status:', status)
                      if status != 'available':
                          return  {
                              'SourceDBSnapshot': {
                                  'SourceDBSnapshotArn': snapshot_identifier,
                                  'status':'NotAvailable'
                                  }
                              }
                          break
                  except Exception as e:
                      print("error: {0}".format(e))
                      return {'status':'NotAvailable'}
                      break
              return  {
                  'SourceDBSnapshot': {
                      'SourceDBSnapshotArn': snapshot_identifier,
                      'status':'Available'
                      }
                  }
      MemorySize: 128
      Timeout: 100
      Role: !GetAtt LambdaSourceAccountRole.Arn
      Tags: 
            - 
              Key: "ProjectName"
              Value: "SyncInstanceCrossAccount"   
#Cloud Watch config        
  CheckSnapshotStatusLogGroup:
      Type: "AWS::Logs::LogGroup"
      Properties:
        RetentionInDays: 120
        LogGroupName: !Join ["", ["/aws/lambda/", !Ref CheckSnapshotStatus]]
# Step 3:  Encrypt Snapshot           
#To copy snapshot after the encryption with AWS KMS keys.
  CopyEncryptSnapshot:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub ${AWS::StackName}-CopyEncryptSnapshot
      Handler: index.lambda_handler
      Runtime: python3.9
      Code:
        ZipFile: |
          import boto3
          from boto3.dynamodb.conditions import Key, Attr
          import os
          from operator import itemgetter
          rds = boto3.client('rds')
          kms_key=os.environ.get("kms_key")
          # Get the service resource.
          dynamodb = boto3.resource('dynamodb')
          # Set the table
          table = dynamodb.Table('drp-instances')
          def get_instance_list():
              response = table.query(
                      KeyConditionExpression=Key('id').eq(1)
                  )
                  
              items = response['Items']
              # Access each item in the items list
              for item in items:
                  # Access the 'rds-instances' set
                  rds_instances = item.get('rds-instances', set())
                  return(rds_instances)
          def delete_existing_snapshot(TargetSnapshotIdentifier):
              print('Deleting Existing Snapshot')
              try:
                  response = rds.delete_db_snapshot(DBSnapshotIdentifier=TargetSnapshotIdentifier)
                  print("Response >> ",response)
              except Exception as e:
                  print(e)
              
          def copy_latest_snapshot(TargetSnapshotIdentifier, SourceSnapshotIdentifier): 
              print('Copying Latest Snapshot ', TargetSnapshotIdentifier)
              try:
                  response = rds.copy_db_snapshot(SourceDBSnapshotIdentifier=SourceSnapshotIdentifier,
                                              KmsKeyId=kms_key,
                                              TargetDBSnapshotIdentifier=TargetSnapshotIdentifier,
                                              SourceRegion=boto3.session.Session().region_name)
                  print("Response >> ",response)
                  print('TargetDBSnapshotArn >> ',response['DBSnapshot']['DBSnapshotArn'])
                  return {
                    'targetDBSnapshot': {
                        'targetDBSnapshotIdentifier': response['DBSnapshot']['DBSnapshotIdentifier']
                        }
                }
              except Exception as e:
                  print(e)
          def lambda_handler(event, context):
              db_list = get_instance_list()
              #Delete Existing Snapshot
              for db_identifier in db_list:
                  print(db_identifier)
                  target_db_snapshot_identifier = "drp-" + db_identifier + '-replicated' 
                  source_db_snapshot_identifier = "drp-" + db_identifier
                  delete_existing_snapshot(target_db_snapshot_identifier)
                  #Copy latest snapshot as automated snapshots can't be copied to other account
                  response = copy_latest_snapshot(target_db_snapshot_identifier, source_db_snapshot_identifier)
                  print('final response >>><<<', response)
              return response
      Description: 'To Encrypt the Snapshot'
      MemorySize: 128
      Timeout: 100
      Role: !GetAtt LambdaSourceAccountRole.Arn
      Environment: 
        Variables:
          kms_key: !Ref KMSKey
      Tags: 
            - 
              Key: "ProjectName"
              Value: "SyncInstanceCrossAccount" 
# Step 4:  DescribeCopyEncryptSnapshotStatus   
#This option fetches the copy snapshot status and passes the response to IsCopyEncryptSnapshotAvailable method
  DescribeCopyEncryptSnapshotStatus:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub ${AWS::StackName}DescribeCopyEncryptSnapshotStatus
      Handler: index.lambda_handler
      Runtime: python3.9
      Code:
        ZipFile: |
            import json
            import boto3
            from boto3.dynamodb.conditions import Key, Attr
            import os
            from operator import itemgetter
            # Get the service resource.
            dynamodb = boto3.resource('dynamodb')
            # Set the table
            table = dynamodb.Table('drp-instances')
            rds = boto3.client('rds')
            sts = boto3.client('sts')
            account_id = sts.get_caller_identity()["Account"]
            def get_instance_list():
                response = table.query(
                        KeyConditionExpression=Key('id').eq(1)
                    )
                    
                items = response['Items']
                # Access each item in the items list
                for item in items:
                    # Access the 'rds-instances' set
                    rds_instances = item.get('rds-instances', set())
                    return(rds_instances)
            def lambda_handler(event, context):
                db_list = get_instance_list()
                for db_identifier in db_list:
                    snapshot_identifier = "drp-" + db_identifier + '-replicated'
                    output_ok = {
                    'targetDBSnapshot': {
                        'targetDBSnapshot': snapshot_identifier,
                        'status':'Available',
                        'accountid': account_id
                        }
                    }
                    output_fail = {
                        'targetDBSnapshot': {
                            'targetDBSnapshot': snapshot_identifier,
                            'status':'NotAvailable',
                            'accountid': account_id
                            }
                        }
                    try:
                        response = rds.describe_db_snapshots(
                            DBSnapshotIdentifier=snapshot_identifier
                        )
                        print("Response >> ",response)
                        if not response['DBSnapshots']:
                            return output_fail
                            break
                        if len(response['DBSnapshots'])>1:
                            sorted_keys = sorted(response['DBSnapshots'], key=itemgetter('SnapshotCreateTime'), reverse=True)
                            status = sorted_keys[0]['Status']
                        else:
                            status = response['DBSnapshots'][0]['Status']
                        print('status:', status)
                        if status != 'available':
                            return output_fail
                            break
                    except Exception as e:
                        print("error: {0}".format(e))
                        return {'status':'NotAvailable'}
                        break
                return  output_ok
      Description: 'To Fetch the encrypted copied snapshot status'
      MemorySize: 128
      Timeout: 100
      Role: !GetAtt LambdaSourceAccountRole.Arn
      Tags: 
            - 
              Key: "ProjectName"
              Value: "SyncInstanceCrossAccount" 

# Step 5:  RDSShareSnapshot   
#To share the copied snapshot with target AWS account.  
  RDSShareSnapshot:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub ${AWS::StackName}RDSShareSnapshot
      Handler: index.lambda_handler
      Runtime: python3.9
      Code:
        ZipFile: |
            import boto3
            from boto3.dynamodb.conditions import Key, Attr
            import json
            import os
            rds = boto3.client('rds')    
            shared_account = os.environ.get("shared_account")
            # Get the service resource.
            dynamodb = boto3.resource('dynamodb')
            # Set the table
            table = dynamodb.Table('drp-instances')
            def get_instance_list():
                response = table.query(
                        KeyConditionExpression=Key('id').eq(1)
                    )
                    
                items = response['Items']
                # Access each item in the items list
                for item in items:
                    # Access the 'rds-instances' set
                    rds_instances = item.get('rds-instances', set())
                    return(rds_instances)
            def lambda_handler(event, context):
                db_list = get_instance_list()
                for db_identifier in db_list:
                    targetdbSnapshotIdentifier = "drp-" + db_identifier + '-replicated'
                    print('shared_account::', shared_account)
                    response = rds.modify_db_snapshot_attribute(DBSnapshotIdentifier=targetdbSnapshotIdentifier,
                                                                AttributeName="restore",
                                                                ValuesToAdd=[shared_account])
                    print('response::', response)
                return event
      Description: 'To Share the snapshot with target AWS account'
      MemorySize: 128
      Timeout: 100
      Role: !GetAtt LambdaSourceAccountRole.Arn
      Environment: 
        Variables:
          shared_account: !Ref DestinationAccountNumber
      Tags: 
            - 
              Key: "ProjectName"
              Value: "SyncInstanceCrossAccount" 

#SNS Topic for Cross Account Notification
  CrossAccountSnapshotNotificationSNSTopic:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: !Sub ${AWS::StackName}-CrossAccountSnapshotNotificationSNSTopic
  SnapshotSchedulerRole:
    Type: "AWS::IAM::Role"
    Properties:
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - events.amazonaws.com
                - states.amazonaws.com
            Action: "sts:AssumeRole"
      Policies:
        - PolicyName: InvokeCloudWatchEvent
          PolicyDocument:
            Statement:
              - Action: ["states:startexecution"] 
                Resource: "*"  
                Effect: Allow
              - Effect: Allow
                Action:
                  - "events:DescribeRule"
                Resource:
                  - "*"   
  StepFunctionLambdaExecutionRole:
    Type: "AWS::IAM::Role"
    Properties:
      RoleName: !Sub ${AWS::StackName}-StepFunctionLambdaExecutionRole
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service: states.amazonaws.com
            Action: "sts:AssumeRole"
      Policies:
        - PolicyName: InvokeCallbackLambda
          PolicyDocument:
            Statement:
              - Effect: Allow
                Action:
                  - "lambda:InvokeFunction"
                Resource:
                  - "*"
              - Effect: Allow
                Action:
                  - 'sns:Publish'
                Resource: !Ref CrossAccountSnapshotNotificationSNSTopic
        - PolicyName: AllowPublishSNSError
          PolicyDocument:
            Statement:
              - Effect: Allow
                Action:
                  - 'sns:Publish'
                Resource: !Ref DRPFailureTopic
                
  CrossAccountRDSSnapshotWorkflow:
    Type: AWS::StepFunctions::StateMachine
    Properties:
        StateMachineName: !Sub ${AWS::StackName}-Cross_Account_RDSSync-SourceAccount
        DefinitionString:
          !Sub
            - |-
              {
                "StartAt": "CreateLatestSnapshot",
                "States": {
                  "CreateLatestSnapshot": {
                    "Type": "Task",
                    "Resource": "${CreateLatestSnapshot.Arn}",
                    "Next": "CheckSnapshotStatus",
                    "Catch": [
                      {
                        "ErrorEquals": [
                          "States.ALL"
                        ],
                        "Next": "ErrorNotify"
                      }
                    ]
                  },
                  "CheckSnapshotStatus": {
                    "Type": "Task",
                    "Resource": "${CheckSnapshotStatus.Arn}",
                    "Next": "IsSnapshotAvailable",
                    "Catch": [
                      {
                        "ErrorEquals": [
                          "States.ALL"
                        ],
                        "Next": "ErrorNotify"
                      }
                    ]
                  },
                  "ErrorNotify": {
                    "Type": "Task",
                    "Resource": "arn:aws:states:::sns:publish",
                    "Parameters": {
                      "TopicArn": "${DRPFailureTopic}",
                      "Message.$": "$"
                    },
                     "Next": "Fail"
                   },
                   "Fail": {
                     "Type": "Fail"
                   },
                  "IsSnapshotAvailable": {
                    "Type": "Choice",
                    "Choices": [
                      {
                        "Variable": "$.SourceDBSnapshot.status",
                        "StringEquals": "Available",
                        "Next": "CopyEncryptedSnapshot"
                      },
                      {
                        "Variable": "$.SourceDBSnapshot.status",
                        "StringEquals": "NotAvailable",
                        "Next": "WaitXMins"
                      }
                    ]
                  },
                  "WaitXMins": {
                    "Type": "Wait",
                    "Seconds": 240,
                    "Next": "CheckSnapshotStatus"
                  },
                  "CopyEncryptedSnapshot": {
                    "Type": "Task",
                    "Resource": "${CopyEncryptSnapshot.Arn}",
                    "Next": "TargetRDSSnapshotStatus",
                    "Catch": [
                      {
                        "ErrorEquals": [
                          "States.ALL"
                        ],
                        "Next": "ErrorNotify"
                      }
                    ]
                  },
                  "TargetRDSSnapshotStatus": {
                    "Type": "Task",
                    "Resource": "${DescribeCopyEncryptSnapshotStatus.Arn}",
                    "Next": "IsEncryptSnapshotAvailable",
                    "Catch": [
                      {
                        "ErrorEquals": [
                          "States.ALL"
                        ],
                        "Next": "ErrorNotify"
                      }
                    ]
                  },
                  "IsEncryptSnapshotAvailable": {
                    "Type": "Choice",
                    "Choices": [
                      {
                        "Variable": "$.targetDBSnapshot.status",
                        "StringEquals": "Available",
                        "Next": "ShareStatusWithTargetAccount"
                      },
                      {
                        "Variable": "$.targetDBSnapshot.status",
                        "StringEquals": "NotAvailable",
                        "Next": "WaitXMinsForTargetSnapshot"
                      }
                    ]
                  },
                  "WaitXMinsForTargetSnapshot": {
                    "Type": "Wait",
                    "Seconds": 180,
                    "Next": "TargetRDSSnapshotStatus"
                  },
                  "ShareStatusWithTargetAccount": {
                    "Type": "Task",
                    "Resource": "${RDSShareSnapshot.Arn}",
                    "Next": "NotifyToTargetAccount",
                    "Catch": [
                      {
                        "ErrorEquals": [
                          "States.ALL"
                        ],
                        "Next": "ErrorNotify"
                      }
                    ]
                  },
                  "NotifyToTargetAccount": {
                    "Type": "Task",
                    "Resource": "arn:aws:states:::sns:publish",
                    "Parameters": {
                      "Message": {
                        "Input.$": "$"
                      },
                      "TopicArn": "${CrossAccountSnapshotNotificationSNSTopic}"
                    },
                    "End": true
                  }
                }
              }
            - {RDSUpScaleLambdaArn: !GetAtt [ RDSShareSnapshot, Arn ]}
        RoleArn: !GetAtt StepFunctionLambdaExecutionRole.Arn
  CrossAccountRDSSnapshotScheduler:
    Type: AWS::Events::Rule
    Properties:
        Description: To Trigger the Step Function for Snapshot creation
        Name: !Sub ${AWS::StackName}-SourceAccountRDSSnapshotScheduler
        RoleArn: !GetAtt SnapshotSchedulerRole.Arn
        ScheduleExpression: !Ref ScheduleExpression
        State: ENABLED
        Targets: 
            - 
                Arn: !Ref CrossAccountRDSSnapshotWorkflow
                Id: "TargetFunctionV1"
                RoleArn: !GetAtt SnapshotSchedulerRole.Arn

# Lambda function for discord notificaiton
# Read topic message and send notification
  DiscordNotify:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub ${AWS::StackName}-DiscordNotify
      Handler: index.lambda_handler
      Runtime: python3.11
      Code:
        ZipFile: |
                from discord_webhook import DiscordWebhook
                import os
                import json

                webhookurl = os.environ.get("discordChannel")

                def lambda_handler(event, context):
                  records = event.get("Records", [])
                    
                  for record in records:
                    sns = record.get("Sns", {})
                    message = sns.get("Message", "")
                    topic_arn = sns.get("TopicArn", "")
                    json_message = json.loads(message)
                    
                  message = "**Topic: " + topic_arn + "**\n\n" + message 
                  postMessage = DiscordWebhook(url=webhookurl, content=message)
                  postMessage = postMessage.execute()

      Description: 'To Share the snapshot with target AWS account'
      MemorySize: 128
      Timeout: 100
      Environment: 
        Variables:
          discordChannel: !Ref DiscordChannel
      Role: !GetAtt LambdaSourceAccountRole.Arn
      Layers: 
        - "arn:aws:lambda:us-east-1:************:layer:discord_webhook:3"
      Tags: 
            - 
              Key: "ProjectName"
              Value: "SyncInstanceCrossAccount" 

