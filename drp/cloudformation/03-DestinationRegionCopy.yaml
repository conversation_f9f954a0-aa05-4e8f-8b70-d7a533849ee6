AWSTemplateFormatVersion: '2010-09-09'
Transform: 'AWS::Serverless-2016-10-31'
Description: Yaml template for snapshot copy across accounts
Parameters:
#O tópico de erros precisa ser inserido manualmente dentro do step workflow
# General params 
#arn:aws:sns:us-east-1:************:cross-account-rds-CrossAccountSnapshotNotificationSNSTopic
  KMSKey:
    Description: Arn of KMS Key to encrypt your snapshot in destination account. Check Pre-requisite 4.
    Type: String 
    Default: 'arn:aws:kms:us-west-2:************:key/mrk-658ab758fb7645b196042fef00aa9e5b'
Resources:
#SNS Topic for step function failure detection
  NotifyCrossRegionCopy:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: !Sub ${AWS::StackName}-NotifyCrossRegionCopy
#DynamoDB table to store rds-instances
  DrpInstancesTable:
    Type: 'AWS::DynamoDB::Table'
    Properties:
      TableName: 'drp-instances'
      AttributeDefinitions:
        - AttributeName: 'id'
          AttributeType: 'N'
      KeySchema:
        - AttributeName: 'id'
          KeyType: 'HASH'
      ProvisionedThroughput:
        ReadCapacityUnits: 5
        WriteCapacityUnits: 5
# Lambda execution role config   
  LambdaTargetRegionRole: 
    Type: AWS::IAM::Role 
    Properties: 
      RoleName: !Sub ${AWS::StackName}-LambdaTargetRegionRole
      Path: / 
      AssumeRolePolicyDocument:               
          Version: '2012-10-17' 
          Statement: 
          - Sid: 'LambdaSSMAssume' 
            Effect: Allow 
            Principal: 
              Service: 
              - lambda.amazonaws.com   
            Action: sts:AssumeRole

            
  DBReplicationPolicyTargetAccount: 
    Type: AWS::IAM::Policy
    Properties: 
      PolicyName: DBReplicationPolicyTargetAccount 
      PolicyDocument: 
        Statement: 
        - Action: ["logs:DescribeLogStreams", "logs:CreateLogStream", "logs:PutLogEvents", "logs:CreateLogGroup"] 
          Resource: "*"  
          Effect: Allow         
        - Action: ["rds:RestoreDBInstanceFromDBSnapshot","rds:CreateDBInstance","rds:DescribeDBInstances","rds:ModifyDBInstance","rds:DeleteDBInstance","rds:DeleteDBSnapshot","rds:DescribeDBSnapshots","rds:CopyDBSnapshot","rds:AddTagsToResource"]
          Resource: "*" 
          Effect: Allow
        - Action: ["KMS:Decrypt","KMS:DescribeKey","KMS:Encrypt", "KMS:GenerateDataKey","KMS:ReEncryptFrom","KMS:ReEncryptTo","KMS:CreateGrant"]
          Resource: "*" 
          Effect: Allow  
        - Action: ["secretsmanager:GetResourcePolicy","secretsmanager:GetSecretValue","secretsmanager:DescribeSecret","secretsmanager:ListSecretVersionIds","secretsmanager:UpdateSecret"]
          Resource: "*" 
          Effect: Allow 
        - Action: ["tag:GetResources"]
          Resource: "*" 
          Effect: Allow
        - Action: ["dynamodb:BatchGetItem", "dynamodb:DescribeTable", "dynamodb:GetItem", "dynamodb:GetRecords", "dynamodb:Query", "dynamodb:Scan"]
          Resource: "*" 
          Effect: Allow
      Roles: [!Ref LambdaTargetRegionRole]
# Lambda execution role config   
  InvokeStepFunctionLambdaRole: 
    Type: AWS::IAM::Role 
    Properties: 
      RoleName: !Sub ${AWS::StackName}-InvokeStepFunctionLambdaRole
      Path: / 
      AssumeRolePolicyDocument:               
          Version: '2012-10-17' 
          Statement: 
          - Sid: 'LambdaSSMAssume' 
            Effect: Allow 
            Principal: 
              Service: 
              - lambda.amazonaws.com   
            Action: sts:AssumeRole
            
  InvokeStepFunctionLambdaPolicy: 
    Type: AWS::IAM::Policy
    Properties: 
      PolicyName: InvokeStepFunctionLambdaPolicy 
      PolicyDocument: 
        Statement: 
        - Action: ["states:*"] 
          Resource: "*"  
          Effect: Allow 
        - Action: ["logs:DescribeLogStreams", "logs:CreateLogStream", "logs:PutLogEvents", "logs:CreateLogGroup"] 
          Resource: "*"  
          Effect: Allow                     
      Roles: [!Ref InvokeStepFunctionLambdaRole]
    
# Start initial workflow from sns topic trigger
# This Lambda function will invoke Step Function workflow.
  InvokeStepFunction:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub ${AWS::StackName}-InvokeStepFunction
      Handler: index.lambda_handler
      Runtime: python3.11
      Code:
        ZipFile: |
                import boto3
                import os

                client = boto3.client('stepfunctions')
                stepfunctionArn = os.environ.get("stepfunctionArn")

                def lambda_handler(event, context):
                  try:                  
                      response = client.start_execution(
                          stateMachineArn=stepfunctionArn,    
                          traceHeader='string'
                          )
                      
                      print("Response >> ",response)                  
                  except Exception as e:
                      print("error: {0}".format(e))
      Description: 'This Lambda function will invoke Step Function workflow.'
      MemorySize: 128
      Timeout: 60
      Role: !GetAtt InvokeStepFunctionLambdaRole.Arn 
      Environment: 
        Variables: 
          stepfunctionArn : !Ref CopySnapshotToNewRegion

# Step 1:  Copy Snapshot from original region to new region        
  CopyFromSource:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub ${AWS::StackName}-CopyFromSource
      Handler: index.lambda_handler
      Runtime: python3.11
      Code:
        ZipFile: |
              import json
              import boto3
              from boto3.dynamodb.conditions import Key, Attr
              import os
              from operator import itemgetter
              kms_key=os.environ.get("kms_key")

              #Custom Session
              sessionEast = boto3.session.Session(region_name="us-east-1")
              dynamo = sessionEast.resource('dynamodb')
              # Set the table
              table = dynamo.Table('drp-instances')

              rds = boto3.client('rds')

              def get_instance_list():
                  response = table.query(
                          KeyConditionExpression=Key('id').eq(1)
                      )
                      
                  items = response['Items']
                  # Access each item in the items list
                  for item in items:
                      # Access the 'rds-instances' set
                      rds_instances = item.get('rds-instances', set())
                      return(rds_instances) 

              def lambda_handler(event, context):
                  try:
                      for instance in get_instance_list():
                          targetsnapshotname = 'drp-' + instance + '-us-west-2'
                          sourcesnapshotname = 'arn:aws:rds:us-east-1:637423434602:snapshot:drp-' + instance + '-replicated'
                          
                          snapshotDescription = rds.describe_db_snapshots(DBSnapshotIdentifier=targetsnapshotname)
                          sorted_keys = sorted(snapshotDescription['DBSnapshots'], key=itemgetter('SnapshotCreateTime'), reverse=True)
                          status = sorted_keys[0]['Status']
                          if status == 'available':
                              rds.delete_db_snapshot(DBSnapshotIdentifier=targetsnapshotname)

                          response = rds.copy_db_snapshot(
                                  SourceDBSnapshotIdentifier=sourcesnapshotname,
                                  TargetDBSnapshotIdentifier=targetsnapshotname,
                                  KmsKeyId=kms_key
                              )
                  except Exception as e:
                      print(e)
      Description: 'This Lambda function will invoke Step Function workflow.'
      MemorySize: 128
      Timeout: 60
      Role: !GetAtt LambdaTargetRegionRole.Arn 
      Environment: 
        Variables: 
          kms_key : !Ref KMSKey
  
# Step 2:  check status of the copied snapshot
  CheckCopyStatus:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub ${AWS::StackName}-CheckCopyStatus
      Handler: index.lambda_handler
      Runtime: python3.11
      Code:
        ZipFile: |
                import json
                import boto3
                from boto3.dynamodb.conditions import Key, Attr
                import os
                from operator import itemgetter
                kms_key=os.environ.get("kms_key")

                #Custom Session
                sessionEast = boto3.session.Session(region_name="us-east-1")
                dynamo = sessionEast.resource('dynamodb')
                # Set the table
                table = dynamo.Table('drp-instances')

                rds = boto3.client('rds')

                def get_instance_list():
                    response = table.query(
                            KeyConditionExpression=Key('id').eq(1)
                        )
                        
                    items = response['Items']
                    # Access each item in the items list
                    for item in items:
                        # Access the 'rds-instances' set
                        rds_instances = item.get('rds-instances', set())
                        return(rds_instances) 

                def lambda_handler(event, context):
                    try:
                        for instance in get_instance_list():
                            targetsnapshotname = 'drp-' + instance + '-us-west-2'
                            
                            snapshotDescription = rds.describe_db_snapshots(DBSnapshotIdentifier=targetsnapshotname)
                            sorted_keys = sorted(snapshotDescription['DBSnapshots'], key=itemgetter('SnapshotCreateTime'), reverse=True)
                            status = sorted_keys[0]['Status']
                            if status != 'available':
                                return {
                                    "Status": "NotAvailable",
                                    "Snapshot": targetsnapshotname
                                }
                    except Exception as e:
                        print(e)
                    return {
                        "Status": "Available",
                        "Snapshot": targetsnapshotname
                    }
      Description: 'This Lambda function will invoke Step Function workflow.'
      MemorySize: 128
      Timeout: 60
      Role: !GetAtt LambdaTargetRegionRole.Arn 

# Step 3:  remove the original region snaphots
  CleanSourceSnapshots:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub ${AWS::StackName}-CleanSourceSnapshots
      Handler: index.lambda_handler
      Runtime: python3.11
      Code:
        ZipFile: |
              import json
              import boto3
              from boto3.dynamodb.conditions import Key, Attr
              import os
              from operator import itemgetter
              kms_key=os.environ.get("kms_key")

              #Custom Session
              sessionEast = boto3.session.Session(region_name="us-east-1")
              dynamo = sessionEast.resource('dynamodb')
              # Set the table
              table = dynamo.Table('drp-instances')

              rds = boto3.client('rds')

              def get_instance_list():
                  response = table.query(
                          KeyConditionExpression=Key('id').eq(1)
                      )
                      
                  items = response['Items']
                  # Access each item in the items list
                  for item in items:
                      # Access the 'rds-instances' set
                      rds_instances = item.get('rds-instances', set())
                      return(rds_instances) 

              def lambda_handler(event, context):
                  try:
                      for instance in get_instance_list():
                          sourcesnapshotname = 'arn:aws:rds:us-east-1:637423434602:snapshot:drp-' + instance + '-replicated'
                          
                          snapshotDescription = rds.describe_db_snapshots(DBSnapshotIdentifier=sourcesnapshotname)
                          sorted_keys = sorted(snapshotDescription['DBSnapshots'], key=itemgetter('SnapshotCreateTime'), reverse=True)
                          status = sorted_keys[0]['Status']
                          if status == 'available':
                              rds.delete_db_snapshot(DBSnapshotIdentifier=sourcesnapshotname)

                  except Exception as e:
                      print(e)
                  return { "Status": "Deleted" }
      Description: 'This Lambda function will invoke Step Function workflow.'
      MemorySize: 128
      Timeout: 60
      Role: !GetAtt LambdaTargetRegionRole.Arn 


  StepFunctionLambdaExecutinRole:
    Type: "AWS::IAM::Role"
    Properties:
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service: states.amazonaws.com
            Action: "sts:AssumeRole"
      Policies:
        - PolicyName: AllowSNSandInvokeLambda
          PolicyDocument:
            Statement:
              - Effect: Allow
                Action:
                  - "lambda:InvokeFunction"
                Resource:
                  - "*"
              - Effect: Allow
                Action:
                  - 'sns:Publish'
                Resource: !Ref NotifyCrossRegionCopy
  CopySnapshotToNewRegion:
    Type: AWS::StepFunctions::StateMachine
    Properties:
        StateMachineName: !Sub ${AWS::StackName}-CopySnapshotToNewRegion
        RoleArn: !GetAtt StepFunctionLambdaExecutinRole.Arn
        DefinitionString:
          !Sub
            - |-
                {
                  "Comment": "Starts the copy from original source region, after sharing and copying to the destination account",
                  "StartAt": "CopyFromSource",
                  "States": {
                    "CopyFromSource": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::lambda:invoke",
                      "OutputPath": "$.Payload",
                      "Parameters": {
                        "FunctionName": "${CopyFromSource.Arn}"
                      },
                      "Next": "Wait",
                      "Retry": [
                        {
                          "ErrorEquals": [
                            "Lambda.ServiceException",
                            "Lambda.AWSLambdaException",
                            "Lambda.SdkClientException",
                            "Lambda.TooManyRequestsException"
                          ],
                          "IntervalSeconds": 1,
                          "MaxAttempts": 3,
                          "BackoffRate": 2
                        }
                      ],
                      "Catch": [
                        {
                          "ErrorEquals": [
                            "States.ALL"
                          ],
                          "Next": "DiscordNotifyTopic"
                        }
                      ]
                    },
                    "DiscordNotifyTopic": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::sns:publish",
                      "Parameters": {
                        "TopicArn": "arn:aws:sns:us-east-1:************:DRP-RDS-Snapshots-DRPFailureTopic",
                        "Message": {
                          "Error": "Failed to copy snapshot to Oregon region"
                        }
                      },
                      "End": true
                    },
                    "Wait": {
                      "Type": "Wait",
                      "Seconds": 120,
                      "Next": "CheckCopyStatus"
                    },
                    "CheckCopyStatus": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::lambda:invoke",
                      "Parameters": {
                        "FunctionName": "${CheckCopyStatus.Arn}"
                      },
                      "Retry": [
                        {
                          "ErrorEquals": [
                            "Lambda.ServiceException",
                            "Lambda.AWSLambdaException",
                            "Lambda.SdkClientException",
                            "Lambda.TooManyRequestsException"
                          ],
                          "IntervalSeconds": 1,
                          "MaxAttempts": 3,
                          "BackoffRate": 2
                        }
                      ],
                      "Next": "Choice",
                      "Catch": [
                        {
                          "ErrorEquals": [
                            "States.ALL"
                          ],
                          "Next": "DiscordNotifyTopic"
                        }
                      ]
                    },
                    "Choice": {
                      "Type": "Choice",
                      "Choices": [
                        {
                          "Variable": "$.Payload.Status",
                          "StringMatches": "NotAvailable",
                          "Next": "CheckCopyStatus"
                        }
                      ],
                      "Default": "CleanSourceAccount"
                    },
                    "CleanSourceAccount": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::sns:publish",
                      "Parameters": {
                        "Message.$": "$",
                        "TopicArn": "arn:aws:sns:us-east-1:************:DRPRDSCleanSnapshots"
                      },
                      "End": true,
                      "Catch": [
                        {
                          "ErrorEquals": [
                            "States.ALL"
                          ],
                          "Next": "DiscordNotifyTopic"
                        }
                      ]
                    }
                  }
                }
            - {CopyFromSource: !GetAtt [ CopyFromSource, Arn ]}

