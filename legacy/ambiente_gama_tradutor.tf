resource "aws_launch_template" "paytrack_gama_tradutor" {
  description = "java17"

  block_device_mappings {
    device_name = "/dev/xvda"

    ebs {
      delete_on_termination = "true"
      encrypted             = "false"
      iops                  = "0"
      snapshot_id           = "snap-03cb24332efcf16cb"
      volume_size           = "8"
      volume_type           = "gp3"
    }
  }

  default_version         = "3"
  disable_api_termination = "false"
  ebs_optimized           = "false"

  iam_instance_profile {
    name = "CloudWatchCustomMetrics"
  }

  image_id      = "ami-015ceed9e4d21cd24"
  instance_type = "t3.small"
  key_name      = "ZYNA_NV_2"

  monitoring {
    enabled = "false"
  }

  name                   = "paytrack_gama_tradutor"
  user_data              = "IyEvYmluL2Jhc2ggLXhlCnl1bSBpbnN0YWxsIC15IGF3c2xvZ3MKYXdzIHMzIGNwIHMzOi8vcGF5dHJhY2stdHJhZHV0b3IvZ2FtYS9hd3Nsb2dzLmNvbmYgL2V0Yy9hd3Nsb2dzL2F3c2xvZ3MuY29uZgpzeXN0ZW1jdGwgc3RhcnQgYXdzbG9nc2QKYXdzIHMzIGNwIHMzOi8vcGF5dHJhY2stdHJhZHV0b3IvZ2FtYS90cmFkdXRvci5qYXIgL3Vzci9wYXl0cmFjay8KYXdzIHMzIGNwIHMzOi8vcGF5dHJhY2stdHJhZHV0b3IvZ2FtYS9zZXRlbnYuc2ggL3Vzci9wYXl0cmFjay8KYXdzIHMzIGNwIHMzOi8vcGF5dHJhY2stdHJhZHV0b3IvZ2FtYS90cmFkdXRvci5wcm9wZXJ0aWVzIC91c3IvcGF5dHJhY2svCmNobW9kIDc3NyAvdXNyL3BheXRyYWNrL3NldGVudi5zaAovdXNyL3BheXRyYWNrL3NldGVudi5zaA=="
  vpc_security_group_ids = ["sg-01fefa2db55768412"]

  lifecycle {
    ignore_changes = [image_id, block_device_mappings[0].ebs[0].snapshot_id]
  }
}

resource "aws_lb_target_group" "gama-tradutor" {
  deregistration_delay = "300"

  health_check {
    enabled             = "true"
    healthy_threshold   = "2"
    interval            = "15"
    matcher             = "200"
    path                = "/tradutor/actuator/info"
    port                = "traffic-port"
    protocol            = "HTTP"
    timeout             = "5"
    unhealthy_threshold = "2"
  }

  load_balancing_algorithm_type = "round_robin"
  name                          = "gama-tradutor"
  port                          = "8080"
  protocol                      = "HTTP"
  protocol_version              = "HTTP1"
  slow_start                    = "0"

  stickiness {
    cookie_duration = "86400"
    enabled         = "false"
    type            = "lb_cookie"
  }

  target_type = "instance"
  vpc_id      = aws_vpc.main.id
}

resource "aws_autoscaling_group" "paytrack_gama_tradutor" {
  capacity_rebalance        = true
  default_cooldown          = 300
  desired_capacity          = 1
  force_delete              = false
  force_delete_warm_pool    = false
  health_check_grace_period = 120
  health_check_type         = "ELB"
  max_size                  = 2
  metrics_granularity       = "1Minute"
  min_size                  = 1
  name                      = "paytrack_gama_tradutor"
  protect_from_scale_in     = false
  service_linked_role_arn   = "arn:aws:iam::393346304284:role/aws-service-role/autoscaling.amazonaws.com/AWSServiceRoleForAutoScaling"
  target_group_arns = [
    "arn:aws:elasticloadbalancing:us-east-1:393346304284:targetgroup/gama-tradutor/7e01a0962e6d5452",
  ]
  vpc_zone_identifier = [
    "subnet-06e109f65da43dbcb",
    "subnet-0c04dabb847ecf9e7",
  ]
  wait_for_capacity_timeout = "10m"

  mixed_instances_policy {
    instances_distribution {
      on_demand_allocation_strategy            = "prioritized"
      spot_allocation_strategy                 = "price-capacity-optimized"
    }

    launch_template {
      launch_template_specification {
        launch_template_id   = "lt-09226295189916472"
        launch_template_name = "paytrack_gama_tradutor"
        version              = "$Latest"
      }

      override {
        instance_type = "t3.small"
      }

      override {
        instance_type = "t3a.small"
      }

      override {
        instance_type = "t4g.small"
      }

      override {
        instance_type = "t3a.medium"
      }

      override {
        instance_type = "t4g.medium"
      }

      override {
        instance_type = "t3.medium"
      }
    }
  }

  tag {
    key                 = "Name"
    propagate_at_launch = true
    value               = "paytrack_gama_tradutor"
  }
}

resource "aws_lb_listener_rule" "gama_tradutor" {
  action {
    order            = "1"
    target_group_arn = aws_lb_target_group.gama-tradutor.arn
    type             = "forward"
  }

  condition {
    host_header {
      values = ["gama.paytrack.com.br"]
    }
  }

  condition {
    path_pattern {
      values = ["/tradutor/*"]
    }
  }

  listener_arn = aws_lb_listener.fullstack_443.arn
  priority     = "50"
}
