resource "aws_sns_topic" "s3_web_alpha_jar" {
  name = "s3-web-alpha-jar"
  display_name = "s3-web-alpha-jar"
}

resource "aws_sns_topic" "s3_web_zulu_jar" {
  name = "s3-web-zulu-jar"
  display_name = "s3-web-zulu-jar"
  policy = <<EOT
{
  "Version": "2012-10-17",
  "Id": "example-ID",
  "Statement": [
    {
      "Sid": "example-statement-ID",
      "Effect": "Allow",
      "Principal": {
        "AWS": "*"
      },
      "Action": "SNS:Publish",
      "Resource": "arn:aws:sns:us-east-1:393346304284:s3-web-zulu-jar"
    }
  ]
}
EOT
}

resource "aws_sns_topic_subscription" "s3_zulu_relatorios" {
  topic_arn = aws_sns_topic.s3_web_zulu_jar.arn
  protocol  = "lambda"
  endpoint  = aws_lambda_function.terminate_relatorios_zulu.arn
}
resource "aws_sns_topic_subscription" "s3_zulu_web" {
  topic_arn = aws_sns_topic.s3_web_zulu_jar.arn
  protocol  = "lambda"
  endpoint  = aws_lambda_function.terminate_web_zulu.arn
}