resource "aws_route53_record" "zulu" {
  name    = "zulu.paytrack.com.br"
  type    = "A"
  zone_id = "ZOQML0WI6K6IU"

  alias {
    name                   = aws_cloudfront_distribution.zulu.domain_name
    zone_id                = "Z2FDTNDATAQYW2"
    evaluate_target_health = false
  }
}

resource "aws_route53_record" "zulu_login" {
  name    = "login-zulu.paytrack.com.br"
  type    = "A"
  zone_id = "ZOQML0WI6K6IU"

  alias {
    name                   = aws_cloudfront_distribution.zulu_login.domain_name
    zone_id                = "Z2FDTNDATAQYW2"
    evaluate_target_health = false
  }
}

resource "aws_route53_record" "zulu_supplier_api" {
  name    = "supplier-zulu-api.paytrack.com.br"
  type    = "A"
  zone_id = "ZOQML0WI6K6IU"

  alias {
    name                   = "dualstack.fullstack-**********.us-east-1.elb.amazonaws.com"
    zone_id                = "Z35SXDOTRQ7X7K"
    evaluate_target_health = true
  }
}

resource "aws_route53_record" "zulu_supplier" {
  name    = "supplier-zulu.paytrack.com.br"
  type    = "A"
  zone_id = "ZOQML0WI6K6IU"

  alias {
    name                   = "d35xd4xdyum77z.cloudfront.net"
    zone_id                = "Z2FDTNDATAQYW2"
    evaluate_target_health = false
  }
}
