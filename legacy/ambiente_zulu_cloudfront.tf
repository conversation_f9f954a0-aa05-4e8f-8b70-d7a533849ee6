resource "aws_cloudfront_distribution" "zulu" {
  aliases = [
    "zulu.paytrack.com.br",
  ]
  default_root_object = "index.html"
  enabled             = true
  http_version        = "http2"
  is_ipv6_enabled     = true
  price_class         = "PriceClass_All"
  retain_on_delete    = false
  wait_for_deployment = true

  default_cache_behavior {
    allowed_methods = [
      "DELETE",
      "GET",
      "HEAD",
      "OPTIONS",
      "PATCH",
      "POST",
      "PUT",
    ]
    cached_methods = [
      "GET",
      "HEAD",
    ]
    compress               = true
    default_ttl            = 86400
    max_ttl                = 31536000
    smooth_streaming       = false
    target_origin_id       = "fullstack-1074348514.us-east-1.elb.amazonaws.com"
    viewer_protocol_policy = "allow-all"

    forwarded_values {
      headers = [
        "*",
      ]
      query_string = true

      cookies {
        forward = "all"
      }
    }
  }

  ordered_cache_behavior {
    allowed_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    cached_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    compress               = true
    default_ttl            = 86400
    max_ttl                = 31536000
    path_pattern           = "/legacy/*"
    smooth_streaming       = false
    target_origin_id       = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    viewer_protocol_policy = "redirect-to-https"

    forwarded_values {
      headers = [
        "Access-Control-Request-Headers",
        "Access-Control-Request-Method",
        "Origin",
      ]
      query_string = false

      cookies {
        forward = "none"
      }
    }
  }
  ordered_cache_behavior {
    allowed_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    cache_policy_id = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    cached_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    compress               = true
    path_pattern           = "/menu/*"
    smooth_streaming       = false
    target_origin_id       = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    viewer_protocol_policy = "redirect-to-https"
  }
  ordered_cache_behavior {
    allowed_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    cache_policy_id = "4135ea2d-6df8-44a3-9df3-4b5a84be39ad"
    cached_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    compress               = true
    path_pattern           = "/settings/*"
    smooth_streaming       = false
    target_origin_id       = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    viewer_protocol_policy = "redirect-to-https"
  }
  ordered_cache_behavior {
    allowed_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    cache_policy_id = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    cached_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    compress               = true
    default_ttl            = 0
    max_ttl                = 0
    min_ttl                = 0
    path_pattern           = "index.html"
    smooth_streaming       = false
    target_origin_id       = "root-config"
    viewer_protocol_policy = "redirect-to-https"
  }
  ordered_cache_behavior {
    allowed_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    cache_policy_id = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    cached_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    compress               = true
    path_pattern           = "/js/*"
    smooth_streaming       = false
    target_origin_id       = "root-config"
    viewer_protocol_policy = "redirect-to-https"
  }
  ordered_cache_behavior {
    allowed_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    cache_policy_id = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    cached_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    compress               = true
    path_pattern           = "/assets/*"
    smooth_streaming       = false
    target_origin_id       = "root-config"
    viewer_protocol_policy = "redirect-to-https"
  }
  ordered_cache_behavior {
    allowed_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    cache_policy_id = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    cached_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    compress               = true
    path_pattern           = "/banco-bilhetes*"
    smooth_streaming       = false
    target_origin_id       = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    viewer_protocol_policy = "redirect-to-https"
  }
  ordered_cache_behavior {
    allowed_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    cache_policy_id = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    cached_methods = [
      "GET",
      "HEAD",
    ]
    compress               = true
    path_pattern           = "/carteira-digital*"
    smooth_streaming       = false
    target_origin_id       = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    viewer_protocol_policy = "redirect-to-https"
  }
  ordered_cache_behavior {
    allowed_methods        = [
        "GET",
        "HEAD",
        "OPTIONS",
    ]
    cache_policy_id        = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    cached_methods         = [
        "GET",
        "HEAD",
        "OPTIONS",
    ]
    compress               = true
    default_ttl            = 0
    max_ttl                = 0
    min_ttl                = 0
    path_pattern           = "/cartao/*"
    smooth_streaming       = false
    target_origin_id       = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    trusted_key_groups     = []
    trusted_signers        = []
    viewer_protocol_policy = "redirect-to-https"
  }
  ordered_cache_behavior {
    allowed_methods        = [
        "GET",
        "HEAD",
        "OPTIONS",
    ]
    cache_policy_id        = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    cached_methods         = [
        "GET",
        "HEAD",
        "OPTIONS",
    ]
    compress               = true
    default_ttl            = 0
    max_ttl                = 0
    min_ttl                = 0
    path_pattern           = "/solicitacao-travel/*"
    smooth_streaming       = false
    target_origin_id       = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    trusted_key_groups     = []
    trusted_signers        = []
    viewer_protocol_policy = "redirect-to-https"
  }
  ordered_cache_behavior {
    allowed_methods        = [
        "GET",
        "HEAD",
        "OPTIONS",
    ]
    cache_policy_id        = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    cached_methods         = [
        "GET",
        "HEAD",
        "OPTIONS",
    ]
    compress               = true
    default_ttl            = 0
    max_ttl                = 0
    min_ttl                = 0
    path_pattern           = "/configuracoes/*"
    smooth_streaming       = false
    target_origin_id       = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    trusted_key_groups     = []
    trusted_signers        = []
    viewer_protocol_policy = "redirect-to-https"
  }

  origin {
    connection_attempts = 3
    connection_timeout  = 10
    domain_name         = "fullstack-1074348514.us-east-1.elb.amazonaws.com"
    origin_id           = "fullstack-1074348514.us-east-1.elb.amazonaws.com"

    custom_origin_config {
      http_port                = 80
      https_port               = 443
      origin_keepalive_timeout = 5
      origin_protocol_policy   = "https-only"
      origin_read_timeout      = 120
      origin_ssl_protocols = [
        "TLSv1.2",
      ]
    }
  }
  origin {
    connection_attempts = 3
    connection_timeout  = 10
    domain_name         = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    origin_id           = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    origin_path         = "/zulu"

    s3_origin_config {
      origin_access_identity = "origin-access-identity/cloudfront/E29CLMZ21WZNHS"
    }
  }
  origin {
    connection_attempts = 3
    connection_timeout  = 10
    domain_name         = "paytrack-frontend-repository.s3.us-east-1.amazonaws.com"
    origin_id           = "root-config"
    origin_path         = "/zulu/root-config"

    s3_origin_config {
      origin_access_identity = "origin-access-identity/cloudfront/E29CLMZ21WZNHS"
    }
  }

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  viewer_certificate {
    acm_certificate_arn            = "arn:aws:acm:us-east-1:393346304284:certificate/1b93ea80-acab-4e4c-a475-73f76b22bd34"
    cloudfront_default_certificate = false
    minimum_protocol_version       = "TLSv1.2_2021"
    ssl_support_method             = "sni-only"
  }
}

resource "aws_cloudfront_distribution" "zulu_login" {
  aliases = [
    "login-zulu.paytrack.com.br",
  ]
  default_root_object = "index.html"
  enabled             = true
  http_version        = "http2"
  is_ipv6_enabled     = true
  price_class         = "PriceClass_All"
  retain_on_delete    = false

  wait_for_deployment = true

  custom_error_response {
    error_caching_min_ttl = 10
    error_code            = 403
    response_code         = 200
    response_page_path    = "/index.html"
  }
  custom_error_response {
    error_caching_min_ttl = 10
    error_code            = 404
    response_code         = 200
    response_page_path    = "/index.html"
  }

  default_cache_behavior {
    allowed_methods = [
      "DELETE",
      "GET",
      "HEAD",
      "OPTIONS",
      "PATCH",
      "POST",
      "PUT",
    ]
    cached_methods = [
      "GET",
      "HEAD",
    ]
    compress               = true
    default_ttl            = 86400
    max_ttl                = 31536000
    smooth_streaming       = false
    target_origin_id       = "S3-login.paytrack.com.br/zulu"
    viewer_protocol_policy = "redirect-to-https"

    forwarded_values {
      query_string = false

      cookies {
        forward = "none"
      }
    }
  }

  origin {
    connection_attempts = 3
    connection_timeout  = 10
    domain_name         = "login.paytrack.com.br.s3.amazonaws.com"
    origin_id           = "S3-login.paytrack.com.br/zulu"
    origin_path         = "/zulu"
  }

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  viewer_certificate {
    acm_certificate_arn            = "arn:aws:acm:us-east-1:393346304284:certificate/1b93ea80-acab-4e4c-a475-73f76b22bd34"
    cloudfront_default_certificate = false
    minimum_protocol_version       = "TLSv1.2_2019"
    ssl_support_method             = "sni-only"
  }
}

resource "aws_cloudfront_distribution" "zulu_supplier" {
  aliases = [
    "supplier-zulu.paytrack.com.br",
  ]
  default_root_object = "index.html"
  enabled             = true
  http_version        = "http2"
  is_ipv6_enabled     = true
  price_class         = "PriceClass_All"
  retain_on_delete    = false

  wait_for_deployment = true

  default_cache_behavior {
    allowed_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    cache_policy_id = "658327ea-f89d-4fab-a63d-7e88639e58f6"
    cached_methods = [
      "GET",
      "HEAD",
      "OPTIONS",
    ]
    compress               = true
    smooth_streaming       = false
    target_origin_id       = "agencias.paytrack.com.br.s3.us-east-1.amazonaws.com"
    viewer_protocol_policy = "redirect-to-https"
  }

  origin {
    connection_attempts = 3
    connection_timeout  = 10
    domain_name         = "agencias.paytrack.com.br.s3.us-east-1.amazonaws.com"
    origin_id           = "agencias.paytrack.com.br.s3.us-east-1.amazonaws.com"
    origin_path         = "/zulu"
  }

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  viewer_certificate {
    acm_certificate_arn            = "arn:aws:acm:us-east-1:393346304284:certificate/e7707da8-ef86-424a-b641-c6befd4d539c"
    cloudfront_default_certificate = false
    minimum_protocol_version       = "TLSv1.2_2021"
    ssl_support_method             = "sni-only"
  }
}
