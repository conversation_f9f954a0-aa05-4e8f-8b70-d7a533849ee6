resource "aws_launch_template" "paytrack_romeo_web" {
  block_device_mappings {
    device_name = "/dev/xvda"

    ebs {
      delete_on_termination = "true"
      encrypted             = "false"
      iops                  = "0"
      snapshot_id           = "snap-03cb24332efcf16cb"
      volume_size           = "8"
      volume_type           = "gp3"
    }
  }

  update_default_version  = true
  disable_api_termination = "false"
  ebs_optimized           = "false"

  iam_instance_profile {
    arn = "arn:aws:iam::393346304284:instance-profile/EC2_Paytrack_Web"
  }

  image_id      = "ami-015ceed9e4d21cd24"
  instance_type = "t3.medium"
  key_name      = "ZYNA_NV_2"

  monitoring {
    enabled = "false"
  }

  name                   = "paytrack_romeo_web"
  user_data              = "IyEvYmluL2Jhc2ggLXhlCnl1bSBpbnN0YWxsIC15IGF3c2xvZ3MKbWtkaXIgL3Vzci9wYXl0cmFjayAtcAphd3MgczMgY3AgczM6Ly9wYXl0cmFjay13ZWIvcm9tZW8vYXdzbG9ncy5jb25mIC9ldGMvYXdzbG9ncy9hd3Nsb2dzLmNvbmYKc3lzdGVtY3RsIHN0YXJ0IGF3c2xvZ3NkCmF3cyBzMyBjcCBzMzovL3BheXRyYWNrLXdlYi9yb21lby93a2h0bWx0b2ltYWdlIC91c3IvYmluL3draHRtbHRvaW1hZ2UKYXdzIHMzIGNwIHMzOi8vcGF5dHJhY2std2ViL3JvbWVvL3draHRtbHRvcGRmIC91c3IvYmluL3draHRtbHRvcGRmCmNobW9kIDc3NyAvdXNyL2Jpbi93ayoKYXdzIHMzIGNwIHMzOi8vcGF5dHJhY2std2ViL3JvbWVvL2xvY2FsLmNvbmYgL2V0Yy9mb250cy9sb2NhbC5jb25mCmF3cyBzMyBjcCBzMzovL3BheXRyYWNrLXdlYi9yb21lby9ST09ULmphciAvdXNyL3BheXRyYWNrLwphd3MgczMgc3luYyBzMzovL3BheXRyYWNrLXdlYi9yb21lby9yZXBvcnRzIC91c3IvcGF5dHJhY2svcmVwb3J0cwphd3MgczMgY3AgczM6Ly9wYXl0cmFjay13ZWIvcm9tZW8vcGF5dHJhY2sucHJvcGVydGllcyAvdXNyL3BheXRyYWNrLwphd3MgczMgY3AgczM6Ly9wYXl0cmFjay13ZWIvcm9tZW8vc2V0ZW52LnNoIC91c3IvcGF5dHJhY2svCmNobW9kIDc3NyAvdXNyL3BheXRyYWNrL3NldGVudi5zaAovdXNyL3BheXRyYWNrL3NldGVudi5zaA=="
  vpc_security_group_ids = ["sg-01fefa2db55768412"]
}

resource "aws_lb_target_group" "romeo_web" {
  deregistration_delay = "300"

  health_check {
    enabled             = "true"
    healthy_threshold   = "2"
    interval            = "15"
    matcher             = "200"
    path                = "/api/heartbeat"
    port                = "traffic-port"
    protocol            = "HTTP"
    timeout             = "5"
    unhealthy_threshold = "2"
  }

  load_balancing_algorithm_type = "round_robin"
  name                          = "romeo-web"
  port                          = "8080"
  protocol                      = "HTTP"
  protocol_version              = "HTTP1"
  slow_start                    = "0"

  stickiness {
    cookie_duration = "86400"
    enabled         = "false"
    type            = "lb_cookie"
  }

  target_type = "instance"
  vpc_id      = aws_vpc.main.id
}

resource "aws_autoscaling_group" "paytrack_romeo_web" {
  capacity_rebalance        = "true"
  default_cooldown          = "300"
  desired_capacity          = "1"
  health_check_grace_period = "300"
  health_check_type         = "ELB"

  max_instance_lifetime = "0"
  max_size              = "1"
  metrics_granularity   = "1Minute"
  min_size              = "1"

  mixed_instances_policy {
    instances_distribution {
      on_demand_allocation_strategy            = "prioritized"
      on_demand_base_capacity                  = "0"
      on_demand_percentage_above_base_capacity = "0"
      spot_allocation_strategy                 = "price-capacity-optimized"
      spot_instance_pools                      = "0"
    }

    launch_template {
      launch_template_specification {
        launch_template_id   = aws_launch_template.paytrack_romeo_web.id
        launch_template_name = aws_launch_template.paytrack_romeo_web.name
        version              = "$Latest"
      }

      override {
        instance_type = "t3.small"
      }

      override {
        instance_type = "t3a.small"
      }

      override {
        instance_type = "t4g.small"
      }

      override {
        instance_type = "t3.medium"
      }

      override {
        instance_type = "t4g.medium"
      }

      override {
        instance_type = "t3a.medium"
      }
    }
  }

  name                    = "paytrack_romeo_web"
  protect_from_scale_in   = "false"
  service_linked_role_arn = "arn:aws:iam::393346304284:role/aws-service-role/autoscaling.amazonaws.com/AWSServiceRoleForAutoScaling"

  tag {
    key                 = "Name"
    propagate_at_launch = "true"
    value               = "paytrack_romeo_web"
  }

  target_group_arns = [aws_lb_target_group.romeo_web.arn]

  vpc_zone_identifier = [
    "subnet-06e109f65da43dbcb",
  ]

  lifecycle {
    ignore_changes = [desired_capacity, max_size]
  }
}

resource "aws_lb_listener_rule" "romeo_web" {
  action {
    order            = "1"
    target_group_arn = aws_lb_target_group.romeo_web.arn
    type             = "forward"
  }


  condition {
    host_header {
      values = ["romeo.paytrack.com.br"]
    }
  }

  listener_arn = aws_lb_listener.fullstack_443.arn
  priority     = "1003"
}
