resource "aws_sqs_queue" "paytrack_gama" {
  content_based_deduplication       = false
  delay_seconds                     = 0
  fifo_queue                        = false
  kms_data_key_reuse_period_seconds = 300
  max_message_size                  = 262144
  message_retention_seconds         = 345600
  name                              = "paytrack-gama"
  policy = jsonencode(
    {
      Id = "__default_policy_ID"
      Statement = [
        {
          Action = "SQS:*"
          Effect = "Allow"
          Principal = {
            AWS = "arn:aws:iam::393346304284:root"
          }
          Resource = "arn:aws:sqs:us-east-1:393346304284:paytrack-gama"
          Sid      = "__owner_statement"
        },
        {
          Action = "SQS:SendMessage"
          Effect = "Allow"
          Principal = {
            AWS = aws_iam_user.paytrack_sqs.arn
          }
          Resource = "arn:aws:sqs:us-east-1:393346304284:paytrack-gama"
          Sid      = "__sender_statement"
        },
        {
          Action = [
            "SQS:ChangeMessageVisibility",
            "SQS:DeleteMessage",
            "SQS:ReceiveMessage",
          ]
          Effect = "Allow"
          Principal = {
            AWS = aws_iam_user.paytrack_sqs.arn
          }
          Resource = "arn:aws:sqs:us-east-1:393346304284:paytrack-gama"
          Sid      = "__receiver_statement"
        },
      ]
      Version = "2008-10-17"
    }
  )
  receive_wait_time_seconds  = 1
  sqs_managed_sse_enabled    = false
  tags                       = {}
  tags_all                   = {}
  visibility_timeout_seconds = 30
}
