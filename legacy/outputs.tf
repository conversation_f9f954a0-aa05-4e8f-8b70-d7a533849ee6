output "s3_paytrack_updater_jar" {
  description = "Informações para acesso ao jar do paytrack updater no s3"
  value = {
    key        = data.aws_s3_object.paytrack_updater_jar.key
    bucket     = data.aws_s3_object.paytrack_updater_jar.bucket
    version_id = data.aws_s3_object.paytrack_updater_jar.version_id
  }
}

output "s3_paytrack_agency_bucket" {
  description = "Bucket do agency no s3"
  value       = aws_s3_bucket.paytrack_agency.bucket
}

output "s3_paytrack_tradutor_bucket" {
  description = "Bucket do tradutor no s3"
  value       = aws_s3_bucket.paytrack_tradutor.bucket
}

output "s3_paytrack_web_bucket" {
  description = "Bucket do paytrack no s3"
  value       = aws_s3_bucket.paytrack_web.bucket
}

output "aws_vpc_main_id" {
  description = "VPC padrão para os ambientes"
  value       = aws_vpc.main.id
}

output "aws_lb_listener_fullstack_arn" {
  description = "Load balancer padrão para os ambientes"
  value       = aws_lb_listener.fullstack_443.arn
}

output "iam_role_arn_to_ec2_terminate" {
  description = "Role para execução da lambda de ec2 terminate"
  value       = aws_iam_role.ec2-terminate.arn
}

output "iam_user_arn_to_sqs" {
  description = "IAM user para SQS do paytrack"
  value       = aws_iam_user.paytrack_sqs.arn
}
