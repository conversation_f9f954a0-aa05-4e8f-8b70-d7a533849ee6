resource "aws_launch_template" "paytrack_romeo_tradutor" {
  description = "java17"

  block_device_mappings {
    device_name = "/dev/xvda"

    ebs {
      delete_on_termination = "true"
      encrypted             = "false"
      iops                  = "3000"
      snapshot_id           = "snap-03cb24332efcf16cb"
      volume_size           = "8"
      volume_type           = "gp3"
    }
  }

  default_version = 3
  disable_api_termination = "false"
  ebs_optimized           = "false"

  iam_instance_profile {
    name = "CloudWatchCustomMetrics"
  }

  image_id      = "ami-015ceed9e4d21cd24"
  instance_type = "t3.small"
  key_name      = "ZYNA_NV_2"

  monitoring {
    enabled = "false"
  }

  name                   = "paytrack_romeo_tradutor"
  user_data              = "IyEvYmluL2Jhc2ggLXhlCnl1bSBpbnN0YWxsIC15IGF3c2xvZ3MKYXdzIHMzIGNwIHMzOi8vcGF5dHJhY2stdHJhZHV0b3Ivcm9tZW8vYXdzbG9ncy5jb25mIC9ldGMvYXdzbG9ncy9hd3Nsb2dzLmNvbmYKc3lzdGVtY3RsIHN0YXJ0IGF3c2xvZ3NkCmF3cyBzMyBjcCBzMzovL3BheXRyYWNrLXRyYWR1dG9yL3JvbWVvL3RyYWR1dG9yLmphciAvdXNyL3BheXRyYWNrLwphd3MgczMgY3AgczM6Ly9wYXl0cmFjay10cmFkdXRvci9yb21lby9zZXRlbnYuc2ggL3Vzci9wYXl0cmFjay8KYXdzIHMzIGNwIHMzOi8vcGF5dHJhY2stdHJhZHV0b3Ivcm9tZW8vdHJhZHV0b3IucHJvcGVydGllcyAvdXNyL3BheXRyYWNrLwpjaG1vZCA3NzcgL3Vzci9wYXl0cmFjay9zZXRlbnYuc2gKL3Vzci9wYXl0cmFjay9zZXRlbnYuc2g="
  vpc_security_group_ids = ["sg-01fefa2db55768412"]

  lifecycle {
    ignore_changes = [image_id, block_device_mappings[0].ebs[0].snapshot_id]
  }
}

resource "aws_lb_target_group" "romeo-tradutor" {
  deregistration_delay = "300"

  health_check {
    enabled             = "true"
    healthy_threshold   = "2"
    interval            = "15"
    matcher             = "200"
    path                = "/tradutor/actuator/info"
    port                = "traffic-port"
    protocol            = "HTTP"
    timeout             = "5"
    unhealthy_threshold = "2"
  }

  load_balancing_algorithm_type = "round_robin"
  name                          = "romeo-tradutor"
  port                          = "8080"
  protocol                      = "HTTP"
  protocol_version              = "HTTP1"
  slow_start                    = "0"

  stickiness {
    cookie_duration = "86400"
    enabled         = "false"
    type            = "lb_cookie"
  }

  target_type = "instance"
  vpc_id      = aws_vpc.main.id
}

resource "aws_autoscaling_group" "paytrack_romeo_tradutor" {
  capacity_rebalance        = true
  default_cooldown          = 300
  desired_capacity          = 1
  force_delete              = false
  force_delete_warm_pool    = false
  health_check_grace_period = 120
  health_check_type         = "ELB"
  max_size                  = 2
  metrics_granularity       = "1Minute"
  min_size                  = 1
  name                      = "paytrack_romeo_tradutor"
  protect_from_scale_in     = false
  service_linked_role_arn   = "arn:aws:iam::393346304284:role/aws-service-role/autoscaling.amazonaws.com/AWSServiceRoleForAutoScaling"
  target_group_arns         = [aws_lb_target_group.romeo-tradutor.arn]
  vpc_zone_identifier = [
    "subnet-06e109f65da43dbcb",
    "subnet-0c04dabb847ecf9e7",
  ]
  wait_for_capacity_timeout = "10m"

  mixed_instances_policy {
    instances_distribution {
      on_demand_allocation_strategy = "prioritized"
      spot_allocation_strategy      = "price-capacity-optimized"
    }

    launch_template {
      launch_template_specification {
        launch_template_id   = aws_launch_template.paytrack_romeo_tradutor.id
        launch_template_name = "paytrack_romeo_tradutor"
        version              = "$Latest"
      }

      override {
        instance_type = "t3.small"
      }

      override {
        instance_type = "t3a.small"
      }

      override {
        instance_type = "t4g.small"
      }

      override {
        instance_type = "t3a.medium"
      }

      override {
        instance_type = "t3.medium"
      }

      override {
        instance_type = "t4g.medium"
      }
    }
  }

  tag {
    key                 = "Name"
    propagate_at_launch = true
    value               = "paytrack_romeo_tradutor"
  }
}

resource "aws_lb_listener_rule" "romeo_tradutor" {
  action {
    order            = "1"
    target_group_arn = aws_lb_target_group.romeo-tradutor.arn
    type             = "forward"
  }

  condition {
    host_header {
      values = ["romeo.paytrack.com.br"]
    }
  }

  condition {
    path_pattern {
      values = ["/tradutor/*"]
    }
  }

  listener_arn = aws_lb_listener.fullstack_443.arn
  priority     = "1002"
}
