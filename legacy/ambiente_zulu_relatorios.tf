resource "aws_launch_template" "paytrack_zulu_relatorios" {
  description = "java17"
  default_version = 1

  block_device_mappings {
    device_name = "/dev/xvda"

    ebs {
      delete_on_termination = "true"
      encrypted             = "false"
      iops                  = "3000"
      snapshot_id           = "snap-0c9123cdf264ec066"
      volume_size           = "8"
      volume_type           = "gp3"
    }
  }

  disable_api_termination = "false"
  ebs_optimized           = "false"

  iam_instance_profile {
    name = "CloudWatchCustomMetrics"
  }

  image_id      = "ami-03631d94974543f76"
  instance_type = "t3.small"
  key_name      = "ZYNA_NV_2"

  monitoring {
    enabled = "false"
  }

  name                   = "paytrack_zulu_relatorios"
  user_data              = "IyEvYmluL2Jhc2ggLXhlCnJtIC1yZiAvdXNyL3BheXRyYWNrCnl1bSBpbnN0YWxsIC15IGF3c2xvZ3MKYXdzIHMzIGNwIHMzOi8vcGF5dHJhY2stcmVsYXRvcmlvcy96dWx1L2F3c2xvZ3MuY29uZiAvZXRjL2F3c2xvZ3MvYXdzbG9ncy5jb25mCnN5c3RlbWN0bCBzdGFydCBhd3Nsb2dzZAphd3MgczMgc3luYyBzMzovL3BheXRyYWNrLXdlYi96dWx1L3JlcG9ydHMgL3Vzci9wYXl0cmFjay9yZXBvcnRzCmF3cyBzMyBjcCBzMzovL3BheXRyYWNrLXJlbGF0b3Jpb3MvenVsdS9yZWxhdG9yaW9zLmphciAvdXNyL3BheXRyYWNrLwphd3MgczMgY3AgczM6Ly9wYXl0cmFjay1yZWxhdG9yaW9zL3p1bHUvYXBwbGljYXRpb24ucHJvcGVydGllcyAvdXNyL3BheXRyYWNrLwphd3MgczMgY3AgczM6Ly9wYXl0cmFjay1yZWxhdG9yaW9zL3p1bHUvc2V0ZW52LnNoIC91c3IvcGF5dHJhY2svCmNobW9kIDc3NyAvdXNyL3BheXRyYWNrL3NldGVudi5zaApjZCAvdXNyL3BheXRyYWNrCi4vc2V0ZW52LnNo"
  vpc_security_group_ids = ["sg-091f8f6883d287294"]

  lifecycle {
    ignore_changes = [image_id, block_device_mappings[0].ebs[0].snapshot_id]
  }
}

resource "aws_lb_target_group" "zulu-relatorios" {
  deregistration_delay = "300"

  health_check {
    enabled             = "true"
    healthy_threshold   = "2"
    interval            = "15"
    matcher             = "200"
    path                = "/relatorios/actuator/info"
    port                = "traffic-port"
    protocol            = "HTTP"
    timeout             = "5"
    unhealthy_threshold = "2"
  }

  load_balancing_algorithm_type = "round_robin"
  name                          = "zulu-relatorios"
  port                          = "8080"
  protocol                      = "HTTP"
  protocol_version              = "HTTP1"
  slow_start                    = "0"

  stickiness {
    cookie_duration = "86400"
    enabled         = "false"
    type            = "lb_cookie"
  }

  target_type = "instance"
  vpc_id      = aws_vpc.main.id
}

resource "aws_autoscaling_group" "paytrack_zulu_relatorios" {
  capacity_rebalance        = true
  default_cooldown          = 300
  desired_capacity          = 1
  force_delete              = false
  force_delete_warm_pool    = false
  health_check_grace_period = 120
  health_check_type         = "ELB"
  max_size                  = 2
  metrics_granularity       = "1Minute"
  min_size                  = 1
  name                      = "paytrack_zulu_relatorios"
  protect_from_scale_in     = false
  service_linked_role_arn   = "arn:aws:iam::393346304284:role/aws-service-role/autoscaling.amazonaws.com/AWSServiceRoleForAutoScaling"
  target_group_arns         = [aws_lb_target_group.zulu-relatorios.arn]
  vpc_zone_identifier = [
    "subnet-0d1c465a0ff2ec225"
  ]
  wait_for_capacity_timeout = "10m"

  mixed_instances_policy {
    instances_distribution {
      on_demand_allocation_strategy = "prioritized"
      spot_allocation_strategy      = "price-capacity-optimized"
    }

    launch_template {
      launch_template_specification {
        launch_template_id   = aws_launch_template.paytrack_zulu_relatorios.id
        launch_template_name = "paytrack_zulu_relatorios"
        version              = "$Latest"
      }

      override {
        instance_type = "t3.small"
      }

      override {
        instance_type = "t3a.small"
      }

      override {
        instance_type = "t4g.small"
      }

      override {
        instance_type = "t4g.medium"
      }

      override {
        instance_type = "t3.medium"
      }

      override {
        instance_type = "t3a.medium"
      }
    }
  }

  tag {
    key                 = "Name"
    propagate_at_launch = true
    value               = "paytrack_zulu_relatorios"
  }
}

resource "aws_lb_listener_rule" "zulu_relatorios" {
  action {
    order            = "1"
    target_group_arn = aws_lb_target_group.zulu-relatorios.arn
    type             = "forward"
  }

  condition {
    host_header {
      values = ["zulu.paytrack.com.br"]
    }
  }

  condition {
    path_pattern {
      values = ["/relatorios/*"]
    }
  }

  listener_arn = aws_lb_listener.fullstack_443.arn
  priority     = "1020"
}

resource "aws_cloudwatch_log_group" "zulu_relatorios" {
  name = "Relatorios-Zulu"
}