resource "aws_vpc" "main" {
  assign_generated_ipv6_cidr_block = "false"
  cidr_block                       = "**********/16"
  enable_classiclink               = "false"
  enable_classiclink_dns_support   = "false"
  enable_dns_hostnames             = "true"
  enable_dns_support               = "true"
  instance_tenancy                 = "default"

  tags = {
    Name = "VIAJOR_VPC"
  }

  tags_all = {
    Name = "VIAJOR_VPC"
  }
}

resource "aws_subnet" "sync" {
  availability_zone                   = "us-east-1a"
  cidr_block                          = "**********/28"
  private_dns_hostname_type_on_launch = "ip-name"
  vpc_id                              = "vpc-289f564e"
  tags = {
    "Name" = "Sync"
  }
  tags_all = {
    "Name" = "Sync"
  }
}
