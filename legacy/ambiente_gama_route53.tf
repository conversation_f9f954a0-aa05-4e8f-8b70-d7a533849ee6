resource "aws_route53_record" "gama" {
  name    = "gama.paytrack.com.br"
  type    = "A"
  zone_id = "ZOQML0WI6K6IU"

  alias {
    name                   = "d3hehjwfl2fn9d.cloudfront.net"
    zone_id                = "Z2FDTNDATAQYW2"
    evaluate_target_health = false
  }
}

resource "aws_route53_record" "gama_login" {
  name    = "login-gama.paytrack.com.br"
  type    = "A"
  zone_id = "ZOQML0WI6K6IU"

  alias {
    name                   = "d3txfe0k7faxjl.cloudfront.net"
    zone_id                = "Z2FDTNDATAQYW2"
    evaluate_target_health = false
  }
}

resource "aws_route53_record" "gama_supplier_api" {
  name    = "supplier-gama-api.paytrack.com.br"
  type    = "A"
  zone_id = "ZOQML0WI6K6IU"

  alias {
    name                   = "dualstack.fullstack-**********.us-east-1.elb.amazonaws.com"
    zone_id                = "Z35SXDOTRQ7X7K"
    evaluate_target_health = true
  }
}

resource "aws_route53_record" "gama_supplier" {
  name    = "supplier-gama.paytrack.com.br"
  type    = "A"
  zone_id = "ZOQML0WI6K6IU"

  alias {
    name                   = "d25uba4ik1vn6n.cloudfront.net"
    zone_id                = "Z2FDTNDATAQYW2"
    evaluate_target_health = false
  }
}
