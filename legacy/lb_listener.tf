resource "aws_lb_listener" "fullstack_443" {
  certificate_arn = "arn:aws:acm:us-east-1:393346304284:certificate/1b93ea80-acab-4e4c-a475-73f76b22bd34"

  default_action {
    order            = "1"
    target_group_arn = "arn:aws:elasticloadbalancing:us-east-1:393346304284:targetgroup/web/40f74a013351e007"
    type             = "forward"
  }

  load_balancer_arn = "arn:aws:elasticloadbalancing:us-east-1:393346304284:loadbalancer/app/fullstack/39751a44f90a3f77"
  port              = "443"
  protocol          = "HTTPS"
  ssl_policy        = "ELBSecurityPolicy-TLS-1-2-2017-01"
}