resource "aws_s3_bucket_notification" "bucket_notification_paytrack-web" {
  bucket      = "paytrack-web"
  eventbridge = false

  lambda_function {
    events = [
      "s3:ObjectCreated:*",
    ]
    filter_prefix       = "beta/"
    filter_suffix       = ".jar"
    id                  = "1a75d781-6aae-408e-bcec-82230cdd1b21"
    lambda_function_arn = "arn:aws:lambda:us-east-1:393346304284:function:Terminate-Web-Beta"
  }
  lambda_function {
    events = [
      "s3:ObjectCreated:*",
    ]
    filter_prefix       = "gama/"
    filter_suffix       = ".jar"
    id                  = "64e861c9-dd45-4b1c-922e-0a1d1079c7a3"
    lambda_function_arn = aws_lambda_function.terminate_web_gama.arn
  }
  lambda_function {
    events = [
      "s3:ObjectCreated:*",
    ]
    filter_prefix       = "beta"
    filter_suffix       = ".war"
    id                  = "update-beta"
    lambda_function_arn = "arn:aws:lambda:us-east-1:393346304284:function:Terminate-Web-Beta"
  }
  lambda_function {
    events = [
      "s3:ObjectCreated:*",
    ]
    filter_prefix       = "romeo/"
    filter_suffix       = ".jar"
    lambda_function_arn = aws_lambda_function.terminate_web_romeo.arn
  }
  lambda_function {
    events = [
      "s3:ObjectCreated:*",
    ]
    filter_prefix       = "tango/"
    filter_suffix       = ".jar"
    lambda_function_arn = "arn:aws:lambda:us-east-1:393346304284:function:Terminate-Web-tango"
  }
  lambda_function {
    events = [
      "s3:ObjectCreated:*",
    ]
    filter_prefix       = "juliet/"
    filter_suffix       = ".jar"
    lambda_function_arn = "arn:aws:lambda:us-east-1:393346304284:function:Terminate-Web-juliet"
  }
  lambda_function {
    events = [
      "s3:ObjectCreated:*",
    ]
    filter_prefix       = "delta/"
    filter_suffix       = ".jar"
    lambda_function_arn = "arn:aws:lambda:us-east-1:393346304284:function:Terminate-Web-delta"
  }

   lambda_function {
    events = [
      "s3:ObjectCreated:*",
    ]
    filter_prefix       = "xray/"
    filter_suffix       = ".jar"
    lambda_function_arn = "arn:aws:lambda:us-east-1:393346304284:function:Terminate-Web-xray"
  }
  lambda_function {
    events = [
      "s3:ObjectCreated:*",
    ]
    filter_prefix       = "charlie/"
    filter_suffix       = ".jar"
    lambda_function_arn = "arn:aws:lambda:us-east-1:393346304284:function:Terminate-Web-charlie"
  }
  topic {
    events = [
      "s3:ObjectCreated:*",
    ]
    filter_prefix = "hml/"
    filter_suffix = ".jar"
    id            = "56faae72-a0ba-4b18-9bc9-001d47346949"
    topic_arn     = "arn:aws:sns:us-east-1:393346304284:s3-web-hml-jar"
  }
  topic {
    events = [
      "s3:ObjectCreated:*",
    ]
    filter_prefix = "prd/"
    filter_suffix = ".jar"
    id            = "UpdatePrdEvent"
    topic_arn     = "arn:aws:sns:us-east-1:393346304284:s3-web-prd-jar"
  }
  topic {
    events = [
      "s3:ObjectCreated:*",
    ]
    filter_prefix = "alpha/"
    filter_suffix = ".jar"
    id            = "26a955b3-4168-4051-b724-a7a31aaacfbf"
    topic_arn     = "arn:aws:sns:us-east-1:393346304284:s3-web-alpha-jar"
  }
  topic {
    events = [
      "s3:ObjectCreated:*",
    ]
    filter_prefix = "zulu/"
    filter_suffix = ".jar"
    id            = "UpdateZuluEvent"
    topic_arn     = "arn:aws:sns:us-east-1:393346304284:s3-web-zulu-jar"
  }  
}

resource "aws_s3_bucket_notification" "bucket_notification_paytrack-tradutor" {
  bucket      = "paytrack-tradutor"
  eventbridge = false

  lambda_function {
    events = [
      "s3:ObjectCreated:*",
    ]
    filter_prefix       = "hml/"
    filter_suffix       = ".jar"
    id                  = "8dfdec0f-6e5b-4c7c-a870-993c255118d7"
    lambda_function_arn = "arn:aws:lambda:us-east-1:393346304284:function:Terminate-Tradutor-Hml"
  }
  lambda_function {
    events = [
      "s3:ObjectCreated:*",
    ]
    filter_prefix       = "prd/"
    filter_suffix       = ".war"
    id                  = "update"
    lambda_function_arn = "arn:aws:lambda:us-east-1:393346304284:function:Terminate-Tradutor"
  }
  lambda_function {
    events = [
      "s3:ObjectCreated:*",
    ]
    filter_prefix       = "alpha/"
    filter_suffix       = ".jar"
    id                  = "aed096ff-ddde-4f23-885a-93cd4f5d857f"
    lambda_function_arn = "arn:aws:lambda:us-east-1:393346304284:function:Terminate-Tradutor-Alpha"
  }
  lambda_function {
    events = [
      "s3:ObjectCreated:*",
    ]
    filter_prefix       = "prd/"
    filter_suffix       = ".jar"
    id                  = "70715445-9d33-491d-96e0-d83c20c1f4d9"
    lambda_function_arn = "arn:aws:lambda:us-east-1:393346304284:function:Terminate-Tradutor"
  }
  lambda_function {
    events = [
      "s3:ObjectCreated:*",
    ]
    filter_prefix       = "gama/"
    filter_suffix       = ".jar"
    id                  = "cc54808f-9e5c-49d7-aba0-87ed491a12bb"
    lambda_function_arn = "arn:aws:lambda:us-east-1:393346304284:function:Terminate-Tradutor-Gama"
  }
  lambda_function {
    events = [
      "s3:ObjectCreated:*",
    ]
    filter_prefix       = "romeo/"
    filter_suffix       = ".jar"
    id                  = "upadate-tradutor-romeo"
    lambda_function_arn = aws_lambda_function.terminate_tradutor_romeo.arn
  }
  lambda_function {
    events = [
      "s3:ObjectCreated:*",
    ]
    filter_prefix       = "zulu/"
    filter_suffix       = ".jar"
    id                  = "upadate-tradutor-zulu"
    lambda_function_arn = aws_lambda_function.terminate_tradutor_zulu.arn
  }
  lambda_function {
    events = [
      "s3:ObjectCreated:*",
    ]
    filter_prefix       = "tango/"
    filter_suffix       = ".jar"
    id                  = "upadate-tradutor-tango"
    lambda_function_arn = "arn:aws:lambda:us-east-1:393346304284:function:Terminate-Tradutor-tango"
  }
  lambda_function {
    events = [
      "s3:ObjectCreated:*",
    ]
    filter_prefix       = "juliet/"
    filter_suffix       = ".jar"
    id                  = "upadate-tradutor-juliet"
    lambda_function_arn = "arn:aws:lambda:us-east-1:393346304284:function:Terminate-Tradutor-juliet"
  }
  lambda_function {
    events = [
      "s3:ObjectCreated:*",
    ]
    filter_prefix       = "delta/"
    filter_suffix       = ".jar"
    id                  = "upadate-tradutor-delta"
    lambda_function_arn = "arn:aws:lambda:us-east-1:393346304284:function:Terminate-Tradutor-delta"
  }

   lambda_function {
    events = [
      "s3:ObjectCreated:*",
    ]
    filter_prefix       = "xray/"
    filter_suffix       = ".jar"
    id                  = "upadate-tradutor-xray"
    lambda_function_arn = "arn:aws:lambda:us-east-1:393346304284:function:Terminate-Tradutor-xray"
  }
  lambda_function {
    events = [
      "s3:ObjectCreated:*",
    ]
    filter_prefix       = "charlie/"
    filter_suffix       = ".jar"
    id                  = "upadate-tradutor-charlie"
    lambda_function_arn = "arn:aws:lambda:us-east-1:393346304284:function:Terminate-Tradutor-charlie"
  }
}

resource "aws_s3_bucket_notification" "bucket_notification_paytrack-agencias" {
  bucket      = "paytrack-agencias"
  eventbridge = false

  lambda_function {
    events = [
      "s3:ObjectCreated:*",
    ]
    filter_prefix       = "beta/"
    filter_suffix       = ".jar"
    id                  = "cf91507f-7ad8-406c-b0fd-ae7e9a5f5d46"
    lambda_function_arn = "arn:aws:lambda:us-east-1:393346304284:function:Terminate-Supplier-Beta"
  }
  lambda_function {
    events = [
      "s3:ObjectCreated:*",
    ]
    filter_prefix       = "alpha/"
    filter_suffix       = ".jar"
    id                  = "3ffb4411-c75b-43ff-846f-171efa46c9b7"
    lambda_function_arn = "arn:aws:lambda:us-east-1:393346304284:function:Terminate-Supplier-Alpha"
  }
  lambda_function {
    events = [
      "s3:ObjectCreated:*",
    ]
    filter_prefix       = "prd/"
    filter_suffix       = ".jar"
    id                  = "update"
    lambda_function_arn = "arn:aws:lambda:us-east-1:393346304284:function:Terminate-Agencias"
  }
  lambda_function {
    events = [
      "s3:ObjectCreated:*",
    ]
    filter_prefix       = "hml/"
    filter_suffix       = ".jar"
    id                  = "update-hml"
    lambda_function_arn = "arn:aws:lambda:us-east-1:393346304284:function:Terminate-Agencias-Hml"
  }
  lambda_function {
    events = [
      "s3:ObjectCreated:*",
    ]
    filter_prefix       = "gama/"
    filter_suffix       = ".jar"
    id                  = "f6952baf-76c0-4ca0-a0be-67afcf5a7523"
    lambda_function_arn = aws_lambda_function.terminate_supplier_gama.arn
  }
  lambda_function {
    events = [
      "s3:ObjectCreated:*",
    ]
    filter_prefix       = "romeo/"
    filter_suffix       = ".jar"
    lambda_function_arn = aws_lambda_function.terminate_supplier_romeo.arn
  }
  lambda_function {
    events = [
      "s3:ObjectCreated:*",
    ]
    filter_prefix       = "zulu/"
    filter_suffix       = ".jar"
    lambda_function_arn = aws_lambda_function.terminate_supplier_zulu.arn
  }
  lambda_function {
    events = [
      "s3:ObjectCreated:*",
    ]
    filter_prefix       = "tango/"
    filter_suffix       = ".jar"
    id                  = "update-agencias-tango"
    lambda_function_arn = "arn:aws:lambda:us-east-1:393346304284:function:Terminate-Supplier-tango"
  }
  lambda_function {
    events = [
      "s3:ObjectCreated:*",
    ]
    filter_prefix       = "juliet/"
    filter_suffix       = ".jar"
    id                  = "update-agencias-juliet"
    lambda_function_arn = "arn:aws:lambda:us-east-1:393346304284:function:Terminate-Supplier-juliet"
  }
  lambda_function {
    events = [
      "s3:ObjectCreated:*",
    ]
    filter_prefix       = "charlie/"
    filter_suffix       = ".jar"
    id                  = "update-agencias-charlie"
    lambda_function_arn = "arn:aws:lambda:us-east-1:393346304284:function:Terminate-Supplier-charlie"
  }
  lambda_function {
    events = [
      "s3:ObjectCreated:*",
    ]
    filter_prefix       = "delta/"
    filter_suffix       = ".jar"
    id                  = "update-agencias-delta"
    lambda_function_arn = "arn:aws:lambda:us-east-1:393346304284:function:Terminate-Supplier-delta"
  }

  lambda_function {
    events = [
      "s3:ObjectCreated:*",
    ]
    filter_prefix       = "xray/"
    filter_suffix       = ".jar"
    id                  = "update-agencias-xray"
    lambda_function_arn = "arn:aws:lambda:us-east-1:393346304284:function:Terminate-Supplier-xray"
  }
}
