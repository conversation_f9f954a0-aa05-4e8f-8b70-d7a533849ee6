resource "aws_launch_template" "paytrack_gama_web" {
  block_device_mappings {
    device_name = "/dev/xvda"

    ebs {
      delete_on_termination = "true"
      encrypted             = "false"
      iops                  = "0"
      snapshot_id           = "snap-03cb24332efcf16cb"
      volume_size           = "8"
      volume_type           = "gp3"
    }
  }

  default_version         = "4"
  disable_api_termination = "false"
  ebs_optimized           = "false"

  iam_instance_profile {
    arn = "arn:aws:iam::393346304284:instance-profile/EC2_Paytrack_Web"
  }

  image_id      = "ami-015ceed9e4d21cd24"
  instance_type = "t3.medium"
  key_name      = "ZYNA_NV_2"

  monitoring {
    enabled = "false"
  }

  name                   = "paytrack_gama_web"
  user_data              = "IyEvYmluL2Jhc2ggLXhlCnl1bSBpbnN0YWxsIC15IGF3c2xvZ3MKbWtkaXIgL3Vzci9wYXl0cmFjayAtcAphd3MgczMgY3AgczM6Ly9wYXl0cmFjay13ZWIvZ2FtYS9hd3Nsb2dzLmNvbmYgL2V0Yy9hd3Nsb2dzL2F3c2xvZ3MuY29uZgpzeXN0ZW1jdGwgc3RhcnQgYXdzbG9nc2QKYXdzIHMzIGNwIHMzOi8vcGF5dHJhY2std2ViL2dhbWEvd2todG1sdG9pbWFnZSAvdXNyL2Jpbi93a2h0bWx0b2ltYWdlCmF3cyBzMyBjcCBzMzovL3BheXRyYWNrLXdlYi9nYW1hL3draHRtbHRvcGRmIC91c3IvYmluL3draHRtbHRvcGRmCmNobW9kIDc3NyAvdXNyL2Jpbi93ayoKYXdzIHMzIGNwIHMzOi8vcGF5dHJhY2std2ViL2dhbWEvbG9jYWwuY29uZiAvZXRjL2ZvbnRzL2xvY2FsLmNvbmYKYXdzIHMzIGNwIHMzOi8vcGF5dHJhY2std2ViL2dhbWEvUk9PVC5qYXIgL3Vzci9wYXl0cmFjay8KYXdzIHMzIHN5bmMgczM6Ly9wYXl0cmFjay13ZWIvZ2FtYS9yZXBvcnRzIC91c3IvcGF5dHJhY2svcmVwb3J0cwphd3MgczMgY3AgczM6Ly9wYXl0cmFjay13ZWIvZ2FtYS9wYXl0cmFjay5wcm9wZXJ0aWVzIC91c3IvcGF5dHJhY2svCmF3cyBzMyBjcCBzMzovL3BheXRyYWNrLXdlYi9nYW1hL3NldGVudi5zaCAvdXNyL3BheXRyYWNrLwpjaG1vZCA3NzcgL3Vzci9wYXl0cmFjay9zZXRlbnYuc2gKL3Vzci9wYXl0cmFjay9zZXRlbnYuc2g="
  vpc_security_group_ids = ["sg-01fefa2db55768412"]
}

resource "aws_lb_target_group" "gama-web" {
  deregistration_delay = "300"

  health_check {
    enabled             = "true"
    healthy_threshold   = "2"
    interval            = "15"
    matcher             = "200"
    path                = "/api/heartbeat"
    port                = "traffic-port"
    protocol            = "HTTP"
    timeout             = "5"
    unhealthy_threshold = "2"
  }

  load_balancing_algorithm_type = "round_robin"
  name                          = "gama-web"
  port                          = "8080"
  protocol                      = "HTTP"
  protocol_version              = "HTTP1"
  slow_start                    = "0"

  stickiness {
    cookie_duration = "86400"
    enabled         = "false"
    type            = "lb_cookie"
  }

  target_type = "instance"
  vpc_id      = aws_vpc.main.id
}

resource "aws_autoscaling_group" "paytrack_gama_web" {
  availability_zones        = ["us-east-1a"]
  capacity_rebalance        = "true"
  default_cooldown          = "300"
  desired_capacity          = "1"
  health_check_grace_period = "300"
  health_check_type         = "ELB"

  max_instance_lifetime = "0"
  max_size              = "1"
  metrics_granularity   = "1Minute"
  min_size              = "1"

  mixed_instances_policy {
    instances_distribution {
      on_demand_allocation_strategy            = "prioritized"
      on_demand_base_capacity                  = "0"
      on_demand_percentage_above_base_capacity = "0"
      spot_allocation_strategy                 = "price-capacity-optimized"
      spot_instance_pools                      = "0"
    }

    launch_template {
      launch_template_specification {
        launch_template_id   = "lt-05e90d117377ede88"
        launch_template_name = "paytrack_gama_web"
        version              = "$Latest"
      }

      override {
        instance_type = "t3.small"
      }

      override {
        instance_type = "t3a.small"
      }

      override {
        instance_type = "t4g.small"
      }

      override {
        instance_type = "t3.medium"
      }

      override {
        instance_type = "t3a.medium"
      }

      override {
        instance_type = "t4g.medium"
      }
    }
  }

  name                    = "paytrack_gama_web"
  protect_from_scale_in   = "false"
  service_linked_role_arn = "arn:aws:iam::393346304284:role/aws-service-role/autoscaling.amazonaws.com/AWSServiceRoleForAutoScaling"

  tag {
    key                 = "Name"
    propagate_at_launch = "true"
    value               = "paytrack_gama_web"
  }

  target_group_arns = ["arn:aws:elasticloadbalancing:us-east-1:393346304284:targetgroup/gama-web/e076d6eb80cd2f9f"]

  lifecycle {
    ignore_changes = [desired_capacity, max_size]
  }
}

resource "aws_lb_listener_rule" "gama_web" {
  action {
    order            = "1"
    target_group_arn = "arn:aws:elasticloadbalancing:us-east-1:393346304284:targetgroup/gama-web/e076d6eb80cd2f9f"
    type             = "forward"
  }


  condition {
    host_header {
      values = ["gama.paytrack.com.br"]
    }
  }

  listener_arn = aws_lb_listener.fullstack_443.arn
  priority     = "51"
}