resource "aws_launch_template" "paytrack_gama_agencias" {
  block_device_mappings {
    device_name = "/dev/xvda"

    ebs {
      delete_on_termination = "true"
      encrypted             = "false"
      iops                  = "0"
      snapshot_id           = "snap-03cb24332efcf16cb"
      volume_size           = "8"
      volume_type           = "gp3"
    }
  }

  default_version         = "2"
  disable_api_termination = "false"
  ebs_optimized           = "false"

  iam_instance_profile {
    name = "CloudWatchCustomMetrics"
  }

  image_id      = "ami-015ceed9e4d21cd24"
  instance_type = "t3.small"
  key_name      = "ZYNA_NV_2"

  monitoring {
    enabled = "false"
  }

  name                   = "paytrack_gama_agencias"
  user_data              = "IyEvYmluL2Jhc2ggLXhlCnl1bSBpbnN0YWxsIC15IGF3c2xvZ3MKbWtkaXIgL3Vzci9wYXl0cmFjayAtcAphd3MgczMgY3AgczM6Ly9wYXl0cmFjay1hZ2VuY2lhcy9nYW1hL2F3c2xvZ3MuY29uZiAvZXRjL2F3c2xvZ3MvYXdzbG9ncy5jb25mCnN5c3RlbWN0bCBzdGFydCBhd3Nsb2dzZAphd3MgczMgY3AgczM6Ly9wYXl0cmFjay1hZ2VuY2lhcy9nYW1hL3BheXRyYWNrLWFnZW5jeS1iYWNrZW5kLmphciAvdXNyL3BheXRyYWNrLwphd3MgczMgY3AgczM6Ly9wYXl0cmFjay1hZ2VuY2lhcy9nYW1hL2FwcGxpY2F0aW9uLWdhbWEucHJvcGVydGllcyAvdXNyL3BheXRyYWNrLwphd3MgczMgY3AgczM6Ly9wYXl0cmFjay1hZ2VuY2lhcy9nYW1hL3NldGVudi5zaCAvdXNyL3BheXRyYWNrLwpjaG1vZCA3NzcgL3Vzci9wYXl0cmFjay9zZXRlbnYuc2gKY2QgL3Vzci9wYXl0cmFjawouL3NldGVudi5zaA=="
  vpc_security_group_ids = ["sg-0d821b46b061be8a1"]
}

resource "aws_lb_target_group" "gama-agencias-api" {
  deregistration_delay = "300"

  health_check {
    enabled             = "true"
    healthy_threshold   = "2"
    interval            = "15"
    matcher             = "200-499"
    path                = "/healthcheck"
    port                = "traffic-port"
    protocol            = "HTTP"
    timeout             = "5"
    unhealthy_threshold = "2"
  }

  load_balancing_algorithm_type = "round_robin"
  name                          = "gama-agencias-api"
  port                          = "8080"
  protocol                      = "HTTP"
  protocol_version              = "HTTP1"
  slow_start                    = "0"

  stickiness {
    cookie_duration = "86400"
    enabled         = "false"
    type            = "lb_cookie"
  }

  target_type = "instance"
  vpc_id      = aws_vpc.main.id
}

resource "aws_autoscaling_group" "paytrack_gama_agencias" {
  availability_zones        = ["us-east-1a"]
  capacity_rebalance        = "true"
  default_cooldown          = "300"
  desired_capacity          = "1"
  force_delete              = "false"
  health_check_grace_period = "300"
  health_check_type         = "ELB"

  max_instance_lifetime = "0"
  max_size              = "1"
  metrics_granularity   = "1Minute"
  min_size              = "1"

  mixed_instances_policy {
    instances_distribution {
      on_demand_allocation_strategy            = "prioritized"
      on_demand_base_capacity                  = "0"
      on_demand_percentage_above_base_capacity = "0"
      spot_allocation_strategy                 = "price-capacity-optimized"
      spot_instance_pools                      = "0"
    }

    launch_template {
      launch_template_specification {
        launch_template_id   = "lt-078b9a1c1b53d1bb9"
        launch_template_name = "paytrack_gama_agencias"
        version              = "3"
      }

      override {
        instance_type = "t3.small"
      }

      override {
        instance_type = "t3a.small"
      }

      override {
        instance_type = "t4g.small"
      }

      override {
        instance_type = "t4g.medium"
      }

      override {
        instance_type = "t3.medium"
      }

      override {
        instance_type = "t3a.medium"
      }

    }
  }

  name                    = "paytrack_gama_agencias"
  protect_from_scale_in   = "false"
  service_linked_role_arn = "arn:aws:iam::393346304284:role/aws-service-role/autoscaling.amazonaws.com/AWSServiceRoleForAutoScaling"

  tag {
    key                 = "Name"
    propagate_at_launch = "true"
    value               = "paytrack_gama_agencias"
  }

  target_group_arns = ["arn:aws:elasticloadbalancing:us-east-1:393346304284:targetgroup/gama-agencias-api/342ff1200bdafa13"]

  wait_for_capacity_timeout = "10m"

  lifecycle {
    ignore_changes = [desired_capacity, max_size]
  }
}

resource "aws_lb_listener_rule" "gama_agency" {
  action {
    order            = "1"
    target_group_arn = "arn:aws:elasticloadbalancing:us-east-1:393346304284:targetgroup/gama-agencias-api/342ff1200bdafa13"
    type             = "forward"
  }


  condition {
    host_header {
      values = ["supplier-gama-api.paytrack.com.br"]
    }
  }

  listener_arn = aws_lb_listener.fullstack_443.arn
  priority     = "42"
}