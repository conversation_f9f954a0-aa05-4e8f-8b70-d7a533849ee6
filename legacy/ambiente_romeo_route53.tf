resource "aws_route53_record" "romeo" {
  name    = "romeo.paytrack.com.br"
  type    = "A"
  zone_id = "ZOQML0WI6K6IU"

  alias {
    name                   = aws_cloudfront_distribution.romeo.domain_name
    zone_id                = "Z2FDTNDATAQYW2"
    evaluate_target_health = false
  }
}

resource "aws_route53_record" "romeo_login" {
  name    = "login-romeo.paytrack.com.br"
  type    = "A"
  zone_id = "ZOQML0WI6K6IU"

  alias {
    name                   = aws_cloudfront_distribution.romeo_login.domain_name
    zone_id                = "Z2FDTNDATAQYW2"
    evaluate_target_health = false
  }
}

resource "aws_route53_record" "romeo_supplier_api" {
  name    = "supplier-romeo-api.paytrack.com.br"
  type    = "A"
  zone_id = "ZOQML0WI6K6IU"

  alias {
    name                   = "dualstack.fullstack-**********.us-east-1.elb.amazonaws.com"
    zone_id                = "Z35SXDOTRQ7X7K"
    evaluate_target_health = true
  }
}

resource "aws_route53_record" "romeo_supplier" {
  name    = "supplier-romeo.paytrack.com.br"
  type    = "A"
  zone_id = "ZOQML0WI6K6IU"

  alias {
    name                   = "d35xd4xdyum77z.cloudfront.net"
    zone_id                = "Z2FDTNDATAQYW2"
    evaluate_target_health = false
  }
}
