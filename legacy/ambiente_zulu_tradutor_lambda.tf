resource "aws_lambda_permission" "allow_bucket_terminate_tradutor_zulu" {
  action         = "lambda:InvokeFunction"
  function_name  = aws_lambda_function.terminate_tradutor_zulu.arn
  principal      = "s3.amazonaws.com"
  source_account = "************"
  source_arn     = "arn:aws:s3:::paytrack-tradutor"
}

resource "aws_lambda_function" "terminate_tradutor_zulu" {
  architectures = [
    "x86_64",
  ]
  function_name                  = "Terminate-Tradutor-Zulu"
  handler                        = "br.com.paytrack.updater.UpdaterService::handle"
  memory_size                    = 256
  package_type                   = "Zip"
  reserved_concurrent_executions = -1
  role                           = aws_iam_role.ec2-terminate.arn
  runtime                        = "java11"
  timeout                        = 600
  s3_key                         = data.aws_s3_object.paytrack_updater_jar.key
  s3_bucket                      = data.aws_s3_object.paytrack_updater_jar.bucket
  s3_object_version              = data.aws_s3_object.paytrack_updater_jar.version_id

  publish = true

  environment {
    variables = {
      "ASG_NAME"      = aws_autoscaling_group.paytrack_zulu_tradutor.name
      "MAX_ATTEMPTS"  = "30"
      "MAX_INSTANCES" = "8"
      "SLEEP_TIME"    = "20000"
      "WEBHOOK_URL"   = "https://discordapp.com/api/webhooks/715291188915208283/J3hOxrFMSZ843uajiCIZDEgwNJil3f9TDsgIu3BcA8gkTxHtMLR8xi26Yx6glnOAKc-6"
    }
  }
}
