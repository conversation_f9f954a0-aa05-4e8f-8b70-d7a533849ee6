# module "tango_agency" {
#   source               = "./modules/agency"
#   environment_name     = "tango"
#   aws_vpc_id           = aws_vpc.main.id
#   lb_listener_arn      = aws_lb_listener.fullstack_443.arn
#   lb_listener_priority = "1008"
#   s3_paytrack_updater_jar = {
#     key        = data.aws_s3_object.paytrack_updater_jar.key
#     bucket     = data.aws_s3_object.paytrack_updater_jar.bucket
#     version_id = data.aws_s3_object.paytrack_updater_jar.version_id
#   }
# }

# module "tango_tradutor" {
#   source               = "./modules/tradutor"
#   environment_name     = "tango"
#   aws_vpc_id           = aws_vpc.main.id
#   lb_listener_arn      = aws_lb_listener.fullstack_443.arn
#   lb_listener_priority = "1009"
#   iam_role_arn_to_ec2_terminate = aws_iam_role.ec2-terminate.arn
#   s3_paytrack_updater_jar = {
#     key        = data.aws_s3_object.paytrack_updater_jar.key
#     bucket     = data.aws_s3_object.paytrack_updater_jar.bucket
#     version_id = data.aws_s3_object.paytrack_updater_jar.version_id
#   }
# }

# module "environment_tango" {
#   source                        = "./ambientes/modules/environment"
#   environment_name              = "tango"
#   iam_user_arn_to_sqs           = aws_iam_user.paytrack_sqs.arn
#   iam_role_arn_to_ec2_terminate = aws_iam_role.ec2-terminate.arn
#   aws_vpc_id                    = aws_vpc.main.id
#   lb_listener_arn               = aws_lb_listener.fullstack_443.arn
#   lb_listener_agency_priority   = "1008"
#   lb_listener_tradutor_priority = "1009"
#   lb_listener_web_priority      = "1010"
#   s3_paytrack_updater_jar = {
#     key        = data.aws_s3_object.paytrack_updater_jar.key
#     bucket     = data.aws_s3_object.paytrack_updater_jar.bucket
#     version_id = data.aws_s3_object.paytrack_updater_jar.version_id
#   }
# }
