resource "aws_iam_role" "terminate_web_gama_role" {
  assume_role_policy = jsonencode(
    {
      Statement = [
        {
          Action = "sts:AssumeRole"
          Effect = "Allow"
          Principal = {
            Service = "lambda.amazonaws.com"
          }
        },
      ]
      Version = "2012-10-17"
    }
  )
  managed_policy_arns = [
    "arn:aws:iam::393346304284:policy/service-role/AWSLambdaBasicExecutionRole-9ac7c451-8b87-4a86-bfdc-26a45b5cc318",
  ]
  max_session_duration = 3600
  name                 = "Terminate-Web-Gama-role-16ifwi2t"
  path                 = "/service-role/"
}

resource "aws_lambda_function" "terminate_web_gama" {
  architectures = [
    "x86_64",
  ]
  function_name     = "Terminate-Web-Gama"
  handler           = "br.com.paytrack.updater.UpdaterService::handle"
  memory_size       = 512
  package_type      = "Zip"
  role              = aws_iam_role.terminate_web_gama_role.arn
  runtime           = "java11"
  timeout           = 600
  s3_key            = data.aws_s3_object.paytrack_updater_jar.key
  s3_bucket         = data.aws_s3_object.paytrack_updater_jar.bucket
  s3_object_version = data.aws_s3_object.paytrack_updater_jar.version_id

  environment {
    variables = {
      "ASG_NAME"      = aws_autoscaling_group.paytrack_gama_web.name
      "MAX_ATTEMPTS"  = "30"
      "MAX_INSTANCES" = "8"
      "SLEEP_TIME"    = "20000"
      "WEBHOOK_URL"   = "https://discordapp.com/api/webhooks/715291188915208283/J3hOxrFMSZ843uajiCIZDEgwNJil3f9TDsgIu3BcA8gkTxHtMLR8xi26Yx6glnOAKc-6"
    }
  }
}
